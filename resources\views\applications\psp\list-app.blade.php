<?php
// phpinfo();
?>
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ (app()->getLocale()=='ar' ? 'rtl' : 'ltr') }}">

<head>
    <meta charset="UTF-8">
    @if(App::getLocale()=='en')
    <title lang="en">Osool </title>
    @else (App::getLocale()=='ar')
    <title lang="ar">أصول </title>
    @endif
    <meta name="description" lang="en"
        content="Osool is your technical partner in following up on maintenance contracts and managing the operational system. Osool enables facilities and properties management teams to manage maintenance and property operations, link with operational service providers, follow up on maintenance contract work in one platform, and serve the final beneficiary of the property.">
    <meta name="description" lang="ar"
        content="أصول هو شريكك التقني في متابعة عقود الصيانة وإدارة المنظومة التشغيلية. يمكن أصول فرق إدارة المرافق والأملاك من إدارة عمليات الصيانة والعقار والربط مع مقدمي الخدمات التشغيلية ومتابعة أعمال عقود الصيانة في منصة واحدة وخدمة المستفيد النهائي من العقار.">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta property="og:image" content="{{ asset('img/img-share.jpg') }}">
    <meta property="og:image:secure_url" content="{{ asset('img/img-share.jpg') }}">
    <meta property="og:image:type" content="image/jpg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <link rel="shortcut icon" href="{{ asset('home/image/favicon/favicon-32x32.png') }}" type="image">

    <!-- Bootstrap, fonts & icons  -->
    <link rel="stylesheet" href="{{ asset('new_theme/css/new_font.css')}}">


    @if(App::getLocale()=='en')
    <link rel="stylesheet" href="{{ asset('home/css/bootstrap.css') }}">
    @else (App::getLocale()=='ar')
    <link rel="stylesheet" href="{{ asset('home/css/bootstrap-rtl.css') }}">
    @endif
    @include('layouts.partials._styles')
    @if(App::getLocale()=='en')
    <link rel="stylesheet" href="{{ asset('home/css/main.css') }}">
    @else (App::getLocale()=='ar')
    <link rel="stylesheet" href="{{ asset('home/css/main-rtl.css') }}">
    @endif

    <link rel="stylesheet" href="{{ asset('home/fonts/icon-font/css/style.css') }}">
    <link rel="stylesheet" href="{{ asset('home/fonts/typography-font/typo.css') }}">
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.4/css/all.css"
        integrity="sha384-DyZ88mC6Up2uqS4h/KRgHuoeGwBcD4Ng9SiP4dIRy0EXTlnuz47vAwmeGwVChigm" crossorigin="anonymous">
    <link href="https://fonts.googleapis.com/css2?family=Karla:wght@300;400;500;600;700;800&display=swap"
        rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Gothic+A1:wght@400;500;700;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Work+Sans:wght@400;500;600;700;800;900&display=swap"
        rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Rubik:wght@400;500;600;700;800;900&display=swap"
        rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700;800;900&display=swap" rel="stylesheet">
    <!-- Plugin'stylesheets  -->
    <link rel="stylesheet" href="{{ asset('home/plugins/aos/aos.min.css') }}">
    <link rel="stylesheet" href="{{ asset('home/plugins/fancybox/jquery.fancybox.min.css') }}">
    <link rel="stylesheet" href="{{ asset('home/plugins/nice-select/nice-select.min.css') }}">
    <link rel="stylesheet" href="{{ asset('home/plugins/slick/slick.min.css') }}">
    <!-- Vendor stylesheets  -->
    <link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">

    <link rel="stylesheet" href="{{ asset('css/scss/new-style.css') }}">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.15.10/dist/sweetalert2.min.css">

    @yield("cssCode")
    <style type="text/css">
        .page-wrapper {
            padding-top: 150px;
        }
        label {
            font-size: 16px;
            color: #696F79;
            font-weight: 500;
        }
    </style>
    <style type="text/css">
        .page-wrapper {
            padding-top: 150px;
        }
        @media only screen and (max-width:576px){
            .page-wrapper {
                padding-top: 100px;
            }
        }

        .nav-link {
            color: #6c757d;
            text-decoration: none;
            padding: 10px 15px;
            transition: color 0.3s ease;
        }

        .nav-link.active p,
        .nav-link.active .nav-icon,
        .nav-link.active .chevron i {
            color: var(--primary) !important;
        }

        .nav-icon,
        .chevron i {
            color: #6c757d;
            transition: color 0.3s ease;
        }

        .account-profile #remove_pro_pic {
            position: absolute;
            color: #fff;
            border: 0px;
            bottom: 0;
            right: 0;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            box-shadow: 0px 1px 4px 0px #1A0F011F;
            background: #ffffffff;
        }

        .account-profile .pro_img_wrapper {
            position: relative;
            width: 60px;
            height: 60px;
        }

        .logout {
            background: #fff;
            border: 0
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .service-item select,
        .service-item input {
            flex: 1;
        }

        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: #fff !important;
            border: 1px solid #e3e6ef;
            border-radius: 4px;
            display: flex;
            align-items: center;
            padding: 10px 20px;
            color: var(--primary);
            font-weight: 500;
            padding: 8px 12px;
            border-radius: 5px;
            width: 100%;
        }

        .file-item button {
            border: none;
            background: transparent;
            color: red;
            cursor: pointer;
            font-size: 18px;
        }

        .hidden {
            display: none;
        }

        .file-input {
            opacity: 0;
            position: absolute;
            width: 0;
            height: 0;
            display: none;
        }

        .file-name {
            flex-grow: 1;
            color: var(--primary);
        }

        .remove-file {
            border: none;
            background: transparent;
            color: red;
            cursor: pointer;
            font-size: 18px;
        }


        .secondary-btn {
            background: #8692A6;
            color: #fff;
        }

        .secondary-btn:hover {
            background: #8692A699 !important;
        }

        .table-responsive {
            border-radius: 8px;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .table thead {
            background-color: #F8F9FB;
            border: 1px solid #F1F2F6;
            border-radius: 5px
        }

        .table th {
            color: #9199B7;
            font-weight: 600;
            border-bottom: 1px solid #e5e7eb;
            font-size: 16px;
        }

        .table td {
            font-weight: 600;
            font-size: 16px;
            vertical-align: middle;
        }

        .status-badge {
            background-color: #E6F1F6;
            color: #016395;
            padding: 6px 12px;
            font-weight: 500;
            border-radius: 999px;
            font-size: 14px;
            display: inline-block;
        }

        /*@media (max-width: 768px) {
            .table thead {
                display: none;
            }

            .table tr {
                display: block;
                margin-bottom: 1rem;
                border-bottom: 2px solid #e5e7eb;
            }

            .table td {
                display: block;
                text-align: right;
                padding: 12px;
            }

            .table td::before {
                content: attr(data-label);
                float: left;
                font-weight: 500;
                color: #6b7280;
            }
        }*/
    </style>

    <!-- Custom stylesheet -->
    <!-- Start of  Zendesk Widget script -->
    <!-- <script id="ze-snippet" src="https://static.zdassets.com/ekr/snippet.js?key=5dcd3800-b341-400a-b410-ac5c928e42f5"> </script> -->
    <!-- End of  Zendesk Widget script -->
</head>
@php
$lang_path=resource_path('lang/'.App::getLocale());
$translations=collect(File::allFiles($lang_path))->flatMap(function ($file)use($lang_path) {
return [
($translation = $file->getBasename('.php')) => trans($translation),
];
})->toJson();
@endphp
<script type="text/javascript">
    window.baseUrl = "{{URL::to('/')}}";
    window.current_locale = "{{App::getLocale()}}";
    window.translations = {!! $translations!!};
</script>

<body data-theme-mode-panel-active data-theme="light" class="ltr">
    <div class="site-wrapper overflow-hidden position-relative">
        <!--Site Header Area -->
        <header
            class="site-header site-header--menu-right landing-1-menu site-header--absolute site-header--sticky bg-white shadow-sm">
            <div class="container-fluid">
                <nav class="navbar site-navbar">
                    <!-- Brand Logo-->
                    <div class="brand-logo">
                        <a href="{{ url('/') }}">
                            <!-- light version logo (logo must be black)-->
                            <img src="{{ asset('home/image/home/<USER>') }}" alt="" class="light-version-logo">
                            <!-- Dark version logo (logo must be White)-->
                            <img src="{{ asset('home/image/home/<USER>') }}" alt="" class="dark-version-logo">
                        </a>
                    </div>
                    <div class="menu-block-wrapper">
                        <div class="menu-overlay"></div>
                        <nav class="menu-block" id="append-menu-header">
                            <div class="mobile-menu-head">
                                <img src="{{ asset('home/image/home/<USER>') }}" alt="" class="">
                                <div class="go-back">
                                    <i class="fa fa-angle-left"></i>
                                </div>
                                <div class="current-menu-title"></div>
                                <div class="mobile-menu-close">&times;</div>
                            </div>
                            <ul class="site-menu-main">
                                <li class="nav-item">
                                    <a href="{{url('/')}}#menu1" class="nav-link-item">
                                        {{__('landing_page.menu.wahts_osool')}}</a>
                                </li>
                                <li class="nav-item">
                                    <a href="{{url('/')}}#menu2"
                                        class="nav-link-item">{{__('landing_page.menu.osool_advantage')}} </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{{url('/')}}#menu3" class="nav-link-item">
                                        {{__('landing_page.menu.beneficiaries_of_osool')}}</a>
                                </li>
                                <li class="nav-item">
                                    <a href="{{url('/')}}#menu4" class="nav-link-item">
                                        {{__('landing_page.menu.contact_us')}}</a>
                                </li>

                                @if(\Illuminate\Support\Facades\Auth::check())
                                @if(\Illuminate\Support\Facades\Auth::user()->isServiceProviderAdmin() &&
                                \Illuminate\Support\Facades\Auth::user()->status != 1)
                                @include('layouts.partials.psp-head-menu')
                                @else
                                <li class="d-flex align-items-center">
                                    <span class="nav-link-item no-hover">
                                        <a class="btn bg-db text-white focus-reset lan-btn"
                                            href="{{ route('admin.dashboard') }}">
                                            {{__('dashboard.blank_dashboard.dashboard')}}
                                        </a>
                                    </span>
                                </li>
                                @endif
                                @else
                                <li class="nav-item mt-sm-0 mt-3 mt-sm-0">
                                    <span class="nav-link-item no-hover pr-0">
                                        <a href="{{ route('psp-registration.index') }}"
                                            class="btn btn-border-lb btn-hover-slide position-relative overflow-hidden"><span
                                                class="rounded d-block position-relative"> Sign Up</span></a>
                                    </span>
                                </li>
                                <li class="nav-item mt-sm-0 mt-3 mt-sm-0">
                                    <span class="nav-link-item no-hover pr-0">
                                        <a href="javascript:void(0);"
                                            class="btn btn-border-lb btn-hover-slide position-relative overflow-hidden"
                                            data-bs-toggle="modal" data-bs-target="#osool-popup"><span
                                                class="rounded d-block position-relative">
                                                {{__('landing_page.menu.get_started')}}</span></a>
                                    </span>
                                </li>
                                <li class="d-flex align-items-center">
                                    <span class="nav-link-item no-hover">
                                        <a class="btn bg-db text-white focus-reset lan-btn" href="{{ url('login') }}">
                                            {{__('landing_page.menu.login')}}
                                        </a>
                                    </span>
                                </li>
                                @endif

                                <li class="nav-item">
                                    @if (App::getLocale()=='en')
                                    <a href="{{route('changeLanguage'," ar")}}" class="nav-link-item lang-btn"><span
                                            class="rounded d-block">{{__('landing_page.menu.en_ar')}}</span></a>
                                    @elseif (App::getLocale()=='ar')
                                    <a href="{{route('changeLanguage',"en")}}" class="nav-link-item lang-btn"><span
                                            class="rounded d-block">{{__('landing_page.menu.en_ar')}}</span></a>
                                    @endif

                                </li>
                            </ul>
                        </nav>
                    </div>

                    <!-- mobile menu trigger -->
                    <div class="mobile-menu-trigger">
                        <span></span>
                    </div>
                    <!--/.Mobile Menu Hamburger Ends-->
                </nav>
            </div>
        </header>
        <!-- navbar- -->

        <div class="page-wrapper container container-fluid profile-setting">
            <!-- Content goes here  -->
            <div class="profile-setting">
                <style>
                    .file-input-label {
                        cursor: pointer;
                        border: 1px solid #ddd;
                        padding: 6px 12px;
                        border-radius: 4px;
                        background: #f8f9fa;
                        transition: all 0.3s ease;
                    }

                    .file-input-label:hover {
                        background: #e9ecef;
                    }

                    .file-name {
                        margin-left: 8px;
                        color: #495057;
                    }

                    .upload-container {
                        border: 2px dashed #dee2e6;
                        padding: 1rem;
                        border-radius: 8px;
                        background: #f8f9fa;
                    }
                </style>
                <!-- Success Message -->
                @if (session()->has('success'))
                <div class="alert alert-success">{{ session('success') }}</div>
                @endif
                <div class="container-fluid">
                    <div class="page-title-wrap">
                        <div class="page-title d-flex justify-content-between">
                            <div class="page-title__left">
                                <div
                                    class="d-flex align-items-center user-member__title justify-content-center mr-sm-25">
                                    <h4 class="text-capitalize fw-500 breadcrumb-title">
                                        <a href="{{ url('/') }}"><i class="las la-arrow-left"></i></a>
                                        {{ __('psp_registration.common.nav_menu.profile_management') }}
                                    </h4>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mb-30 mt-15">
                        <div class="row">
                            <div class="col-md-4">
                                <!-- Profile Account -->
                                <div class="card mb-25">
                                    <div class="card-body p-0">
                                        <div class="d-flex align-items-center gap-x-4 p-20">

                                            <img class="ap-img__main rounded-circle wh-60"
                                                src="{{ \Illuminate\Support\Facades\Auth::user()->profile_img ?ImagesUploadHelper::displayImage(\Illuminate\Support\Facades\Auth::user()->profile_img, 'uploads/profile_images') : asset('uploads/profile_images/dummy_profile_image.png') }}"
                                                alt="profile" />

                                            <div class="pl-15">
                                                <h6>{{ \Illuminate\Support\Facades\Auth::user()->name}}</h6>
                                                <p class="m-0">{{ \Illuminate\Support\Facades\Auth::user()->email }}</p>
                                            </div>
                                        </div>
                                        <hr class="w-80 mx-auto m-0 px-2" />
                                        <!-- <div class="ap-nameAddress pb-3">
                                            <h5 class="ap-nameAddress__title">John Doe</h5>
                                        </div> -->
                                    </div>
                                    <div class="ps-tab p-20 pb-25">
                                        <div class="nav flex-column" id="v-pills-tab" role="tablist"
                                            aria-orientation="vertical">
                                            <a class="nav-link d-flex align-items-center justify-content-between"
                                                href="/psp/profile/profile">
                                                <p class="m-0 d-flex align-items-center"><span data-feather="user"
                                                        class="nav-icon"></span>
                                                    {{ __('psp_registration.common.nav_menu.my_profile') }}
                                                </p>
                                                <span class="chevron"><i class="ri-arrow-right-s-line"></i></span>
                                            </a>
                                            <a class="nav-link d-flex align-items-center justify-content-between"
                                                href="/psp/profile/password">
                                                <p class="m-0 d-flex align-items-center"><span data-feather="settings"
                                                        class="nav-icon"></span>
                                                    {{ __('psp_registration.common.nav_menu.password') }}
                                                </p>
                                                <span class="chevron"><i class="ri-arrow-right-s-line"></i></span>
                                            </a>
                                            @if($vendor)
                                            <a class="nav-link active align-items-center justify-content-between"
                                                href="/psp/profile/vendor">
                                                <p class="m-0 d-flex align-items-center">
                                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                        class="nav-icon" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" clip-rule="evenodd"
                                                            d="M16.7274 16.8133L15.2637 15.8556L15.2372 17.4933L16.7274 16.8133ZM21.5175 8.5172L21.9038 7.9248L21.3099 7.53742L20.9235 8.12983L21.5175 8.5172H21.5175ZM3.51789 16.715V17.8359L3.75241 17.6591C3.84199 17.5888 3.97094 17.5844 4.06614 17.6563L4.30446 17.8359V16.715H3.51789ZM4.81971 16.715H6.55657V22.0059H1.26574V16.715H3.00264V18.3522H3.00307C3.00288 18.5583 3.24396 18.6867 3.41524 18.5575L3.91118 18.1837L4.39591 18.5491C4.57844 18.6948 4.81975 18.5599 4.81975 18.3522V16.7151L4.81971 16.715ZM11.7938 7.61162C11.3172 7.13505 10.4986 7.47409 10.4986 8.14811C10.4986 8.82212 11.3172 9.16117 11.7938 8.68459C12.09 8.38834 12.09 7.90787 11.7938 7.61162ZM9.48283 10.9881C10.5346 11.7713 11.98 11.7713 13.0317 10.9881C12.5681 9.83941 11.148 9.42958 10.1448 10.1474C9.85146 10.3574 9.61989 10.6485 9.48283 10.9881ZM13.3601 6.49844C14.4926 7.63089 14.5251 9.45676 13.4334 10.6283C13.1024 9.96025 12.4783 9.47547 11.7445 9.32547C12.5785 8.97991 12.8023 7.89165 12.158 7.24731C11.6605 6.74978 10.854 6.74978 10.3565 7.24731C9.71219 7.89165 9.93597 8.97991 10.77 9.32547C10.0362 9.47547 9.41219 9.9603 9.08116 10.6283C7.98944 9.45672 8.02197 7.63089 9.15443 6.49844C10.3158 5.33706 12.1988 5.33706 13.3602 6.49844H13.3601ZM11.2573 5.1122C13.1842 5.1122 14.7464 6.67445 14.7464 8.60134C14.7464 10.5282 13.1842 12.0905 11.2573 12.0905C9.33039 12.0905 7.76814 10.5282 7.76814 8.60134C7.76814 6.67445 9.33039 5.1122 11.2573 5.1122ZM18.8389 9.99123L20.0321 8.16166L21.8478 9.34576L19.5555 12.8607L17.7398 11.6766L18.816 10.0263C18.8246 10.0153 18.8322 10.0036 18.8389 9.99123H18.8389ZM18.8698 14.8522V20.7911C18.8698 20.9334 18.7545 21.0488 18.6122 21.0488H7.07182V22.2635C7.07182 22.4057 6.95646 22.5211 6.81419 22.5211H1.00811C0.865848 22.5211 0.750488 22.4057 0.750488 22.2635V16.4574C0.750488 16.3151 0.865848 16.1998 1.00811 16.1998H3.64469V5.25883C3.64474 5.19292 3.66986 5.12697 3.72016 5.07667L7.23855 1.55828C7.28533 1.51028 7.35068 1.48047 7.423 1.48047H18.6122C18.7545 1.48047 18.8698 1.59583 18.8698 1.73809V9.00353L19.7419 7.66633C19.8191 7.54741 19.9782 7.51361 20.0972 7.59086L20.4933 7.84923L21.0197 7.04214C21.0969 6.92322 21.256 6.88942 21.3749 6.96667L22.3991 7.63459C22.518 7.71184 22.5518 7.87089 22.4746 7.98986L21.9477 8.79775L22.8551 9.38955C23.2557 9.65078 23.3701 10.19 23.1087 10.5908L21.8016 12.5952C21.7243 12.7141 21.5653 12.7479 21.4463 12.6707C21.3274 12.5934 21.2936 12.4344 21.3708 12.3154L22.678 10.311C22.7839 10.1486 22.7381 9.92631 22.5753 9.82023L22.278 9.62626L18.8698 14.8522ZM18.3856 14.6546L17.1858 16.4943L15.3701 15.3101L17.4592 12.1068L19.2749 13.2909L18.4083 14.6197C18.3998 14.6306 18.3922 14.6423 18.3856 14.6545V14.6546ZM4.52421 5.00125H7.16547V2.35998L4.52421 5.00125ZM7.68072 1.99577H18.3546V9.79366L14.7993 15.2452C14.7702 15.29 14.7569 15.3405 14.758 15.3902L14.7175 17.8853C14.7119 18.0816 14.91 18.2071 15.0807 18.1291L17.3481 17.0944C17.3979 17.0759 17.4424 17.0419 17.4737 16.9941L18.3546 15.6423V20.5335H7.07182V16.4574C7.07182 16.3151 6.95646 16.1997 6.81419 16.1997H4.15994V5.5165H7.42305C7.56532 5.5165 7.68068 5.40114 7.68068 5.25887V1.99577H7.68072ZM7.72868 17.8614H14.0898C14.2321 17.8614 14.3474 17.746 14.3474 17.6038C14.3474 17.4615 14.2321 17.3462 14.0898 17.3462H7.72868C7.58641 17.3462 7.47105 17.4615 7.47105 17.6038C7.47105 17.746 7.58641 17.8614 7.72868 17.8614ZM6.2748 15.1697H6.18311C6.04085 15.1697 5.92549 15.2851 5.92549 15.4273C5.92549 15.5696 6.04085 15.685 6.18311 15.685H6.2748C6.41707 15.685 6.53243 15.5696 6.53243 15.4273C6.53243 15.2851 6.41707 15.1697 6.2748 15.1697ZM7.72868 15.685H14.1691C14.3114 15.685 14.4267 15.5696 14.4267 15.4273C14.4267 15.2851 14.3114 15.1697 14.1691 15.1697H7.72868C7.58641 15.1697 7.47105 15.2851 7.47105 15.4273C7.47105 15.5696 7.58641 15.685 7.72868 15.685ZM6.2748 12.9933H6.18311C6.04085 12.9933 5.92549 13.1087 5.92549 13.2509C5.92549 13.3932 6.04085 13.5085 6.18311 13.5085H6.2748C6.41707 13.5085 6.53243 13.3932 6.53243 13.2509C6.53243 13.1087 6.41707 12.9933 6.2748 12.9933ZM7.72868 13.5085H15.3593C15.5015 13.5085 15.6169 13.3932 15.6169 13.2509C15.6169 13.1087 15.5015 12.9933 15.3593 12.9933H7.72868C7.58641 12.9933 7.47105 13.1087 7.47105 13.2509C7.47105 13.3932 7.58641 13.5085 7.72868 13.5085Z"
                                                            fill="#696F79" stroke="#696F79" stroke-width="0.4" />
                                                    </svg>
                                                    {{ __('psp_registration.common.nav_menu.application_tracking') }}
                                                </p>
                                                <span class="chevron"><i class="ri-arrow-right-s-line"></i></span>
                                            </a>
                                            @else
                                            <a class="nav-link d-flex align-items-center justify-content-between"
                                                href="/psp/profile/vendor">
                                                <p class="m-0 d-flex align-items-center">
                                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                        class="nav-icon" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" clip-rule="evenodd"
                                                            d="M16.7274 16.8133L15.2637 15.8556L15.2372 17.4933L16.7274 16.8133ZM21.5175 8.5172L21.9038 7.9248L21.3099 7.53742L20.9235 8.12983L21.5175 8.5172H21.5175ZM3.51789 16.715V17.8359L3.75241 17.6591C3.84199 17.5888 3.97094 17.5844 4.06614 17.6563L4.30446 17.8359V16.715H3.51789ZM4.81971 16.715H6.55657V22.0059H1.26574V16.715H3.00264V18.3522H3.00307C3.00288 18.5583 3.24396 18.6867 3.41524 18.5575L3.91118 18.1837L4.39591 18.5491C4.57844 18.6948 4.81975 18.5599 4.81975 18.3522V16.7151L4.81971 16.715ZM11.7938 7.61162C11.3172 7.13505 10.4986 7.47409 10.4986 8.14811C10.4986 8.82212 11.3172 9.16117 11.7938 8.68459C12.09 8.38834 12.09 7.90787 11.7938 7.61162ZM9.48283 10.9881C10.5346 11.7713 11.98 11.7713 13.0317 10.9881C12.5681 9.83941 11.148 9.42958 10.1448 10.1474C9.85146 10.3574 9.61989 10.6485 9.48283 10.9881ZM13.3601 6.49844C14.4926 7.63089 14.5251 9.45676 13.4334 10.6283C13.1024 9.96025 12.4783 9.47547 11.7445 9.32547C12.5785 8.97991 12.8023 7.89165 12.158 7.24731C11.6605 6.74978 10.854 6.74978 10.3565 7.24731C9.71219 7.89165 9.93597 8.97991 10.77 9.32547C10.0362 9.47547 9.41219 9.9603 9.08116 10.6283C7.98944 9.45672 8.02197 7.63089 9.15443 6.49844C10.3158 5.33706 12.1988 5.33706 13.3602 6.49844H13.3601ZM11.2573 5.1122C13.1842 5.1122 14.7464 6.67445 14.7464 8.60134C14.7464 10.5282 13.1842 12.0905 11.2573 12.0905C9.33039 12.0905 7.76814 10.5282 7.76814 8.60134C7.76814 6.67445 9.33039 5.1122 11.2573 5.1122ZM18.8389 9.99123L20.0321 8.16166L21.8478 9.34576L19.5555 12.8607L17.7398 11.6766L18.816 10.0263C18.8246 10.0153 18.8322 10.0036 18.8389 9.99123H18.8389ZM18.8698 14.8522V20.7911C18.8698 20.9334 18.7545 21.0488 18.6122 21.0488H7.07182V22.2635C7.07182 22.4057 6.95646 22.5211 6.81419 22.5211H1.00811C0.865848 22.5211 0.750488 22.4057 0.750488 22.2635V16.4574C0.750488 16.3151 0.865848 16.1998 1.00811 16.1998H3.64469V5.25883C3.64474 5.19292 3.66986 5.12697 3.72016 5.07667L7.23855 1.55828C7.28533 1.51028 7.35068 1.48047 7.423 1.48047H18.6122C18.7545 1.48047 18.8698 1.59583 18.8698 1.73809V9.00353L19.7419 7.66633C19.8191 7.54741 19.9782 7.51361 20.0972 7.59086L20.4933 7.84923L21.0197 7.04214C21.0969 6.92322 21.256 6.88942 21.3749 6.96667L22.3991 7.63459C22.518 7.71184 22.5518 7.87089 22.4746 7.98986L21.9477 8.79775L22.8551 9.38955C23.2557 9.65078 23.3701 10.19 23.1087 10.5908L21.8016 12.5952C21.7243 12.7141 21.5653 12.7479 21.4463 12.6707C21.3274 12.5934 21.2936 12.4344 21.3708 12.3154L22.678 10.311C22.7839 10.1486 22.7381 9.92631 22.5753 9.82023L22.278 9.62626L18.8698 14.8522ZM18.3856 14.6546L17.1858 16.4943L15.3701 15.3101L17.4592 12.1068L19.2749 13.2909L18.4083 14.6197C18.3998 14.6306 18.3922 14.6423 18.3856 14.6545V14.6546ZM4.52421 5.00125H7.16547V2.35998L4.52421 5.00125ZM7.68072 1.99577H18.3546V9.79366L14.7993 15.2452C14.7702 15.29 14.7569 15.3405 14.758 15.3902L14.7175 17.8853C14.7119 18.0816 14.91 18.2071 15.0807 18.1291L17.3481 17.0944C17.3979 17.0759 17.4424 17.0419 17.4737 16.9941L18.3546 15.6423V20.5335H7.07182V16.4574C7.07182 16.3151 6.95646 16.1997 6.81419 16.1997H4.15994V5.5165H7.42305C7.56532 5.5165 7.68068 5.40114 7.68068 5.25887V1.99577H7.68072ZM7.72868 17.8614H14.0898C14.2321 17.8614 14.3474 17.746 14.3474 17.6038C14.3474 17.4615 14.2321 17.3462 14.0898 17.3462H7.72868C7.58641 17.3462 7.47105 17.4615 7.47105 17.6038C7.47105 17.746 7.58641 17.8614 7.72868 17.8614ZM6.2748 15.1697H6.18311C6.04085 15.1697 5.92549 15.2851 5.92549 15.4273C5.92549 15.5696 6.04085 15.685 6.18311 15.685H6.2748C6.41707 15.685 6.53243 15.5696 6.53243 15.4273C6.53243 15.2851 6.41707 15.1697 6.2748 15.1697ZM7.72868 15.685H14.1691C14.3114 15.685 14.4267 15.5696 14.4267 15.4273C14.4267 15.2851 14.3114 15.1697 14.1691 15.1697H7.72868C7.58641 15.1697 7.47105 15.2851 7.47105 15.4273C7.47105 15.5696 7.58641 15.685 7.72868 15.685ZM6.2748 12.9933H6.18311C6.04085 12.9933 5.92549 13.1087 5.92549 13.2509C5.92549 13.3932 6.04085 13.5085 6.18311 13.5085H6.2748C6.41707 13.5085 6.53243 13.3932 6.53243 13.2509C6.53243 13.1087 6.41707 12.9933 6.2748 12.9933ZM7.72868 13.5085H15.3593C15.5015 13.5085 15.6169 13.3932 15.6169 13.2509C15.6169 13.1087 15.5015 12.9933 15.3593 12.9933H7.72868C7.58641 12.9933 7.47105 13.1087 7.47105 13.2509C7.47105 13.3932 7.58641 13.5085 7.72868 13.5085Z"
                                                            fill="#696F79" stroke="#696F79" stroke-width="0.4" />
                                                    </svg>
                                                    {{ __('psp_registration.common.nav_menu.register_as_vendor') }}
                                                </p>
                                                <span class="chevron"><i class="ri-arrow-right-s-line"></i></span>
                                            </a>
                                            @endif
                                            <button onclick="pspLogout()"
                                                class="logout nav-link d-flex align-items-center justify-content-between">
                                                <p class="m-0 d-flex align-items-center">
                                                    <svg width="22" height="20" class="nav-icon" viewBox="0 0 22 20"
                                                        fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path
                                                            d="M17 6L21 10M21 10L17 14M21 10H8M14 2.20404C12.7252 1.43827 11.2452 1 9.66667 1C4.8802 1 1 5.02944 1 10C1 14.9706 4.8802 19 9.66667 19C11.2452 19 12.7252 18.5617 14 17.796"
                                                            stroke="#696F79" stroke-width="1.5" stroke-linecap="round"
                                                            stroke-linejoin="round" />
                                                    </svg>
                                                    {{ __('psp_registration.common.nav_menu.logout') }}
                                                </p>
                                            </button>
                                            <form id="psp-logout-form" action="{{ route('psp.logout') }}" method="GET"
                                                style="display: none;">
                                                {{ csrf_token() }}
                                                <input type="text" name="token" value="{{ now() }}">
                                                <button type="submit" class="d-none" id="psp_logout_btn">logout</button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <!-- Profile Account End -->
                            </div>
                            <div class="col-md-8">
                                <div class="mb-50  card">
                                    <div class="card-body crm" id="v-pills-tabContent">
                                        <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                                            <div class="table-responsive">
                                                <table class="table radius-0 mb-0">
                                                    <thead>
                                                        <tr>
                                                            <th><span class="no-wrap">{{ __('psp_registration.dashboard.list_page.table.application_id') }}</span></th>
                                                            <th><span class="no-wrap">{{ __('psp_registration.dashboard.list_page.table.name') }}</span></th>
                                                            <th><span class="no-wrap">{{ __('psp_registration.dashboard.list_page.table.date') }}</span></th>
                                                            <th><span class="no-wrap">{{ __('psp_registration.dashboard.list_page.table.application_status') }}</span></th>
                                                            <th><span class="no-wrap">{{ __('psp_registration.dashboard.list_page.table.action') }}</span></th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td data-label="{{ __('psp_registration.dashboard.list_page.table.application_id') }}" class="no-wrap">
                                                                {{$vendor->application_number}}
                                                            </td>
                                                            <td data-label="{{ __('psp_registration.dashboard.list_page.table.name') }}" class="no-wrap">
                                                                {{$vendor->vendor_name}}
                                                            </td>
                                                            <td data-label="{{ __('psp_registration.dashboard.list_page.table.date') }}" class="no-wrap">
                                                                {{$vendor->submitted_at}}
                                                            </td>
                                                            <td data-label="{{ __('psp_registration.dashboard.list_page.table.date') }}" class="no-wrap">
                                                                @switch($vendor->application_status)
                                                                    @case('rejected')
                                                                        <span
                                                                            class="status-badge m-0 error-badge text-danger fs-12">{{ __('psp_registration.dashboard.application_statuses.rejected') }}</span>
                                                                        @break
                                                                    @case('in_review')
                                                                        <span
                                                                            class="status-badge m-0 info-badge fs-12 text-center">{{ __('psp_registration.dashboard.application_statuses.in_review') }}</span>
                                                                        @break
                                                                    @case('approved')
                                                                        <span
                                                                            class="status-badge m-0 success-badge fs-12 text-center">{{ __('psp_registration.dashboard.application_statuses.approved') }}</span>
                                                                        @break
                                                                    @case('updated')
                                                                        <span
                                                                            class="status-badge m-0 warning-badge fs-12 text-center">{{ __('psp_registration.dashboard.application_statuses.updated') }}</span>
                                                                        @break
                                                                    @default
                                                                        <span
                                                                            class="status-badge m-0 warning-badge fs-12 text-center">{{ __('psp_registration.dashboard.application_statuses.no_action') }}</span>
                                                                @endswitch
{{--                                                                <span class="status-badge text-center">--}}
{{--                                                                    {{ __('psp_registration.app_status.' . ($vendor->application_status ?? 'unknown')) }}--}}
{{--                                                                </span>--}}
                                                            </td>
                                                            <td data-label="{{ __('psp_registration.dashboard.list_page.table.date') }}" class="no-wrap">
                                                                <a href="/psp/profile/vendor" class="">
                                                                    <!-- {{ __('psp_registration.dashboard.list_page.table.view') }} -->
                                                                    <i class="iconsax text-osool fs-18" icon-name="eye"></i>
                                                                </a>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        @include('layouts.partials._scripts')

        <script src="{{ asset('home/plugins/menu/menu.js') }}"></script>
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.15.10/dist/sweetalert2.all.min.js"></script>

        @yield('jsCode')
        {{--
        <script src="{{ asset('home/js/vendor.min.js') }}"></script>--}}
        <!-- toggle header dropdown , general layout  -->


        <script>
            $(document).ready(function () {
                // Toggle dropdown menu
                $('.user-dropdown-trigger').on('click', function (e) {
                    e.stopPropagation();
                    $('.user-dropdown-menu').toggleClass('active');
                });

                // Close dropdown when clicking outside
                $(document).on('click', function (e) {
                    if (!$(e.target).closest('.user-dropdown-wrapper').length) {
                        $('.user-dropdown-menu').removeClass('active');
                    }
                });

                // Handle mobile menu integration
                $('.mobile-menu-trigger').on('click', function () {
                    if (window.innerWidth <= 768) {
                        $('.user-dropdown-menu').removeClass('active');
                    }
                });
            });
            window.addEventListener('toast', ({ detail }) => toastr.info(detail.message));
        </script>

        <!-- add service script , just for this page  -->
        <script>
            $(document).ready(function () {
                $(".add-service").click(function () {
                    $("#services-container").append(`
                    <div class="service-item row w-100">
                 <div class="form-group col-12 col-md-6 mb-20">
                     <label for="service_1">Service Category <small class="required">*</small></label>
                     <select class="js-example-basic-single js-states form-control" id="service_1" name="service_1">
                         <option value="0">Select Service</option>
                         <option value="1" selected>service_1</option>
                         <option value="2">service_2</option>
                     </select>
                 </div>
                 <div class="form-group col-11 col-md-5">
                     <label>Service Description</label>
                     <input type="text" class="form-control" value="service Description">
                 </div>
                  <button type="button" class="remove-service bg-transparent border-0 cursor-pointer p-0 col-1"><svg width="13" height="14" viewBox="0 0 13 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M11.875 1.875H10.625H9.6875V0.625C9.6875 0.279846 9.40765 0 9.0625 0H3.4375C3.09235 0 2.8125 0.279846 2.8125 0.625V1.875H1.875H0.625C0.279846 1.875 0 2.15485 0 2.5C0 2.84515 0.279846 3.125 0.625 3.125H1.25V11.25C1.25 12.6285 2.37152 13.75 3.75 13.75H8.75C10.1285 13.75 11.25 12.6285 11.25 11.25V3.125H11.875C12.2202 3.125 12.5 2.84515 12.5 2.5C12.5 2.15485 12.2202 1.875 11.875 1.875ZM4.0625 1.25H8.4375V1.875H4.0625V1.25ZM10 11.25C10 11.9394 9.43939 12.5 8.75 12.5H3.75C3.06061 12.5 2.5 11.9394 2.5 11.25V3.125H3.4375H9.0625H10V11.25ZM8.4375 4.375V10.9375C8.4375 11.2827 8.15765 11.5625 7.8125 11.5625C7.46735 11.5625 7.1875 11.2827 7.1875 10.9375V4.375C7.1875 4.02985 7.46735 3.75 7.8125 3.75C8.15765 3.75 8.4375 4.02985 8.4375 4.375ZM5.3125 4.375V10.9375C5.3125 11.2827 5.03265 11.5625 4.6875 11.5625C4.34235 11.5625 4.0625 11.2827 4.0625 10.9375V4.375C4.0625 4.02985 4.34235 3.75 4.6875 3.75C5.03265 3.75 5.3125 4.02985 5.3125 4.375Z" fill="#FF2828"/>
</svg>
</button>
             </div>
            `);
                });

                $(document).on("click", ".remove-service", function () {
                    $(this).closest(".service-item").remove();
                });
            });
        </script>
        <!-- add file script , just for this page  -->
        <script>
            $(document).ready(function () {
                $(".add-file").click(function () {
                    let target = $(this).data("target");
                    let inputId = "file-input-" + Math.random().toString(36).substring(7);

                    let fileInputHtml = `
                    <div class="upload-container d-flex align-items-center gap-10 mt-10">
                        <div class="file-item">
                            <input type="file" id="${inputId}" class="file-input" accept=".jpg,.png,.pdf">
                            <span class="file-name">No file chosen</span>
                            <button type="button" class="remove-file"><svg width="13" height="14" viewBox="0 0 13 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M11.875 1.875H10.625H9.6875V0.625C9.6875 0.279846 9.40765 0 9.0625 0H3.4375C3.09235 0 2.8125 0.279846 2.8125 0.625V1.875H1.875H0.625C0.279846 1.875 0 2.15485 0 2.5C0 2.84515 0.279846 3.125 0.625 3.125H1.25V11.25C1.25 12.6285 2.37152 13.75 3.75 13.75H8.75C10.1285 13.75 11.25 12.6285 11.25 11.25V3.125H11.875C12.2202 3.125 12.5 2.84515 12.5 2.5C12.5 2.15485 12.2202 1.875 11.875 1.875ZM4.0625 1.25H8.4375V1.875H4.0625V1.25ZM10 11.25C10 11.9394 9.43939 12.5 8.75 12.5H3.75C3.06061 12.5 2.5 11.9394 2.5 11.25V3.125H3.4375H9.0625H10V11.25ZM8.4375 4.375V10.9375C8.4375 11.2827 8.15765 11.5625 7.8125 11.5625C7.46735 11.5625 7.1875 11.2827 7.1875 10.9375V4.375C7.1875 4.02985 7.46735 3.75 7.8125 3.75C8.15765 3.75 8.4375 4.02985 8.4375 4.375ZM5.3125 4.375V10.9375C5.3125 11.2827 5.03265 11.5625 4.6875 11.5625C4.34235 11.5625 4.0625 11.2827 4.0625 10.9375V4.375C4.0625 4.02985 4.34235 3.75 4.6875 3.75C5.03265 3.75 5.3125 4.02985 5.3125 4.375Z" fill="#FF2828"/>
</svg>
</button>
</div>
                    </div>
                `;

                    $(target).append(fileInputHtml);

                    let fileInput = $("#" + inputId);
                    let fileNameSpan = fileInput.siblings(".file-name");

                    fileNameSpan.click(function () {
                        fileInput.click();
                    });

                    fileInput.change(function () {
                        if (this.files.length > 0) {
                            fileNameSpan.text(this.files[0].name);
                        } else {
                            fileNameSpan.text("No file chosen");
                        }
                    });
                });

                $(document).on("click", ".remove-file", function () {
                    $(this).closest(".upload-container").remove();
                });
            });

            document.addEventListener("DOMContentLoaded", function () {
                document.querySelectorAll(".file-item").forEach((fileItem) => {
                    let input = fileItem.querySelector(".file-input");
                    let fileNameSpan = document.createElement("span");
                    fileNameSpan.classList.add("file-name");
                    fileNameSpan.textContent = "No file chosen";
                    fileItem.insertBefore(fileNameSpan, input);

                    fileItem.addEventListener("click", () => input.click());

                    input.addEventListener("change", function () {
                        if (this.files.length > 0) {
                            fileNameSpan.textContent = this.files[0].name;
                        }
                    });

                    fileItem.querySelector(".remove-file").addEventListener("click", function (event) {
                        event.stopPropagation();
                        input.value = "";
                        fileNameSpan.textContent = "No file chosen";
                    });
                });
            });
        </script>
        <script>
            window.addEventListener('swal', function (e) {
                Swal.fire(e.detail);
            });
        </script>

        <script>
            function submitUploadProfileImageForm() {
                setTimeout(() => {
                    // $("#img_upload_form_submittion").click();
                }, 2000)
            }

            function pspLogout() {
                $("#psp_logout_btn").click()
                // $("#psp-logout-form").submit();
            }
        </script>
    </div>

</body>

</html>
