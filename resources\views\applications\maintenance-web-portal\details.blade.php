@extends('layouts.app')
@section('styles')
<style type="text/css">
    a.btn-outline-primary .feather{
        color:#5f63f2 !important;
    }

a.btn-outline-primary:hover .feather{
        color:#ffffff !important;
    }</style>
@endsection
@section('content')
<!-- Main Content Starts Here -->
<main class="main-content">

    <div class="breadcrumb-main">
        <div class="d-sm-flex flex-row-reverse justify-content-between flex-fill px-4 align-items-center border-bottom pb-3">
        <div class="logo-div text-xs-center text-center">
                        @if($data['projectImage'])                        
                        <a class="" href="{{  url('') }}"><img class="svg dark" src="{{ImagesUploadHelper::displayImage($data['projectImage'], 'uploads/project_images')}}" alt="svg" width="150"></a>
                        @else
                        <a class="" href="{{  url('') }}"><img class="svg dark" src="{{ asset('img/OSOOL_logo_svg.svg') }}" alt="svg" width="150"></a>
                        @endif
                        {{--<a class="" href="{{  url('') }}"><img class="svg dark" src="{{ asset('img/OSOOL_logo.png') }}" alt="svg" width="150"></a>--}}
                        </div>
        <div>
        <h4 class="text-capitalize breadcrumb-title w-100 mb-0 text-sm-left text-center mt-sm-0 mt-4">{{ __('data_maintanance_request.common.Request') }} #{{ $data['maintanance_request_id']}}</h4>
        <div class="clearfix w-100"></div>

        <p class=" w-100 grey-7 text-sm-left text-center mb-0">{{ __('data_maintanance_request.common.You can track your maintenance request details here') }}</p>
        </div>
    </div>
    </div>

    <div class="form-element">
        <div class="row">
            <div class="col-lg-12">
                <div class="card-default mb-2 border-bottom">
                    <div class="card-header no-bg border-none">
                        <p class="fs-18 text-dark d-flex align-items-center justify-content-between mb-0 icon-heading"><span class="d-flex align-items-center justify-content-between"><span class="icon mr-2 border rounded"><span data-feather="clock"></span></span> <span>{{ __('data_maintanance_request.common.Workorder Status') }}</span></span> <span class="badge badge-round badge-xl {{ $data['status_class'] }} pull-right">{{
                        $data['trans_status']
                    }}</span></p>

                    </div>
                    @if($data['details']->status == 'Completed' && $data['details']->feedback == '')
                    <div class="card-header no-bg border-bottom border border-warning py-0">
                        <a href="javascript:void(0);" class="btn btn-outline-primary" id="add_feedback"><b><span data-feather="star"></span> {{ __('data_maintanance_request.buttons.add_feedback') }}</b></a>
                    </div>
                    @endif
                     @if($data['details']->status == 'Rejected')
                    <div class="card-body pb-md-20 pt-0">
                        <form>
                            <div class="form-row mx-n15">
                                <div class="col-12 px-15">
                                    <p>
                                        {{  $data['details']->reason  }}
                                    </p>
                                </div>
                            </div>
                        </form>
                    </div>
                    @else
                    <div class="card-body pb-md-20 pt-0">
                        <form>
                            <div class="form-row mx-n15">
                                <div class="col-12 px-15">
                                    <p>
                                        <!--{{ __('data_maintanance_request.common.'.$data['status_description']) }}-->
                                    </p>
                                </div>
                            </div>
                        </form>
                    </div>
                    @endif
                </div>
                <div class="card-default mb-2 border-bottom">
                    <div class="card-header no-bg border-none">
                        <p class="fs-18 text-dark d-flex align-items-center mb-0 icon-heading"><span class="icon mr-2 border rounded"><span data-feather="map-pin"></span></span>{{ __('data_maintanance_request.common.Workorder Location') }}</p>
                    </div>
                    <div class="card-body pb-md-20 pt-0">
                        <form>
                            <div class="form-row mx-n15">
                                <div class="col-md-4 col-12 mb-10 px-15">
                                        <div class="bg-grey px-3 py-4 radius-md">
                                    <label for="validationDefault01" class="il-gray fs-15 align-center">{{$trans_the_building}}</label>
                                    <span class="text-dark1">{{ $data['buildingName'] }}</span>
                                </div>
                                </div>
                                <div class="col-md-4 col-12 mb-10  px-15">
                                        <div class="bg-grey px-3 py-4 radius-md">
                                    <label for="validationDefault02" class="il-gray fs-15 align-center">{{ __('data_maintanance_request.common.floor') }}</label>
                                    <span class="text-dark1">{{                        $data['details']->floor  }}</span>
                                    </div>
                                </div>
                                <div class="col-md-4 col-12 mb-10  px-15">
                                        <div class="bg-grey px-3 py-4 radius-md">
                                    <label for="validationDefault012" class="il-gray fs-15 align-center">{{ __('data_maintanance_request.common.space_unit') }}</label>
                                    <span class="text-dark1">{{                        $data['details']->space_no  }}</span>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="">
                    <div class="card-header no-bg border-none">
                        <p class="fs-18 text-dark d-flex align-items-center mb-0 icon-heading"><span class="icon mr-2 border rounded"><span data-feather="info"></span></span>{{ __('data_maintanance_request.common.Issue Details') }}</p>
                    </div>
                    <div class="card-body pb-md-20 pt-0">
                        <form>
                            <div class="form-row mx-n15">
                                <div class="col-12 px-15">
                                        <div class="bg-grey p-4 radius-md">
                                    <label for="validationDefault01" class="il-gray fs-14 fw-100 align-center">{{ __('data_maintanance_request.common.Issue Description') }}</label>
                                    <p>
                                    {{ $data['details']->description  }}
                                    </p>
                                </div>
                                </div>

                            </div>

                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
@endsection
@section('scripts')

<script>
   function previewFile(input){
      var file = $("#customFile").get(0).files[0];

      if(file){
            var reader = new FileReader();

            reader.onload = function(){
               $("#previewImg").attr("src", reader.result);
            }

            reader.readAsDataURL(file);
      }
   }
   function previewFile1(input){
      var file = $("#customFile1").get(0).files[0];

      if(file){
            var reader = new FileReader();

            reader.onload = function(){
               $("#previewImg1").attr("src", reader.result);
            }
            reader.readAsDataURL(file);
      }
   }
   function previewFile2(input){
      var file = $("#customFile2").get(0).files[0];

      if(file){
            var reader = new FileReader();

            reader.onload = function(){
               $("#previewImg2").attr("src", reader.result);
            }
            reader.readAsDataURL(file);
      }
   }
</script>
<script type="text/javascript">
   $(function() {
      $(".custom-file input:file").change(function (){
            $(this).parent().addClass("active");
      });
   });
   $(".custom-file .close").click(function(){
      $(this).parent().parent().find(".custom-file-input").val() == "";
      $(this).parent().parent().removeClass("active");
   });
</script>
<script>



    $(document).ready(function(){

        $('#add_feedback').click(function (){
            $.ajax({
                url: "{{ route('maintenance.skip') }}",
                method: "POST",
                data: {
                    _token: $('meta[name="csrf-token"]').attr("content"),
                    skip: "0",
                    maintanance_id: "{{$data['maintanance_request_id']}}",

                },
                dataType: "json",
                beforeSend: function () {},
                success: function (data) {

                    location.reload();

                },
                error: function (data) {
                    var errors = $.parseJSON(data.responseText);
                    toastr.error(data, translations.general_sentence.validation.Error, {
                        timeOut: 1500,
                        positionClass: "toast-top-center",
                        progressBar: true,
                        preventDuplicates: true,
                        preventOpenDuplicates: true,
                    });
                },
            });
        });
    });

</script>
@endsection
