<style>
.datepicker{
    z-index: 10000000 !important;
}
.ui-datepicker {
  z-index: 10000000 !important;
}
</style>
<script src="https://unpkg.com/feather-icons"></script>
<div class="tab-content">
    <div class="tab-pane fade show active" id="tab-v-4" role="tabpanel" aria-labelledby="tab-v-4-tab">
        @csrf
        <input type="hidden" id="id" name="id" value="{{$data['id']}}">
        <input type="hidden" id="asset_id" name="id" value="{{$data['id']}}">
        <input type="hidden" id="property_id1" name="property_id" value="{{$data['property_id']}}">
        <input type="hidden" id="building_id" name="building_id" value="{{$data['building_id']}}">
        <div class="form-group mb-20">
            <label for="issue_type">{{__('data_properties.common.property_name')}}<span class="required">*</span></label>
            <input type="text" class="form-control" value="{{$data['builiding_name']}}" readonly="readonly">
        </div>
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="region_id">{{__('data_properties.property_forms.label.floor')}}<span class="required">*</span></label>
                    <div class="atbd-select">
                        <select class="form-control" id="floor1" name="floor">
                            <option disabled selected value="">{{__('data_properties.property_forms.place_holder.floor')}}</option>
                            @foreach($data['asset_floor'] as $floor)
                                <option value="{{$floor}}" @if($floor==$data['row_data']->floor) selected @endif>{{$floor}}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div id="floor_error1" class="required"></div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="region_id">{{__('data_properties.property_forms.label.room')}}<span class="required">*</span></label>
                        <div class="atbd-select">

                            <select class="form-control" id="room1" name="room">
                                <option disabled selected value="">{{__('data_properties.property_forms.label.room')}}</option>

                                @foreach($data['asset_room'] as $room)
                                    <option value="{{$room->id}},,,{{$room->room}}" @if($room->room==$data['row_data']->room) selected @endif>{{$room->room}} @if($room->deleted_at != null) {{__('general_sentence.modal.deleted')}}  @endif </option>
                                @endforeach
                                <!-- <option value="{{$data['row_data']->room_id}},,,{{$data['row_data']->room}}"  selected >{{$data['row_data']->room}}</option> -->

                                <!-- @foreach($data['asset_room'] as $room)
                                    <option value="{{$room}}" @if($room==$data['row_data']->room) selected @endif>{{$room}}</option>
                                @endforeach -->
                            </select>
                        </div>
                    </div>
                    <div id="room_error1" class="required"></div>
                </div>
            </div>

            <div class="form-group">
                <label for="region_id">{{__('data_properties.property_forms.label.Asset_Name')}} <span class="required">*</span></label>
                <div class="atbd-select">
                    <select class="form-control" id="asset_name_id1" name="asset_name_id">

                        @foreach($data['asset_name'] as $name)
                            <option value="{{$name->id}}" data="{{$name->asset_symbol}}" @if($name->id==$data['row_data']->asset_name_id) selected @endif >{{$name->asset_name}} @if($name->deleted_at != null || $name->is_deleted == 'yes') {{__('general_sentence.modal.deleted')}}  @endif </option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div id="name_error1" class="required"></div>

            @if(isset($data['asset_category']->directCategory) != NULL)
            <div class="form-group">
                <label for="region_id">{{__('data_properties.property_forms.label.Asset_Category')}} <span class="required">*</span></label>
                <div class="atbd-select">
                    <select class="form-control" id="asset_category_id1" name="asset_category_id[]" multiple>
                        <option value="">{{__('data_properties.property_forms.place_holder.Choose_Category')}}</option>
                        <option value="{{$data['asset_category']->directCategory->id}}" data="{{$data['asset_category']->directCategory->asset_symbol}}" selected>{{$data['asset_category']->directCategory->asset_category}} @if($data['asset_category']->directCategory->deleted_at != null || $data['asset_category']->directCategory->is_deleted == 'yes') {{__('general_sentence.modal.deleted')}}  @endif </option>
                    </select>
                </div>
            </div>
            <div id="name_error1" class="required"></div>
            @else
            <div class="form-group">
                <label for="region_id">{{__('configration_assets.asset_categories_forms.label.service_type')}}<span class="required">*</span></label>
                <div class="atbd-select">
                    <select class="form-control" id="asset_category_id1" name="asset_category_id[]" multiple>
                        <option value="">{{__('data_properties.property_forms.place_holder.Choose_Category')}}</option>
                        @if(isset($data['asset_category']) && isset($data['asset_category']->categories) && !empty($data['asset_category']->categories))
                            @foreach ($data['asset_category']->categories as $category)
                                <option value="{{$category->id}}" data="{{$category->asset_symbol}}" @if(in_array($category->id, $data['row_data']->asset_categories)) selected @endif>{{$category->asset_category}} @if($category->deleted_at != null || $category->is_deleted == 'yes') {{__('general_sentence.modal.deleted')}}  @endif </option>
                            @endforeach
                        @endif
                    </select>
                </div>
            </div>
            <div id="name_error1" class="required"></div>
            @endif
            <div class="form-group mb-20">
                <input @if($data['row_data']->hide_asset_symbol) checked @endif class=""type="checkbox" id="noAssetSymbolEdit" name="noAssetSymbol"
                    value="1">
                <label class="m-auto"
                    for="noAssetSymbolEdit">{{ __('data_properties.property_forms.label.hideAssetSymbol') }} </label>
            </div>
            <div class="form-group mb-20">
                <label for="issue_type">{{__('data_properties.property_forms.label.Asset_Number')}}<span class="required">*</span></label>
                <div class="input-group mb-3">
                    <div class="input-group-prepend">
                        <input name="asset_symbol" id="asset_symbol1" class="form-control" placeholder="{{__('data_properties.property_forms.place_holder.Symbol')}}" type="text" readonly="readonly" value="{{$data['row_data']->asset_symbol}}">
                        <!-- <span class="input-group-text" id="basic-addon11">FCU</span> -->
                    </div>
                    <input type="text" class="form-control" value="{{$data['row_data']->asset_number}}" placeholder="001" name="asset_number" id="asset_number1">
                </div>
                <div id="number_error1" class="required"></div>
            </div>
            <div class="form-group mb-20" id="assetSymbolDivEdit">
                <label for="issue_type">{{__('work_order.asset_information.asset_tag')}}</label>

                <input type="text" class="form-control" id="asset_tag_data1" value="{{$data['row_data']->asset_tag}}" readonly="readonly" disabled>
            </div>
        <div>
            @if(isset($data['row_data']->barcode_img_str)&&$data['row_data']->barcode_img_str!='')

            <div class="view-img">
            <a href="javascript:void(0);">
            <div class="wo-img">

                <img id = "previousAssetQr" data-url="{{url('/')."/maintenance/need-help/".$data['barcode_value']}}"
                 src="{{$data['row_data']->barcode_img_str}}" alt="barcode" width="100">
                <!-- <object id = "previousAssetQr" class="mb-o pb-0"  data="{{$data['row_data']->barcode_img_str}}" type="image/svg+xml"></object> -->
            </div>
                        </div>

            @else
                <img src="{{asset('uploads/property/'.$data['row_data']->barcode)}}" alt="barcode" width="100">
            @endif
        </div>
        <h6 class="mt-1 " id="previous_label">{{$data['row_data']->barcode_value}}</h6>
        <div id="previous_action_barcode">
            @if($data['row_data']->barcode_select=='1')
            {{__('data_properties.property_forms.label.generated_manually')}}
            @else
            {{__('data_properties.property_forms.label.generated_osool')}}
            @endif
        </div>
        <br><br>
        <div id="hide_show_options">
            <a  id="update_asset_barcode" class="h6 text-primary cursor-pointer"> {{__('data_properties.property_forms.label.edit_barcode')}}</a>
            <a  id="hide_update_asset_barcode" class="h6 d-none text-primary cursor-pointer"> {{__('data_properties.property_forms.label.hide_options')}}</a>
        </div>
        <br>
        <div class="form-group d-none" id="hidden_update_barcode_radio_buttons">
            <label for="name1">{{__('data_properties.property_forms.label.property_barcode_choose_one')}}</label>
            <br/>
            <div class="radio-theme-default custom-radio">
                <input class="radio" type="radio" name="radio_barcode" value="barcode_upload" id="radio_barcode_upload1"  >
                <label for="radio_barcode_upload1">
                    <span class="radio-text">{{__('data_properties.property_forms.label.upload_my_own_barcode')}}</span>
                </label>
            </div>
            <div class="radio-theme-default custom-radio">
                <input class="radio" type="radio" name="radio_barcode" value="barcode_generate"
                    id="radio_barcode_generate1">
                <label for="radio_barcode_generate1">
                    <span class="radio-text">{{__('data_properties.property_forms.label.regenerate_using_osool')}}</span>
                </label>
            </div>
        </div>
        <!-- Bar Code generates script -->
        <div class="" style="display:none;" id="generateBarcode1">
            <div class="qr-code-generator">
                <div class="row">
                    <div class="col-sm-8 mb-3 pr-0">
                        <input type="text" name="qr_url"  id="qr_url3" class="qr-url3 form-control" value="" placeholder="12345Abcde" minlength="5" maxlength="15"  onchange="uniqueQrNumber3()">
                    </div>
                    <input type="hidden" class="qr-size" value="" minlength="5">
                    <div class="col-sm-4 pl-0">
                        <input type="button" class="generate-qr-code3 btn btn-light btn-default btn-squared fw-400 text-capitalize b-light color-light " value="{{__('data_properties.property_forms.label.generate_barcode')}}">
                    </div>
                </div>
                <div id="qrcode3"></div>
                <div class="qrlabel3"></div>
            </div>
            <input type="hidden" name="qr_img_str_own" class="" id="qr_img_str_own3" >
            <img class="qrcode-preview3" src="" alt="Qrcode" style="display: none;" />
        </div>
        <div id="qr_url_error2"></div>
        <div class="form-group mb-20" style="display:none;" id="generateBarcodeByOsool3">
            <!-- <label for="name1">Enter barcode</label> -->
            <div class="qr-code-generator">
                <div class="input-group-prepend">
                    <input type="hidden" name="qr_url1" id="qr-url4" class="qr-url4 form-control" placeholder="12345Abcde">
                    <input type="hidden" class="qr-size1" value="" min="" max="">
                </div>
                <input type="button" class="generate-qr-code1 btn btn-light btn-default btn-squared fw-400 text-capitalize b-light color-light" value="{{__('data_properties.property_forms.label.generate_barcode')}}">
                <div id="qrcode5"></div>
                <div class="qrlabel5"></div>
            </div>
            <input type="hidden" name="qr_img_str_osool" class="" id="qr_img_str_osool4" value="">
        </div>
        {{--</div>
            <input type="hidden" id="image2" value="" >
            @if($errors->has('logo'))
            <span class="text-danger">{{$errors->first('logo')}}</span>
            @endif
        </div>--}}
        {{--<div class="button-group d-flex justify-content-end pt-25">
            <button type="reset"
                class="btn btn-light btn-default btn-squared fw-400 text-capitalize b-light color-light resetEve">
                {{__('data_properties.property_button.Reset')}}
            </button>
            <button type="submit" class="btn btn-primary btn-default btn-squared text-capitalize">
            {{__('work_order.button.save')}}
            </button>
        </div>--}}
        </div>
        <div class="tab-pane fade" id="tab-v-5" role="tabpanel" aria-labelledby="tab-v-5-tab">
            <div class="form-group form-group-calender">
                <label>{{__('data_properties.property_forms.label.purchase_date')}}</label>
                <div class="position-relative">
                <input type="text" readonly value="{{isset($data['row_data']->purchase_date) ? date('d/m/Y',strtotime($data['row_data']->purchase_date))  : '' }}" name="purchase_date" id="purchase_date_edit" class="form-control date-picker" placeholder="{{__('data_properties.property_forms.place_holder.purchase_date')}}">
                <a id="calendar-icon"><span data-feather="calendar"></span></a>
                </div>
            </div>
            <div class="form-group">
                <label>{{__('data_properties.property_forms.label.model_number')}}</label>
                <input type="text" value="{{isset($data['row_data']->model_number) ? $data['row_data']->model_number  : '' }}" name="model_number" id="model_number" class="form-control" placeholder="{{__('data_properties.property_forms.place_holder.model_no')}}">
                <div id="model_error"></div>
            </div>
            <div class="form-group">
                <label>{{__('data_properties.property_forms.label.manufacturer_name')}}</label>
                <input type="text" value="{{isset($data['row_data']->manufacturer_name) ? $data['row_data']->manufacturer_name  : '' }}" name="manufacturer_name" id="manufacturer_name" class="form-control" placeholder="{{__('data_properties.property_forms.place_holder.manufacturer_name')}}">
                <div id="manufacturer_error"></div>
            </div>

            <div class="row">
                                                <div class="col-md-5">
                                                    <div class="form-group">
                                                        <label>{{ __('data_properties.property_forms.label.assets_status') }} {{ __('data_properties.property_forms.label.optional') }}</label>

                                                        <div class="atbd-select">
                                                        <select class="form-control select2" id="asset_status1"  name ="asset_status">
                                                        <option disabled selected value="">
                                        {{ __('data_properties.property_forms.label.select') }}</option>
                                                            @foreach($data['asset_statuses'] as $status)
                                                                <option  @if($data['row_data']->asset_status == strtolower($status->name)) selected @endif  value="{{strtolower($status->name)}}">
                                                                    {{ $status->localized_name }}
                                                                </option>
                                                            @endforeach
                                                        </select>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-7 asset_damge_files_section1 @if($data['row_data']->asset_status != 'damaged') d-none @endif">
                                                    <div class="form-group form-group-calender">
                                                        <label>{{ __('data_properties.property_forms.label.date_of_asset_damage') }} {{ __('data_properties.property_forms.label.optional') }}</label>
                                                        <div class="position-relative">
                                                            <input readonly value="{{isset($data['row_data']->asset_damage_date) ? date('d/m/Y',strtotime($data['row_data']->asset_damage_date))  : '' }}"type="text" name="date_of_damage" id="date_of_damage1"
                                                                class="form-control date-picker"
                                                                placeholder="{{ __('data_properties.property_forms.label.date_of_asset_damage') }}">

                                                             <a href="#"><span data-feather="calendar"></span></a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group mb-1 asset_damge_files_section1 @if($data['row_data']->asset_status != 'damaged') d-none @endif">
                                                <label>{{ __('data_properties.property_forms.label.files_damage') }} {{ __('data_properties.property_forms.label.optional') }}</label>
                                                    <div class="item-inner">
                                                        <input type="hidden" name ="damage_images" value ="" id="damage_images1">
                                                        <div class="item-content">
                                                                <div class="atbd-upload">
                                                                    <label for="file_upload3" class="btn btn-lg btn-outline-lighten btn-upload mb-0 cursor-pointer">
                                                                        <span data-feather="upload"></span  onclick="$('#file_upload3').click()"> {{__('work_order.common.Upload_images')}}
                                                                            <input data-required="image" type="file" name="damage_files[]" id="file_upload3" class="image-input upload-one" accept="image/png, image/jpeg,image/jpg,Application/pdf" multiple value="">
                                                                    </label>
                                                                </div>
                                                                <div class="asset-damage">
                                                                    <!--If the file is pdf-->
                                                                <ul class="thumb-Images mt-2" id="imgList1">
                                                                    @if(!empty($data['damage_images']))
                                                                    <?php $damage_images =  $data['damage_images'];
                                                                        for( $i = 0 ; $i < count($damage_images) ; $i++ ) {
                                                                    ?>
                                                                     <li><div class="img-wrap"> <span class="close">×</span><img class="thumb" src="
                                                                @if(in_array('pdf', explode('.',$damage_images[$i]),'pdf')){{asset('img/pdf.png')}} @else {{asset('uploads/asset_images/'.$damage_images[$i])}} @endif" title="logo.png" data-id="logo.png"></div><div class="FileNameCaptionStyle">{{$damage_images[$i]}}</div></li>
                                                                    <?php }?>
                                                                @endif
                                                                </ul>
                                                                <!--If the file is pdf-->

                                                                    <output id="Filelist" class="image-list mt-10"></output>
                                                                </div>

                                                              <div id="logo_id_error"></div>
                                                        </div>

                                                    <!--item-content-->
                                                    </div>
                                            </div>


            <div class="form-group">
                <label>{{__('data_properties.property_forms.label.general_information')}}</label>
                <textarea class="form-control" name="general_information" id="general_information" placeholder="{{__('data_properties.property_forms.place_holder.general_info')}}">{{ $data['row_data']->general_information }}</textarea>
                <div id="general_information_error"></div>
            </div>
        </div>
        <div class="tab-pane fade" id="tab-v-6" role="tabpanel" aria-labelledby="tab-v-6-tab">
            <div class="form-group" id="image_upload_section2">
                @if(1)
                    <div class="item-inner" >
                        <div class="item-content">
                            <div class="upload-files">
                                <div class="atbd-tag-wrap">
                                    <div class="atbd-upload">
                                        <div class="atbd-upload__button " >
                                            <a href="javascript:void(0)" id="upload_file_button1" class="btn btn-lg btn-outline-lighten btn-upload" onclick="$('#file_upload1').click()"> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-upload"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="17 8 12 3 7 8"></polyline><line x1="12" y1="3" x2="12" y2="15"></line></svg>  {{__('data_properties.property_forms.label.upload_files')}} </a>
                                            <input type="file" name="gen_image[]"  class="upload-one file_upload_123" multiple id="file_upload1">
                                            <input type="text" name="gen_image_name"  value="{{$data['row_data']->gen_image}}" class="upload-one file_upload_123" id="file_upload1">
                                        </div>
                                        <div class="atbd-upload__file" >
                                            <?php
                                                if(isset($data['row_data']->gen_image)) {
                                                    $asset_files = explode(',',$data['row_data']->gen_image);
                                                } else{
                                                    $asset_files = [];
                                                }
                                                //print_r($data['assets_files']);
                                            ?>
                                            <ul id="hidden_label_file_name1">
                                                @if(count($data['assets_files'])>0)
                                                <?php
                                                    $i = 1;
                                                ?>
                                                @foreach($data['assets_files'] as $file)
                                                <li class="file-list-{{ $file->id }}">
                                                    <a class="text-primary" target="_blank" href="{{($file->file_name) ?  asset('uploads/asset_images/'.$file->file_name) : '/img/upload.png' }}" class="file-name t-p"><i class="las la-paperclip"></i> <span class="name-text " id="file_name1"><span>{{$file->file_name}}</span></span></a>
                                                    <a onClick="removeFile1({{  $file->id }},`{{ $file->file_name }}`)" class="btn-delete"><i class="la la-trash"></i></a>
                                                </li>
                                                <?php
                                                    $i++;
                                                ?>
                                                @endforeach
                                                @endif
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <script>
                                    //$("#upload_file_button1").hide();
                                </script>
                            </div>
                        </div>
                    </div>
                @else
                    <!-- <label for="logo">File </label> -->
                    <div class="item-inner">
                        <div class="upload-files">
                            <div class="atbd-tag-wrap">
                                <div class="atbd-upload">
                                    <div class="atbd-upload__button">
                                        <a href="javascript:void(0)" id="upload_file_button1" class="btn btn-lg btn-outline-lighten btn-upload" onclick="$('#file_upload1').click()"> <span data-feather="upload"></span> {{__('data_properties.property_forms.label.upload_files')}}</a>
                                        <input type="file" name="gen_image" class="upload-one file_upload_123" id="file_upload1">
                                    </div>
                                    <div class="atbd-upload__file" id="hidden_label_file_name1">
                                        <ul>
                                            <li>
                                                <a href="javascript:void(0)" class="file-name"><i class="las la-paperclip"></i> <span class="name-text" id="file_name1"><span></span></span></a>
                                                <a href="javascript:void(0)" onClick="removeFile1()" class="btn-delete"><i class="la la-trash"></i></a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div>
                            <script>
                            //$("#hidden_label_file_name1").hide();
                            </script>
                            <!--item-content-->
                        </div>
                    </div>
                @endif
                <input type="hidden" name="old_img" value="{{($data['row_data']->gen_image) ?  $data['row_data']->gen_image : '' }}">
            </div>
        </div>
{{--
<!-- Asset Work orders tab content -->
    <div class="tab-pane fade" id="tab-v-7" role="tabpanel" aria-labelledby="tab-v-7-tab">
        <div class="row">
            <div class="col-lg-12">
                <div class="input-group search-group">
                    <div class="input-group-prepend">
                    <span class="input-group-text" id="basic-addon1"><i class="fa fa-search" aria-hidden="true"></i></span>
                    </div>
                    <input type="text" class="form-control rounded search_building" placeholder="Search for workorders..." aria-label="Username" aria-describedby="basic-addon1">
                </div>
            </div>
            <div class="col-lg-12 mb-4">
                <span class="d-inline-block py-3 rounded">
                Showing latest  <b>  20 workorders</b>
                </span>
            </div>
        </div>
        <div class="properties-list">
            <ul class="site-scrollbar-one">
                <li>
        <table id="" class="table table-borderless table-p-bottom mb-0">
    <tbody>
        <tr class="asset_tr_container asset_building_row435 asset_building_manager_row435 asset_table_row435">
            <td>
               <a href="#"><span class="fw-400 badge badge-round badge-primary badge-lg badge-outlined tt">PM-021433</span></a>
            </td>
            <td>
                <span>14-03-2023 07:00</span>
            </td>
            <td>
               <span class="bg-opacity-success color-success rounded-pill userDatatable-content-status active">Open</span>
            </td>
        </tr>
        <tr class="asset_tr_container asset_building_row435 asset_building_manager_row435 asset_table_row435">
            <td>
                <a href="#"><span class="fw-400 badge badge-round badge-primary badge-lg badge-outlined tt">PM-021539</span></a>
            </td>
            <td>
                <span>14-03-2023 07:00</span>
            </td>
            <td>
                <span class="badge-in-progress rounded-pill userDatatable-content-status active">In Progress</span>
            </td>
        </tr>
        <tr class="asset_tr_container asset_building_row435 asset_building_manager_row435 asset_table_row435">
            <td>
                <a href="#"><span class="fw-400 badge badge-round badge-primary badge-lg badge-outlined tt">RM-021510</span></a>
            </td>
            <td>
                <span>14-03-2023 07:00</span>
            </td>
            <td>
                <span class="bg-opacity-warning color-warning rounded-pill userDatatable-content-status active">On Hold</span>
            </td>
        </tr>
        <tr class="asset_tr_container asset_building_row435 asset_building_manager_row435 asset_table_row435">
            <td>
               <a href="#"><span class="fw-400 badge badge-round badge-primary badge-lg badge-outlined tt">PM-021433</span></a>
            </td>
            <td>
                <span>14-03-2023 07:00</span>
            </td>
            <td>
               <span class="bg-opacity-success color-success rounded-pill userDatatable-content-status active">Open</span>
            </td>
        </tr>
        <tr class="asset_tr_container asset_building_row435 asset_building_manager_row435 asset_table_row435">
            <td>
                <a href="#"><span class="fw-400 badge badge-round badge-primary badge-lg badge-outlined tt">PM-021539</span></a>
            </td>
            <td>
                <span>14-03-2023 07:00</span>
            </td>
            <td>
                <span class="badge-in-progress rounded-pill userDatatable-content-status active">In Progress</span>
            </td>
        </tr>
        <tr class="asset_tr_container asset_building_row435 asset_building_manager_row435 asset_table_row435">
            <td>
                <a href="#"><span class="fw-400 badge badge-round badge-primary badge-lg badge-outlined tt">RM-021510</span></a>
            </td>
            <td>
                <span>14-03-2023 07:00</span>
            </td>
            <td>
                <span class="bg-opacity-warning color-warning rounded-pill userDatatable-content-status active">On Hold</span>
            </td>
        </tr>
        <tr class="asset_tr_container asset_building_row435 asset_building_manager_row435 asset_table_row435">
            <td>
               <a href="#"><span class="fw-400 badge badge-round badge-primary badge-lg badge-outlined tt">PM-021433</span></a>
            </td>
            <td>
                <span>14-03-2023 07:00</span>
            </td>
            <td>
               <span class="bg-opacity-success color-success rounded-pill userDatatable-content-status active">Open</span>
            </td>
        </tr>
        <tr class="asset_tr_container asset_building_row435 asset_building_manager_row435 asset_table_row435">
            <td>
                <a href="#"><span class="fw-400 badge badge-round badge-primary badge-lg badge-outlined tt">PM-021539</span></a>
            </td>
            <td>
                <span>14-03-2023 07:00</span>
            </td>
            <td>
                <span class="badge-in-progress rounded-pill userDatatable-content-status active">In Progress</span>
            </td>
        </tr>
        <tr class="asset_tr_container asset_building_row435 asset_building_manager_row435 asset_table_row435">
            <td>
                <a href="#"><span class="fw-400 badge badge-round badge-primary badge-lg badge-outlined tt">RM-021510</span></a>
            </td>
            <td>
                <span>14-03-2023 07:00</span>
            </td>
            <td>
                <span class="bg-opacity-warning color-warning rounded-pill userDatatable-content-status active">On Hold</span>
            </td>
        </tr>
        <tr class="asset_tr_container asset_building_row435 asset_building_manager_row435 asset_table_row435">
            <td>
               <a href="#"><span class="fw-400 badge badge-round badge-primary badge-lg badge-outlined tt">PM-021433</span></a>
            </td>
            <td>
                <span>14-03-2023 07:00</span>
            </td>
            <td>
               <span class="bg-opacity-success color-success rounded-pill userDatatable-content-status active">Open</span>
            </td>
        </tr>
        <tr class="asset_tr_container asset_building_row435 asset_building_manager_row435 asset_table_row435">
            <td>
                <a href="#"><span class="fw-400 badge badge-round badge-primary badge-lg badge-outlined tt">PM-021539</span></a>
            </td>
            <td>
                <span>14-03-2023 07:00</span>
            </td>
            <td>
                <span class="badge-in-progress rounded-pill userDatatable-content-status active">In Progress</span>
            </td>
        </tr>
        <tr class="asset_tr_container asset_building_row435 asset_building_manager_row435 asset_table_row435">
            <td>
                <a href="#"><span class="fw-400 badge badge-round badge-primary badge-lg badge-outlined tt">RM-021510</span></a>
            </td>
            <td>
                <span>14-03-2023 07:00</span>
            </td>
            <td>
                <span class="bg-opacity-warning color-warning rounded-pill userDatatable-content-status active">On Hold</span>
            </td>
        </tr>
    </tbody>
</table>
</li>
</ul>

<div class="d-flex justify-content-end mt-3">
                                           <a href="" class="py-1 px-3 border border-primary text-primary rounded mb-2 view_all_button" data-toggle="modal" data-target="#all-properties">View All</a>
                                           </div>

    </div>
    </div>
<!--End Asset Work orders tab content -->
    --}}
    <div class="button-group d-flex justify-content-end pt-25">
        <button type=""  data-dismiss="modal" aria-label="Close"
        class="btn btn-light btn-default btn-squared fw-400 text-capitalize b-light color-light ">
        {{__('data_properties.property_button.cancel')}}
        </button>
        <button type ="submit" data-tab="basic_info" class="btn btn-primary btn-default btn-squared text-capitalize" value=" {{__('work_order.button.save')}}"> {{__('work_order.button.save')}} </button>
    </div>
</div>
 <!-- Script Here -->
 <script type="text/javascript" charset="utf-8" src="{{asset('js/admin/asset/assetManagement.js')}}"></script>
 <!--<script src="{{ asset('js/JsBarcode.all.min.js') }}"></script>-->
 <script src="https://s3-us-west-2.amazonaws.com/s.cdpn.io/130527/qrcode.js"></script>
 <script src="https://cdn.jsdelivr.net/gh/davidshimjs/qrcodejs@gh-pages/qrcode.min.js"></script>

 {{--@if($data['row_data']->barcode_select=='1')

 @endif--}}
    <script>
        // Get the URL to fetch asset categories based on selected asset names
        var assetCategoryRoute = '{{ route('property.get-asset-categories') }}';
    </script>
 <script type="text/javascript">
    $("#asset_category_id1").select2({
        placeholder:translations.data_properties.property_forms.place_holder.Choose_Category,
        dropdownCssClass: "tag",
        language: { noResults: () => translations.general_sentence.validation.No_results_found,},
    });

  var qrcode = new QRCode(document.getElementById("qrcode"),
                {
                text: "http://www.runoob.com",
                width: 450,
                height: 450,
                colorDark : "#000000",
                colorLight : "#ffffff",
                correctLevel : QRCode.CorrectLevel.H
                });
   setTimeout(
     function ()
     {
         let dataUrl = document.querySelector('#qrcode').querySelector('img').src;
         downloadURI(dataUrl, 'qrcode.png');
     }
     ,1000);


    $(document).on('change', '#asset_edit_form #floor1', function(e) {
     e.preventDefault();
     var floor = $(this).find(":selected").val();
     var building_id=$('#building_id').val();
     var app_url = $('#app_url').val();

     $.ajax({
         type: 'POST',
         dataType: "json",
         url: app_url + "/property/asset/get_asset_rooms",
         data: {'_token': $("input[name='_token']").val(), floor:floor,building_id:building_id},
         beforeSend: function( xhr ) {
             xhr.overrideMimeType( "text/plain; charset=x-user-defined" );
             //$("#wait").css("display", "block");
         },
         success: function(data) {
             $("#room1").empty();
             $("#room1").append($("<option></option>").attr("value", "").text(translations.work_order.forms.place_holder.Choose_Room));
             $.each(data, function (key, value) {
                $("#room1").append($("<option></option>").attr("value", value.id+',,,'+value.room).text(value.room));
             });
         }
     });
 });
 $('#qr_url3').attr('required', false);

 $(document).on('change', '#asset_name_id1', function(e) {
    var property_name = '{{$data['builiding_name']}}';
    var asset_name = $("#asset_name_id1").find(":selected").attr("data");
    var asset_number = $("#asset_number1").val();

    var asset_tag_name = asset_name+asset_number;
    $("#asset_tag_data1").val(asset_tag_name);
 });

 $(document).on('keyup mouseup', '#asset_number1', function() {
    var property_name = '{{$data['builiding_name']}}';
    var asset_name = $("#asset_name_id1").find(":selected").attr("data") == undefined || '' ? '' : $("#asset_name_id1").find(":selected").attr("data");
    var asset_number = $("#asset_number1").val() == undefined || '' ? '' : $("#asset_number1").val();
     var asset_tag_name = asset_name+asset_number;
    if (document.getElementById('noAssetSymbolEdit').checked) {
        var asset_tag_name = asset_number;
    }
    $("#asset_tag_data1").val(asset_tag_name);
});
$(".date-picker").datepicker({
  maxDate:  new Date(),

  dateFormat: 'dd-mm-yy',
})

function saveandnext() {
    document.getElementById("submittype").value = "save-and-next";
}
var AttachmentArray1 = [];
var arrCounter1 = 0;
var filesCounterAlertStatus1 = false;

//un ordered list to keep attachments thumbnails
var ul = document.createElement("ul");
ul.className = "thumb-Images";
ul.id = "imgList1";


 var damageImages = <?php echo json_encode($data['damage_images']); ?>;
document.previous_damage_files = damageImages;
document.total_asset_damage_files1 = damageImages;
document.total_asset_damage_files_length1 = document.total_asset_damage_files1.length;
$('#damage_images1').val(document.total_asset_damage_files1.join(','))

console.log(document.total_asset_damage_files1)
//the handler for file upload event
$(document).on('change', '#file_upload3', function(e) {

  //to remove li tag

  //to make sure the user select file/files
  if (!e.target.files) return;

  //To obtaine a File reference
  var files = e.target.files;
  var file_size_error=false;
    var file_type_error=false;
  // Loop through the FileList and then to render image files as thumbnails.
  for (var i = 0, f; (f = files[i]); i++) {


    document.total_asset_damage_files_length1++
    if(document.total_asset_damage_files_length1 <= 3){
        //instantiate a FileReader object to read its contents into memory
        var file_size_in_kb=(files[i].size/10048);
            var file_type= files[i].type;
            var file_name= files[i].name;
            if(file_size_in_kb>10048){
                file_size_error=true;
            }
            var supported_types=['image/jpeg','image/png','image/jpg','application/pdf'];

            if(!supported_types.includes(file_type)){
                file_type_error=true;
            }

            if(file_size_error==true || file_type_error==true){
                // reset($('#file_upload'));

                // removeFile()

                var error_message='';

                if(file_size_error==true && file_type_error==true){
                    error_message =  translations.general_sentence.validation.Please_upload_only_jpg_jpeg_png_image_of_max_size_10mb;
                }else if(file_size_error==true && file_type_error==false){
                    error_message = translations.general_sentence.validation.File_size_should_not_be_more_than_10_mb;
                }else{
                    error_message = translations.general_sentence.validation.Please_upload_only_jpg_jpeg_png_imagezip;
                }

                swal({
                    title: error_message,
                    // text: ,
                    icon: "warning",
                    buttons: true,
                    dangerMode: true,
                    // showCancelButton: true,
                    confirmButtonText:  translations.general_sentence.button_and_links.ok,
                    // cancelButtonText:  translations.general_sentence.button_and_links.ok,

                    buttons: [
                        translations.general_sentence.button_and_links.ok,
                        // translations.general_sentence.button_and_links.ok
                                    ],

                })
                // swal();

                return false;
            }

        var data = new FormData($('#asset_edit_form')[0]);
                data.append('damage_files1',  files[i])
                data.append('triggered_from',  'damage_files')
                data.append('previous_damage_files',  document.previous_damage_files)


                    $.ajax({
                        url: $('#asset_documents_url').val(),
                        dataType: 'json',
                        headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        cache: false,
                        enctype: 'multipart/form-data',
                        contentType: false,
                        processData: false,
                        data: data,
                        type: 'post',
                        success: function(res){
                            document.total_asset_damage_files1=res;
                            console.log('total_asset_damage_files1'+document.total_asset_damage_files1)
                            $('#damage_images1').val(document.total_asset_damage_files1.join(','))

                        }
                    });

    }


    var fileReader = new FileReader();
    // Closure to capture the file information and apply validation.
    fileReader.onload = (function(readerEvt) {
      return function(e) {
        //console.log(readerEvt.type);

        //Apply the validation rules for attachments upload
        ApplyFileValidationRules1(e, readerEvt);

        CheckFileType1(readerEvt.type);


        //Render attachments thumbnails.

        //Check the file count
        if($('#imgList1 li').length >= 3) {

            toastr.error(
                translations.work_order.common.max_3_images,
            { timeOut: 5000,
                positionClass: "toast-top-center",
                preventDuplicates: true,
                preventOpenDuplicates: true, }
            );
          $('.custom-file1').hide();

          $('.max-file-choseen-error1').show();
        } else {

            RenderThumbnail1(e, readerEvt);

          $('.max-file-choseen-error1').hide();
          $('.custom-file1').show();

        }


        //Fill the array of attachment
        FillAttachmentArray1(e, readerEvt);
      };
    })(f);
    fileReader.readAsDataURL(f);
  }

})
    var qrcode5 = new QRCode(document.getElementById("previousAssetQr"),
    {
    text: document.getElementById("previousAssetQr").dataset.url
    ,
    width: 100,
    height: 100,
    colorDark : "#000000",
    colorLight : "#ffffff",
    correctLevel : QRCode.CorrectLevel.H
    });

//To remove attachment once user click on x button
jQuery(function($) {
  $("div").on("click", ".img-wrap .close", function() {
    $('.custom-file1').show();
    var id = $(this)
      .closest(".img-wrap")
      .find("img")
      .data("id");

    //to remove the deleted item from array
    var elementPos = AttachmentArray.map(function(x) {
      return x.FileName;
    }).indexOf(id);
    if (elementPos !== -1) {
      AttachmentArray.splice(elementPos, 1);
    }
    document.total_asset_damage_files1.splice(elementPos, 1);
    document.total_asset_damage_files_length1--
    console.log(document.total_asset_damage_files1)
    $('#damage_images1').val(document.total_asset_damage_files1.join(','))

    //to remove image tag
    $(this)
      .parent()
      .find("img")
      .not()
      .remove();

    //to remove div tag that contain the image
    $(this)
      .parent()
      .find("div")
      .not()
      .remove();

    //to remove div tag that contain caption name
    $(this)
      .parent()
      .parent()
      .find("div")
      .not()
      .remove();

    //to remove li tag
    var lis = document.querySelectorAll("#imgList1 li");

    for (var i = 0; (li = lis[i]); i++) {
      if (li.innerHTML == "") {
        li.parentNode.removeChild(li);
      }
    }
  });
});

//Apply the validation rules for attachments upload
function ApplyFileValidationRules1(e, readerEvt) {

  //To check files count according to upload conditions
  if (CheckFilesCount1(AttachmentArray) == false) {
    if (!filesCounterAlertStatus1) {
      filesCounterAlertStatus1 = true;
      //swal(translations.general_sentence.validation.max_3_pictures);
      toastr.error(
        translations.work_order.common.max_3_images,
          { timeOut: 5000,
            positionClass: "toast-top-center",
            preventDuplicates: true,
                preventOpenDuplicates: true, }
        );
    }
    e.preventDefault();
    return;
  }
}

//To check file type according to upload conditions
function CheckFileType1(fileType) {
    if(fileType == "application/pdf"){
        return true;

    }
  if (fileType == "image/jpeg"  ) {
    return true;
  } else if (fileType == "image/png") {
    return true;
  } else if (fileType == "image/gif") {
    return true;
  }

  else {
    toastr.error(
      translations.general_sentence.validation.Please_upload_only_jpg_jpeg_png_image,
      { timeOut: 5000 }
    );
    return false;
  }
  return true;
}

//To check file Size according to upload conditions
function CheckFileSize1(fileSize) {
  if (fileSize < 300000) {
    return true;
  } else {
    return false;
  }
  return true;
}

//To check files count according to upload conditions
function CheckFilesCount1(AttachmentArray) {
  //Since AttachmentArray.length return the next available index in the array,
  //I have used the loop to get the real length
  var len = 0;
  for (var i = 0; i < AttachmentArray.length; i++) {
    if (AttachmentArray[i] !== undefined) {
      len++;
    }
  }

  //To check the length does not exceed 10 files maximum

  if (len > 2) {
    $('.custom-file1').hide();
    return false;
  } else {
    return true;
  }
}

//Render attachments thumbnails.
function RenderThumbnail1(e, readerEvt) {
    var pdfImage = '<?= asset('img/pdf.png')?>';

  var li = document.createElement("li");

  ul.appendChild(li);
  if(readerEvt.name.split(".").includes('pdf')){
     li.innerHTML = [
    '<div class="img-wrap"> <span class="close">&times;</span>' +
      '<img class="thumb" src="',
    pdfImage,
    '" title="',
    escape(readerEvt.name),
    '" data-id="',
    readerEvt.name,
    '"/>' + "</div>"
  ].join("");
  }
  else{
    li.innerHTML = [
    '<div class="img-wrap"> <span class="close">&times;</span>' +
      '<img class="thumb" src="',
    e.target.result,
    '" title="',
    escape(readerEvt.name),
    '" data-id="',
    readerEvt.name,
    '"/>' + "</div>"
  ].join("");
  }


  var div = document.createElement("div");
  div.className = "FileNameCaptionStyle";
  li.appendChild(div);
  div.innerHTML = [readerEvt.name].join("");
  document.getElementById("Filelist").insertBefore(ul, null);
}

//Fill the array of attachment
function FillAttachmentArray1(e, readerEvt) {
  AttachmentArray[arrCounter1] = {
    AttachmentType: 1,
    ObjectType: 1,
    FileName: readerEvt.name,
    FileDescription: "Attachment",
    NoteText: "",
    MimeType: readerEvt.type,
    Content: e.target.result.split("base64,")[1],
    FileSizeInBytes: readerEvt.size
  };
  arrCounter1 = arrCounter1 + 1;
}
$(document).on('change', '#asset_status1', function(e) {
if(this.value=='damaged'){
    $('#date_of_damage1').prop('disabled', false)
    $('.asset_damge_files_section1').removeClass('d-none')

}
else{
    $('#date_of_damage1').prop('disabled', true)
    $('.asset_damge_files_section1').addClass('d-none')

}
})
$(".view-img div.wo-img").click(function(){ //alert();
    $(".close-img").show();
    $(".image-zoom").fadeIn().addClass("d-flex");
    $(".image-zoom div").removeClass("w-100");
    // alert('#qr-'+this.id+'');
//   var $img = $(this).clone();

//   $img.children().attr("width", 250)
//   $img.children().attr("height", 250)
  //console.log($img);
  $('.image-zoom').append('<div id="testzoom"></div>');
  let qrcode6 = new QRCode(document.getElementById("testzoom"),
    {
    text: document.getElementById("previousAssetQr").dataset.url
    ,
    width: 250,
    height: 250,
    colorDark : "#000000",
    colorLight : "#ffffff",
    correctLevel : QRCode.CorrectLevel.H
    });

  $('.image-zoom').find(".view-img").removeClass("view-img");
});

$('#noAssetSymbolEdit').change(function() {
var asset_name = $("#asset_name_id1").find(":selected").attr("data");
var asset_number = $("#asset_number1").val();
if(this.checked) {
var asset_tag_name = asset_number;
}
else{
var asset_tag_name = asset_name+asset_number;
}
$("#asset_tag_data1").val(asset_tag_name);
});

 </script>
 <script>
    feather.replace();
    $("#asset_status1").select2();
</script>
