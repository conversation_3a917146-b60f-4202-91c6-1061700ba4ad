<?php


     /*
    |--------------------------------------------------------------------------
    | Configration Service Type module
    |--------------------------------------------------------------------------
    |
    | language translate for create, edit , list and delte
    | english verion file
    | update@sobhon
    |
     */

return [

    'workorder_trigger'=> [
        'workorder_created'=>'Work Order Created',
        'workorder_closed'=>'Work Order Closed',
        'workorder_status_updates' => 'Work Order Status Update',
        'maintenance_workorder_created' => 'Maintenance Request Received',
        'workorder_deleted' => 'Work Order Deleted',
    ],

    'maintanence_type'=> [
        'hard_service'=>'Hard Service',
        'soft_service'=>'Soft Service'
    ],

    'workorder_type'=> [
        'reactive_maintenance'=>'Reactive Maintenance',
        'preventive_maintenance'=>'Preventive Maintenance'
    ],

    'common'=> [
        /*'asset_categories'=>'Asset Categories',*/
        'asset_categories'=>'Service Type',
        'asset_name'=>'Asset Name',
        'email_triggers' => 'Email Triggers',
        'email_escalation' => 'Email Escalation',
        'email_escalation_desc' => 'Here you can find and define new email escalation notification to be sent to specific user types when execution rate or response time KPIs are breached.',
        'email_triggers_desc' => 'Here you can find and define new email triggers to be sent to specific user types. Whether a work order has been created, closed, a status got updated, or maintenance request has been submitted, selected user types will receive an email notification.',
    ],

    'comminucation_table' => [
        'user_type'=>'Send email to',
        'notify_when'=>'When',
        'work_order_type'=>'Work Order Type',
        'Frequency' => 'Frequency',
        'Priority' => 'Priority',
        'PassFailKpiBreached' => 'Execution time KPI Breached',
        'ResponseTimeKPIBreached' => 'Response Time KPI Breached',
        'WO-Created' => 'WO Created',
        'WO-Closed' => 'WO Closed',
        'WO-Status-Updated' => 'WO Status Updated',
        'MWO-Created' => 'MR Received',
        'WO-Deleted' => 'WO Deleted',
        'Select One' => 'Select One',
        'Seconds' => 'Seconds',
        'Minutes' => 'Minutes',
        'Hours' => 'Hours',
        'Days' => 'Days',

    ],

    'asset_categories_bread_crumbs'=>[
        'configration'=>'Configuration',
        'assets'=>'Services & Assets',
        'new_asset_category'=>'Add Service Type',
        'edit_asset_category'=>'Edit Service Type',
        'add_new_email_trigger' => 'Add New Email Trigger',
        'add_new_email_escalation' =>  'Add New Email Escalation',
        'edit_email_trigger'=>'Edit Email Trigger',
    ],

    'assets_button'=>[
        'add_new_asset_category'=>'Add New Service Type',
        'cancel'=>'Cancel',
        'create'=>'Create',
        'add_new_asset_name'=>'Add New Asset Name',
        'update'=>'Update',
        'add_new_user' => 'Add New User'
    ],

    'asset_name_bread_crumbs'=>[
        'configration'=>'Configuration',
        'assets'=>'Services & Assets',
        'new_asset_name'=>'New Asset Name',
        'edit_asset_name'=>'Edit Asset Name',
        
        
    ],

    'asset_name_table'=>[
        'asset_name'=>'Asset Name',
        'asset_category'=>'Service Type',
        'symbol'=>'Symbol',
        'action'=>'Action'
    ],
   
    'asset_categories_table'=>[
        'asset_category'=>'Service Type',
        'service_type'=>'Service',
        'priority'=>'Priority',
        'action'=>'Action',
        'high'=>'High',
        'medium'=>'Medium',
        'low'=>'Low',
        'hard_service'=>'Hard Service',
        'soft_service'=>'Soft Service'
    ],


    /**-------------------------forms fields--------------- */

    'asset_categories_forms' => [

        'label' => [
            'asset_category'=>'Service Type',
            'service_type'=>'Service Type',
            'priority'=>'Priority',
            'select_one' => 'Please Choose',
            'would_you_like_to_add_this_service'=>'Would you like to add this service to current contracts and users?',
            'assign_to_contract'=>'Assign to Contracts',
            'select_contracts'=>'Select Contracts',
            'assign_to_bm'=>'Assign to Building Managers',
            'assign_to_bme'=>'Assign to Building Managers Employees (Optional)',
            'select_bm'=>'Select Building Managers',
            'select_bme'=>'Select Building Managers Employees',
            'assign_to_sps'=>'Assign to Service Provider Supervisors (Optional)',
            'select_supervisor'=>'Select Supervisors',
        ],

        'place_holder' => [
           'asset_category'=>'Service Type'
            
        ],
    ],

    'asset_comminucation_forms' => [
        'label' => [
            'user_type'=>'Send email to',
            'notification_trigger'=>'When',
            'maintanence_type' => 'Maintanence Type',
            'workorder_type' => 'Work Order Type',
            'frequency' => 'Preventive Maintenance Frequency',
            'priority'=>'Reactive Maintenance Priority',
            'notification_esalation' => 'Send notification email when',
            'Pass_Fail_KPI_Breached' => 'After (Execution time) KPI Breached',
            'Response_Time_KPI_Breached' => 'After (Response Time) KPI Breached',
            'Arabic' => 'Arabic',
            'email_language' => 'Email language',
            'rert_language' => 'Report language',

            'email_language_desc' => 'What would you like the language of email content to be?',
        ],

        'place_holder' => [
           'asset_category'=>'Service Type'
            
        ],
    ],


    'asset_name_forms' => [

        'label' => [
           'asset_name'=>'Asset Name',
           'asset_category'=>'Service Type',
           'asset_symbol'=>'Asset Symbol',
        ],

        'place_holder' => [
            'asset_name'=>'Asset Name',
            'asset_category'=>'Choose',
            'asset_symbol'=>'Asset Symbol'
        ],
    ],

        /**-----------------validation------------------------------- */

        'asset_categories_validation' => [
            'duplicate_service_type' => 'Please enter a unique service type'
        ],

        'asset_name_validation'=>[
            'duplicate_asset_name' => 'Please enter a unique asset name'
        ],

    ];




      
      
      
?>