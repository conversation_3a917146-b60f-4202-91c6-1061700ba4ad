@extends('layouts.app')
@section('styles')
    <link rel = "stylesheet" href = "{{ asset('new_theme/css/import.css') }}">
@endsection
@section('content')
    <div class = "contents">
        <div class = "container-fluid">
            <div class = "social-dash-wrap">
                <div class = "row">
                    <div class = "col-lg-12">
                        <div class = "breadcrumb-main">
                            <h4 class = "text-capitalize breadcrumb-title">
                                <a href = "{{ route('bulk-import.openUploadFile') }}" class = "text-dark">
                                    <i class = "las la-arrow-left"></i>
                                </a>
                                @lang('import.import_project_assets')
                            </h4>
                            <div class = "breadcrumb-action justify-content-center flex-wrap"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class = "container-fluid">
            <div class = "checkout wizard1 wizard7 global-shadow px-sm-50 px-20 py-sm-50 py-30 mb-30 bg-white radius-xl w-100">
                <div class = "row justify-content-center">
                    <div class = "col-xl-8">
                        <div class = "checkout-progress-indicator content-center col-md-10">
                            <div class = "checkout-progress">
                                <div class = "step completed" id = "1">
                                    <span class = "las la-check"></span>
                                    <span>@lang('import.upload')</span>
                                </div>
                                 <div class = "current">
                                    <img src = "{{ asset('img/svg/checkout.svg') }}" class = "svg">
                                </div>
                                <div class = "step current" id = "2">
                                    <span>2</span>
                                    <span>@lang('import.map')</span>
                                </div>
                                   <div class = "current">
                                    <img src = "{{ asset('img/svg/checkout.svg') }}" class = "svg">
                                </div>
                                <div class = "step" id = "3">
                                    <span>3</span>
                                    <span>@lang('import.validate')</span>
                                </div>
                                <div class = "current">
                                    <img src = "{{ asset('img/svg/checkout.svg') }}" class = "svg">
                                </div>
                                <div class = "step" id = "4">
                                    <span>4</span>
                                    <span>@lang('import.done')</span>
                                </div>
                            </div>
                        </div>        
                        <div class = "row justify-content-center">
                            <div class = "col-xl-10 col-lg-8 col-sm-10">
                                <div class = "card checkout-shipping-form pt-2 pb-30 border-0">
                                    <div class = "row">
                                        <div class = "col-6">
                                                <div class = "form-group">
                                                    <label for = "name1">
                                                        @lang("data_properties.property_forms.label.project") 
                                                        <span class = "required">*</span>
                                                    </label>
                                                    <input type = "text" class = "form-control" name = "projectName" id = "projectName" value = "{{ $projectName }}" disabled>
                                                </div>
                                            </div>
                                            <div class = "col-6">
                                                <div class = "form-group">
                                                    <label for = "name1">
                                                        @lang("import.connected_user") 
                                                        <span class = "required">*</span>
                                                    </label>
                                                    <input type = "text" class = "form-control" name = "user" id = "user" value = "{{ $userName }}" disabled>
                                                </div>
                                            </div>
                                            <div class = "col-6">
                                                <div class = "form-group">
                                                    <label for = "name1">
                                                        @lang("import.filename_title") 
                                                        <span class = "required">*</span>
                                                    </label>
                                                    <input type = "text" class = "form-control" name = "filename" id = "filename" value = "{{ isset($bulkImportDetails) ? $bulkImportDetails->file_name : '-' }}" disabled>
                                                </div>
                                            </div>
                                            <div class = "col-6">
                                                <div class = "form-group">
                                                    <label for = "name1">
                                                        @lang("import.filesize_title") 
                                                        <span class = "required">*</span>
                                                    </label>
                                                    <input type = "text" class = "form-control" name = "filesize" id = "filesize" value = "{{ isset($bulkImportDetails) ? $bulkImportDetails->file_size : '-' }}" disabled>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class = "mb-3">
                    <div class = "d-flex justify-content-sm-between mb-3">
                        <ul class = "nav nav-tabs site-pills bg-white p-1 rounded mb-3 mb-sm-0" id = "myTab" role = "tablist">
                            <li class = "nav-item" role = "presentation">
                                <button class = "nav-link rounded {{ isset($bulkImportDetails) && $bulkImportDetails->users_import ? 'active' : '' }} {{ isset($bulkImportDetails) && !$bulkImportDetails->users_import ? 'not-allowed' : '' }}" id = "users-tab" data-toggle = "tab" data-target = "#users-livewire" type = "button" role = "tab" aria-controls = "users" aria-selected = "true" {{ isset($bulkImportDetails) && !$bulkImportDetails->users_import ? 'disabled' : '' }}>@lang('import.users_label')</button>
                            </li>
                            <li class = "nav-item" role = "presentation">
                                <button class = "nav-link rounded {{ isset($bulkImportDetails) && !$bulkImportDetails->users_import && $bulkImportDetails->priorities_import ? 'active' : '' }} {{ isset($bulkImportDetails) && !$bulkImportDetails->priorities_import ? 'not-allowed' : '' }}" id = "priorities-tab" data-toggle = "tab" data-target = "#priorities-livewire" type = "button" role = "tab" aria-controls = "priorities" aria-selected = "true" {{ isset($bulkImportDetails) && !$bulkImportDetails->priorities_import ? 'disabled' : '' }}>@lang('import.priorities_label')</button>
                            </li>
                            <li class = "nav-item" role = "presentation">
                                <button class = "nav-link rounded {{ isset($bulkImportDetails) && !$bulkImportDetails->users_import && !$bulkImportDetails->priorities_import && $bulkImportDetails->services_import ? 'active' : '' }} {{ isset($bulkImportDetails) && !$bulkImportDetails->services_import ? 'not-allowed' : '' }}" id = "services-tab" data-toggle = "tab" data-target = "#services-livewire" type = "button" role = "tab" aria-controls = "services" aria-selected = "true" {{ isset($bulkImportDetails) && !$bulkImportDetails->services_import ? 'disabled' : '' }}>@lang('import.services_label')</button>
                            </li>
                            <li class = "nav-item" role = "presentation">
                                <button class = "nav-link rounded {{ isset($bulkImportDetails) && !$bulkImportDetails->users_import && !$bulkImportDetails->priorities_import && !$bulkImportDetails->services_import && $bulkImportDetails->properties_import ? 'active' : '' }} {{ isset($bulkImportDetails) && !$bulkImportDetails->properties_import ? 'not-allowed' : '' }}" id = "properties-tab" data-toggle = "tab" data-target = "#properties-livewire" type = "button" role = "tab" aria-controls = "properties" aria-selected = "true" {{ isset($bulkImportDetails) && !$bulkImportDetails->properties_import ? 'disabled' : '' }}>@lang('import.properties_label')</button>
                            </li>
                            <li class = "nav-item" role = "presentation">
                                <button class = "nav-link rounded {{ isset($bulkImportDetails) && !$bulkImportDetails->users_import && !$bulkImportDetails->priorities_import && !$bulkImportDetails->services_import && !$bulkImportDetails->properties_import && $bulkImportDetails->buildings_import ? 'active' : '' }} {{ isset($bulkImportDetails) && !$bulkImportDetails->buildings_import ? 'not-allowed' : '' }}" id = "buildings-tab" data-toggle = "tab" data-target = "#buildings-livewire" type = "button" role = "tab" aria-controls = "buildings" aria-selected = "true" {{ isset($bulkImportDetails) && !$bulkImportDetails->buildings_import ? 'disabled' : '' }}>@lang('import.property_buildings_label')</button>
                            </li>
                            <li class = "nav-item" role = "presentation">
                                <button class = "nav-link rounded {{ isset($bulkImportDetails) && !$bulkImportDetails->users_import && !$bulkImportDetails->priorities_import && !$bulkImportDetails->services_import && !$bulkImportDetails->properties_import && !$bulkImportDetails->buildings_import && $bulkImportDetails->assets_import ? 'active' : '' }} {{ isset($bulkImportDetails) && !$bulkImportDetails->assets_import ? 'not-allowed' : '' }}" id = "assets-tab" data-toggle = "tab" data-target = "#assets-livewire" type = "button" role = "tab" aria-controls = "assets" aria-selected = "true" {{ isset($bulkImportDetails) && !$bulkImportDetails->assets_import ? 'disabled' : '' }}>@lang('import.assets_label')</button>
                            </li>
                        </ul>
                    </div>
                    <div class = "card card-md mb-4">
                        <div class = "card-body">
                            <div class = "tab-content" id = "myTabContent">
                                <div class = "tab-pane fade {{ isset($bulkImportDetails) && $bulkImportDetails->users_import ? 'show active' : '' }}" id = "users-livewire" role = "tabpanel" aria-labelledby = "users-tab">
                                    @livewire('bulk-import.new.mapping.users-mapping', ['showMore' => $showMore, 'perPage' => $perPage, 'chunkData' => $chunkData, 'projectUserId' => $projectUserId])
                                </div>
                                <div class = "tab-pane fade {{ isset($bulkImportDetails) && !$bulkImportDetails->users_import && $bulkImportDetails->priorities_import ? 'show active' : '' }}" id = "priorities-livewire" role = "tabpanel" aria-labelledby = "priorities-tab">
                                    @livewire('bulk-import.new.mapping.priorities-mapping', ['showMore' => $showMore, 'perPage' => $perPage, 'chunkData' => $chunkData, 'projectUserId' => $projectUserId])
                                </div>
                                <div class = "tab-pane fade {{ isset($bulkImportDetails) && !$bulkImportDetails->users_import && !$bulkImportDetails->priorities_import && $bulkImportDetails->services_import ? 'show active' : '' }}" id = "services-livewire" role = "tabpanel" aria-labelledby = "services-tab">
                                    @livewire('bulk-import.new.mapping.services-mapping', ['showMore' => $showMore, 'perPage' => $perPage, 'chunkData' => $chunkData, 'projectUserId' => $projectUserId])
                                </div>
                                <div class = "tab-pane fade {{ isset($bulkImportDetails) && !$bulkImportDetails->users_import && !$bulkImportDetails->priorities_import && !$bulkImportDetails->services_import && $bulkImportDetails->properties_import ? 'show active' : '' }}" id = "properties-livewire" role = "tabpanel" aria-labelledby = "properties-tab">
                                    @livewire('bulk-import.new.mapping.properties-mapping', ['showMore' => $showMore, 'perPage' => $perPage, 'chunkData' => $chunkData, 'projectUserId' => $projectUserId])
                                </div>
                                <div class = "tab-pane fade {{ isset($bulkImportDetails) && !$bulkImportDetails->users_import && !$bulkImportDetails->priorities_import && !$bulkImportDetails->services_import && !$bulkImportDetails->properties_import && $bulkImportDetails->buildings_import ? 'show active' : '' }}" id = "buildings-livewire" role = "tabpanel" aria-labelledby = "buildings-tab">
                                    @livewire('bulk-import.new.mapping.property-buildings-mapping', ['showMore' => $showMore, 'perPage' => $perPage, 'chunkData' => $chunkData, 'projectUserId' => $projectUserId])
                                </div>
                                <div class = "tab-pane fade {{ isset($bulkImportDetails) && !$bulkImportDetails->users_import && !$bulkImportDetails->priorities_import && !$bulkImportDetails->services_import && !$bulkImportDetails->properties_import && !$bulkImportDetails->buildings_import && $bulkImportDetails->assets_import ? 'show active' : '' }}" id = "assets-livewire" role = "tabpanel" aria-labelledby = "assets-tab">
                                    @livewire('bulk-import.new.mapping.assets-mapping', ['showMore' => $showMore, 'perPage' => $perPage, 'chunkData' => $chunkData, 'projectUserId' => $projectUserId])
                                </div>
                            </div>
                            @livewire('bulk-import.new.progress-jobs', [
                                'usersCheck' => isset($bulkImportDetails) ? $bulkImportDetails->users_import : false,
                                'prioritiesCheck' => isset($bulkImportDetails) ? $bulkImportDetails->priorities_import : false,
                                'servicesCheck' => isset($bulkImportDetails) ? $bulkImportDetails->services_import : false,
                                'propertiesCheck' => isset($bulkImportDetails) ? $bulkImportDetails->properties_import : false,
                                'buildingsCheck' => isset($bulkImportDetails) ? $bulkImportDetails->buildings_import : false,
                                'assetsCheck' => isset($bulkImportDetails) ? $bulkImportDetails->assets_import : false,
                                'projectId' => $projectId, 
                                'bulkImportDetailsId' => isset($bulkImportDetails) ? $bulkImportDetails->id : null,
                                'projectUserId' => $projectUserId,
                                'userId' => $userId,
                                'userName' => $userName,
                                'token' => $token
                            ])
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@section('scripts')
    <script src = "{{ asset('new_theme/js/jquery.min.js') }}"></script>
    <script src = "{{ asset('new_theme/js/functions.js') }}"> </script>
    <script>
        $('#show-action-btn').on('click', function() {
            manageShowMoreTable();
        });

        $('#btn-next-map').on('click', function() {
            openConfirmModalForMapView();
        });
    </script>
@endsection