<div wire:ignore.self class="modal fade new-popup" id="projectSettingModal" tabindex="-1" role="dialog"
    aria-labelledby="createModalLabel" aria-modal="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content radius-xl">
            <div class="modal-header">
                <h6 class="modal-title" id="createModalLabel">@lang('CRMProjects.common.sharedprojectsettings')


                </h6>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i class="iconsax" icon-name="x"></i>
                </button>
            </div>

            <form wire:submit.prevent="saveSettings">
                <div class="modal-body">
                    


                <div class="table-responsive">
                <table class="table mb-0">
                <thead class="thead-light">
                <tr>
                <th> @lang('CRMProjects.common.name')</th>
                <th class="text-right"> @lang('CRMProjects.common.off_on')</th>
                </tr>
                </thead>
                <tbody>
                <tr>
                <td> @lang('CRMProjects.common.basic_details')</td>
                <td class="action text-right">
                <label class="switch mr-3">
                <input type="checkbox" wire:model="settingsState.basic_details" name="basic_details">
                <span class="slider round"></span>
                </label>
                </td>
                </tr>
                <tr>
                <td> @lang('CRMProjects.common.member')</td>
                <td class="action text-right">
                <label class="switch mr-3">
                <input type="checkbox" wire:model="settingsState.member" name="member">    
                    
                <span class="slider round"></span>
                </label>   
                </td>
                </tr>
                <tr>
                <td> @lang('CRMProjects.common.client')</td>
                <td class="action text-right">
                <label class="switch mr-3">
                    <input type="checkbox" wire:model="settingsState.client" name="client"><span class="slider round"></span>
                </label>   
                </td>
                </tr>
                <tr>
                <td> @lang('CRMProjects.common.vendor')</td>
                <td class="action text-right">
                <label class="switch mr-3">
                    <input type="checkbox" wire:model="settingsState.vendor" name="vendor">    
                <span class="slider round"></span>
                </label>   
                </td>
                </tr>
                <tr>
                <td> @lang('CRMProjects.common.milestone')</td>
                <td class="action text-right">
                <label class="switch mr-3">
                    <input type="checkbox" wire:model="settingsState.milestone" name="milestone">    
                <span class="slider round"></span>
                </label>   
                </td>
               
                </tr>
                        <tr>
                <td>@lang('CRMProjects.common.activity')</td>
                <td class="action text-right">
                <label class="switch mr-3">
                    <input type="checkbox" wire:model="settingsState.activity" name="activity">    
                <span class="slider round"></span>
                </label>   
                </td>
                </tr>
                    <tr>
                <td>@lang('CRMProjects.common.attachment')</td>
                <td class="action text-right">
                <label class="switch mr-3">
                    <input type="checkbox" wire:model="settingsState.attachment" name="attachment">
                <span class="slider round"></span>
                </label>   
                </td>
               
                </tr>
                <tr>
                <td>@lang('CRMProjects.common.task')</td>
                <td class="action text-right">
                <label class="switch mr-3">
                    <input type="checkbox" wire:model="settingsState.task" name="task">
                <span class="slider round"></span>
                </label>   
                </td>
                </tr>
                <tr>
                <td>@lang('CRMProjects.common.bug_report')</td>
                <td class="action text-right">
                <label class="switch mr-3">
                    <input type="checkbox" wire:model="settingsState.bug_report" name="bug_report">
                <span class="slider round"></span>
                </label>   
                </td>
                </tr>
                <tr>
                <td>@lang('CRMProjects.common.invoice')</td>
                 <td class="action text-right">
                <label class="switch mr-3">
                    <input type="checkbox" wire:model="settingsState.invoice" name="invoice">
                <span class="slider round"></span>
                </label>   
                </td>
               
                </tr>
                        <tr>
                <td>@lang('CRMProjects.common.bill')</td>
                 <td class="action text-right">
                <label class="switch mr-3">
                    <input type="checkbox" wire:model="settingsState.bill" name="bill">
                <span class="slider round"></span>
                </label>   
                </td>
             
                </tr>

                        <tr>
                <td>@lang('CRMProjects.common.timesheet')</td>
                 <td class="action text-right">
                <label class="switch mr-3">
                    <input type="checkbox" wire:model="settingsState.timesheet" name="timesheet">
                <span class="slider round"></span>
                </label>   
                </td>
    
                </tr>
                                        <tr>
                <td>@lang('CRMProjects.common.documents')</td>
                <td class="action text-right">
                <label class="switch mr-3">
                    <input type="checkbox" wire:model="settingsState.documents" name="documents">
                <span class="slider round"></span>
                </label>   
                </td>
              
                </tr>
                    <tr>
                <td>@lang('CRMProjects.common.progress')</td>
                <td class="action text-right">
                <label class="switch mr-3">
                    <input type="checkbox" wire:model="settingsState.progress" name="progress">
                <span class="slider round"></span>
                </label>   
                </td>
                
                </tr>
                <tr>
                <td>@lang('CRMProjects.common.password_protected')</td>
                <td class="action text-right">
                <label class="switch mr-3">
                    <input type="checkbox"id="password_protected" wire:model="settingsState.password_protected" name="password_protected">
                <span class="slider round"></span>
                </label>   
                </td>

              
                </tr>
                <tr class="{{ ($settingsState['password_protected'] == 1) ? '' : 'd-none' }}">
            <td>
            <div class="action input-group input-group-merge text-left">
                <input type="password" wire:model="settingsState.password" class="form-control" placeholder="Enter Your Password">
               <!--  <div class="input-group-append">
                    <span class="input-group-text">
                        <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="eye-slash"></i>
                    </span>
                </div> -->
            </div>
        </td>

                </tr>

                        <tr>
                <td>@lang('CRMProjects.common.retainer')</td>
                 <td class="action text-right">
                <label class="switch mr-3">
                    <input type="checkbox" wire:model="settingsState.retainer" name="retainer">
                <span class="slider round"></span>
                </label>   
                </td>
            
                </tr>
                    <tr>
                <td>@lang('CRMProjects.common.proposal')</td>
                 <td class="action text-right">
                <label class="switch mr-3">
                    <input type="checkbox" wire:model="settingsState.proposal" name="proposal">
                <span class="slider round"></span>
                </label>   
                </td>
              
                </tr>
                        <tr>
                <td>@lang('CRMProjects.common.procurement')</td>
                 <td class="action text-right">
                <label class="switch mr-3">
                    <input type="checkbox" wire:model="settingsState.procurement" name="procurement">
                <span class="slider round"></span>
                </label>   
                </td>
              
                </tr>
                </tbody>
                </table>
                </div>


                </div>
                 <div class="modal-footer">
                    <button type="button" class="btn bg-loss text-white radius-xl" data-dismiss="modal">@lang('CRMProjects.common.close')</button>
                    <button type="submit" class="btn bg-new-primary radius-xl">@lang('CRMProjects.common.submit')</button>
                </div>
            </form>
        </div>
    </div>
</div>
<script src="//cdnjs.cloudflare.com/ajax/libs/jquery/2.1.3/jquery.min.js"></script>
<script>
   
    $(document).ready(function() {
        // if ($('.password_protect').is(':checked')) {
        //     $('.passwords').show();
        // } else {
        //     $('.passwords').hide();
        // }
        // $('#password_protected').on('change', function() {
        //     if ($('.password_protect').is(':checked')) {
        //         $('.passwords').show();
        //     } else {
        //         $('.passwords').hide();
        //     }
        // });
    });
    $(document).on('change', '#password_protected', function() {
        if ($(this).is(':checked')) {
            $('.passwords').removeClass('password_protect');
            $('.passwords').attr("required", true);
        } else {
            $('.passwords').addClass('password_protect');
            $('.passwords').val(null);
            $('.passwords').removeAttr("required");
        }
    });


     window.addEventListener('close-modal', event => {
        $('#'+event.detail.id).modal('hide');
    });


</script>
<script>
    const togglePassword = document.querySelector("#togglePassword");
    const password = document.querySelector("#password");

    togglePassword.addEventListener("click", function() {
        // toggle the type attribute
        const type = password.getAttribute("type") === "password" ? "text" : "password";
        password.setAttribute("type", type);
        this.classList.toggle("eye");
        this.classList.toggle("eye-slash");
    });


</script>

<!-- <script>
        window.addEventListener('show-toastr', event => {
            toastr.options = {
                "closeButton": true,
                "progressBar": true,
                "positionClass": "toast-top-center",
                "timeOut": "3000"
            };
            if (event.detail.type === 'success') {
                toastr.success(event.detail.message);
            } else if (event.detail.type === 'error') {
                toastr.error(event.detail.message);
            }
        });
    </script>  -->