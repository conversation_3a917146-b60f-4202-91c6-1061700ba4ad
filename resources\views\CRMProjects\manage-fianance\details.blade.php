@extends('layouts.app')
@section('styles')
    @if(app()->getLocale() == 'ar')
        <style>
            /* Call these style if arabic*/
            #invoice.smooth-arabic, #invoice.smooth-arabic * {
            word-break: normal !important;
            word-wrap: normal !important;
            overflow-wrap: normal !important;
            white-space: normal !important;
            margin: 0;
            }
            #invoice.font-reset, #invoice.font-reset * {
            font-family: 'Cairo', sans-serif !important;
            font-size: initial !important;
            line-height: normal;
            direction: rtl !important;
            unicode-bidi: isolate !important;
            font-size: .8rem !important;
            }
            #invoice.font-reset table th{   
                font-size: .8rem !important;
                max-width: 250px;
                font-weight: bold;
            }
            #invoice.font-reset table td{   
                font-size: .8rem !important;
                max-width: 250px;
            }
            #invoice.font-reset table th span,#invoice.font-reset table th div, #invoice.font-reset table th small{
                font-size: 10px !important;
                white-space: nowrap !important;
                display: inline-block !important;
            }
        </style>
    @endif
@endsection
@section('content')
@switch($documentType)
    @case('invoice')
    @livewire('c-r-m-projects.finance-managements.details.finance-invoice', ['projectID' => $projectID, 'documentType' => $documentType, 'documentId' => $documentId])
        @break
    @case('bill')
    @livewire('c-r-m-projects.finance-managements.details.finance-bill', ['projectID' => $projectID, 'documentType' => $documentType, 'documentId' => $documentId])
        @break

    @default
    @livewire('c-r-m-projects.finance-managements.details.finance-proposal', ['projectID' => $projectID, 'documentType' => $documentType, 'documentId' => $documentId])
@endswitch

@endsection

@section('scripts')
    <script src = "{{ asset('new_theme/js/functions.js')}}"></script>
    <script src="{{asset('js/admin/crmProjects/finance.js')}}"></script>
    <script>
        $('.datepicker').datepicker();

        $('.select2-select').select2();

        $("#invoice-button").on("click", function () {
            $('#convert-to-invoice-confirm-message').modal('show');
        });

        document.addEventListener('closeConvertModal', (event) => {
            $('#convert-to-invoice-confirm-message').modal('hide');
        });

        $('#file-upload').on('change', function(e) {
            const file = e.target.files[0];
            uploadFile('file-name', 'file-size', file, 'import-btn');
        });

        $('.cancel-btn').on('click', function() {
            clearFile('file-upload', 'import-btn');
        });

        $('#searchAttachment').on('input', function() {
            searchAttachment(this);
        }); 

        $('#perPage').on('change', function() {
            filterByPerPage(this);
        });

        document.addEventListener('closeConfirmMessage', (event) => {
            $('#confirm-delete-attachment').modal('hide');
        }); 

        $("#invoice-delivery-button").on("click", function () {
            $('#invoice-delivery-modal').modal('show');
        });

        $("#add-payment-button").on("click", function () {
            $('#add-payment-modal').modal('show');
        });

        $("#amount").on("input", function () {
            validateNumber(this);
        });

        $('#status').on('change', function() {
            filterByStatus(this);
        });

        $('#searchReceipt').on('input', function() {
            searchReceipt(this);
        });

        document.addEventListener('closeConfirmReceiptSummaryMessage', (event) => {
            $('#confirm-delete-receipt').modal('hide');
        }); 

        $("#account").on("change", function () {
            setAccount(this);
        });   

        document.addEventListener('resetAccount', (event) => {
            initAccount();
        }); 

        document.addEventListener('resetFile', (event) => {
            initFile();
        });

        $('#file-upload2').on('change', function(e) {
            const file = e.target.files[0];
            uploadFile('file-name2', 'file-size2', file, null);
        });

        document.addEventListener('closeAddPaymentModal', (event) => {
            $('#add-payment-modal').modal('hide');
        });

        $('#date').on('change', function(e) {
            addPaymentDate(this);
        });

        document.addEventListener('resetDate', (event) => {
            initDate();
        });
    </script>
@endsection
