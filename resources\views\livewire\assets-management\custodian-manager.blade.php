<div>
    <!-- Bootstrap Modal -->
    <div class="modal fade" id="custodianModal" tabindex="-1" aria-labelledby="custodianModalLabel" aria-hidden="true"
        wire:ignore.self data-backdrop="static">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="custodianModalLabel">
                        {{ __('assets_managements.custodian.Add Custodian') }}</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <!-- Toggle for Manual Entry -->
                    <div class="form-check mb-4">
                        <input type="checkbox" wire:model="isManualEntry" class="form-check-input" id="isManualEntry">
                        <label class="form-check-label"
                            for="isManualEntry">{{ __('assets_managements.custodian.Manual Entry') }}</label>
                    </div>

                    <!-- Form -->
                    <form wire:submit.prevent="saveCustodian">
                        @if (!$isManualEntry)
                            <div class="form-group">
                                <label for="user_id">{{ __('assets_managements.custodian.Select Custodian') }}</label>
                                <select wire:model.defer="user_id" id="user_id" class="form-control">
                                    <option value="">{{ __('assets_managements.custodian.Select Custodian') }}</option>
                                    @foreach ($users as $user)
                                        <option value="{{ $user->id }}">{{ $user->name }} ({{ $user->email }})</option>
                                    @endforeach
                                </select>
                                @error('user_id') <span class="text-danger text-sm">{{ $message }}</span> @enderror
                            </div>
                        @else
                            <div class="form-group">
                                <label for="name">{{ __('assets_managements.custodian.Full Name') }}</label>
                                <input wire:model.defer="name" type="text" id="name" class="form-control">
                                @error('name') <span class="text-danger text-sm">{{ $message }}</span> @enderror
                            </div>
                            <div class="form-group">
                                <label for="email">{{ __('assets_managements.custodian.Email') }}</label>
                                <input wire:model.defer="email" type="email" id="email" class="form-control">
                                @error('email') <span class="text-danger text-sm">{{ $message }}</span> @enderror
                            </div>
                            <div class="form-group">
                                <label for="mobile_number">{{ __('assets_managements.custodian.Mobile Number') }}</label>
                                <input wire:model.defer="mobile_number" type="text" id="mobile_number" class="form-control">
                                @error('mobile_number') <span class="text-danger text-sm">{{ $message }}</span> @enderror
                            </div>
                        @endif

                        <div class="form-group">
                            <label for="start_date">{{ __('assets_managements.custodian.Start Date') }}</label>
                            <div class="position-relative">
                                <input wire:model.defer="start_date" type="text" id="start_date" class="form-control"
                                    placeholder="2024-12-01">
                                <i class="iconsax field-icon" icon-name="calendar-1"></i>
                            </div>

                            @error('start_date') <span class="text-danger text-sm">{{ $message }}</span> @enderror
                        </div>

                        <div class="form-group">
                            <label for="end_date">{{ __('assets_managements.custodian.End Date') }}</label>
                            <div class="position-relative">
                                <input wire:model.defer="end_date" type="text" id="end_date" class="form-control"
                                    placeholder="2024-12-01">
                                <i class="iconsax field-icon" icon-name="calendar-1"></i>
                            </div>

                            @error('end_date') <span class="text-danger text-sm">{{ $message }}</span> @enderror
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary"
                        data-dismiss="modal">{{ __('assets_managements.custodian.Cancel') }}</button>
                    <button type="submit" wire:click="saveCustodian"
                        class="btn btn-primary">{{ __('assets_managements.custodian.Save') }}</button>
                </div>
            </div>
        </div>
    </div>

    <!-- List of Custodians -->
    <div class="mt-6">
        <div class="card-header color-dark px-0 pt-0">
            <h4 class="text-capitalize fw-500 breadcrumb-title">
                {{ __('assets_managements.custodian.Current Custodians') }}</h4>
            <!-- Button to Open Popup -->
            <button wire:click="openPopup" class="btn btn-primary" data-toggle="modal" data-target="#custodianModal">
                {{ __('assets_managements.custodian.Add Custodian') }}
            </button>
        </div>
    <!-- Success Message -->
    @if (session()->has('message'))
        <div class="alert alert-success mb-4">
            {{ session('message') }}
        </div>
    @endif


        <div class="table-responsive">
            <table class="table mb-0 table-borderless">
                <thead>
                    <tr class="userDatatable-header">
                        <th>
                            <div class="text-center">
                                <span class="projectDatatable-title ">
                                    {{ __('assets_managements.custodian.Name') }}
                                </span>
                            </div>
                        </th>
                        <th>
                            <div class="text-center">
                                <span class="projectDatatable-title ">
                                    {{ __('assets_managements.custodian.Email') }}
                                </span>
                            </div>
                        </th>
                        <th>
                            <div class="text-center">
                                <span class="projectDatatable-title ">
                                    {{ __('assets_managements.custodian.Mobile Number') }}
                                </span>
                            </div>
                        </th>
                        <th>
                            <div class="text-center">
                                <span class="projectDatatable-title ">
                                    {{ __('assets_managements.custodian.Start Date') }}
                                </span>
                            </div>
                        </th>
                        <th>
                            <div class="text-center">
                                <span class="projectDatatable-title ">
                                    {{ __('assets_managements.custodian.End Date') }}
                                </span>
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @forelse ($asset->custodians as $custodian)
                        <tr>
                            <td>{{ $custodian->name }}</td>
                            <td>{{ $custodian->email }}</td>
                            <td>{{ $custodian->phone }}</td>
                            <td>{{ $custodian->pivot->start_date }}</td>
                            <td>{{ $custodian->pivot->end_date ?? 'N/A' }}</td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="5">
                                <div class="PropertyListEmpty">
                                    <img src="{{ asset('empty-icon/no-custodian.svg') }}" class="asset-imgage-empty-img"
                                        alt="">
                                    <p class="mt-3">{{__('general_sentence.empty_ui.No_users_yet')}}</p>
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>
<script>

    function initDatepickers() {
        $('#start_date, #end_date').datepicker({
            dateFormat: 'yy-mm-dd',
            changeMonth: true,
            changeYear: true,
            onSelect: function (dateText, inst) {
                let fieldName = $(this).attr('id');
                Livewire.emit('dateSelected', fieldName, dateText);
            }
        });
    }

    document.addEventListener('livewire:load', function () {

        initDatepickers();

        Livewire.hook('message.processed', (message, component) => {
            initDatepickers(); // reinitialize after DOM update
            setLoading(false);
            $("#user_id").select2();
        });
        Livewire.hook('message.sent', (message, component) => {
            setLoading(true);
        });
        Livewire.on('closeModal', () => {
            $('#custodianModal').modal('hide');
        });
        $('#user_id').on('change', function (e) {
            let selectedValue = $(this).val();
            Livewire.emit('select2Updated', selectedValue);
        });
    });


    window.addEventListener('custodian-added', () => {
        setTimeout(() => {
            window.location.reload();
        }, 500); // allow time for modal to close smoothly
    });

</script>