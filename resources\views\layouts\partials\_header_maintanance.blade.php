<!--link rel="stylesheet" href="{{ asset('css/maintanance/style.css') }}"-->

@if(App::getLocale() == 'en')
<link rel="stylesheet" href="{{ asset('css/maintanance/style.css') }}">
 @else
<link rel="stylesheet" href="{{ asset('css/maintanance/style_ar.css') }}">
@endif
<div class="body-bg"></div>
<div class="d-flex justify-content-center">
    <div class="col-lg-8">
<div class="mobile-search"></div>
        <div class="mobile-author-actions"></div>
        <header class="header-top maintenance-section">
            <nav class="navbar navbar-light">
                <div class="navbar-left">
                     @if($data['projectImage'])
                            <div class="mt-20 mb-20">
                                <a class="logo-c" href="{{  url('') }}"><img class="svg dark" width="150" src="{{ asset('img/osool-logo-white.svg') }}" alt="svg"></a>
                            </div>
                        @endif
                    <a href="" class="sidebar-toggle">
                        <!-- <img class="svg" src="{{ asset('img/svg/bars.svg') }}" alt="img"></a> -->
                    <div class="top-menu">
                        <div class="strikingDash-top-menu position-relative">
                            <ul>
                                <li>
                                    <a href="task-app.html" class="">
                                        <span data-feather="clipboard" class="nav-icon"></span>
                                        <span class="menu-text">Task App</span>
                                    </a>
                                </li>
                                <li class="nav-flag-select">
                                    <div class="dropdown-custom">
                                        <a href="javascript:;" class="nav-item-toggle">
                                            @if (App::getLocale()=='en')
                                            <img src="{{asset('img/flag.png')}}" alt="English" class="rounded-circle">
                                            @elseif (App::getLocale()=='ar')
                                            <img src="{{asset('img/ar.png')}}" alt="Arabic" class="rounded-circle">
                                            @endif
                                        </a>
                                        <div class="dropdown-wrapper dropdown-wrapper--small">
                                            <a href="{{route('changeLanguage',"en")}}"><img src="{{asset('img/eng.png')}}" alt="English"
                                                    class="rounded-circle"> English</a>
                                            <a href="{{route('changeLanguage',"ar")}}"><img src="{{asset('img/ar.png')}}" alt="Arabic"
                                                    class="rounded-circle"> Arabic</a>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

            </nav>
            @if (App::getLocale()=='ar')
            <a href="{{route('changeLanguage',"en")}}" class="text-white language btn bg-primary">English</a>
            @else
            <a href="{{route('changeLanguage',"ar")}}" class="text-white language btn bg-primary">العربية</a>
            @endif
        </header>
</div>
</div>
        <!-- <div class="contents d-flex justify-content-center pt-4"> -->
            <div class="mt-2 justify-content-center d-flex">
            <div class="col-lg-8">
                <div class="bg-white maintenance-section">
                <div class="row">
                    <div class="col-lg-12">
                       
                       <!-- <div class="logo-div text-xs-center mb-5 mt-10 text-center">
                        @if($data['projectImage'])                        
                        <a class="" href="{{  url('') }}"><img class="svg dark" src="{{ImagesUploadHelper::displayImage($data['projectImage'], 'uploads/project_images')}}" alt="svg" width="150"></a>
                        @else
                        <a class="" href="{{  url('') }}"><img class="svg dark" src="{{ asset('img/OSOOL_logo_svg.svg') }}" alt="svg" width="150"></a>
                        @endif
                        {{--<a class="" href="{{  url('') }}"><img class="svg dark" src="{{ asset('img/OSOOL_logo.png') }}" alt="svg" width="150"></a>--}}
                        </div> -->
                    </div>
                </div>
