<div>
    <div class = "contents wo-management assets_managements scroll-only-table">
        <div class = "container-fluid">
            <div class = "row">
                <div class = "col-lg-12">
                    <div class = "project-progree-breadcrumb">
                        <div class = "breadcrumb-main flex-md-nowrap user-member justify-content-sm-between mb-xs-none ">
                            <div class = "d-flex flex-wrap justify-content-center breadcrumb-main__wrapper">
                                <div class = "page-title-wraps">
                                    <div
                                        class = "page-title bm-page-title justify-content-between mr-md-3 d-flex align-items-center text-center text-lg-left">
                                        <div class = "mr-md-2 page-title__left d-block">
                                            <h4 class = "text-capitalize fw-500 breadcrumb-title">
                                                @lang('work_order.bread_crumbs.work_order_list')
                                            </h4>
                                            <span class = "sub-title work_order_count ml-0">
                                                {{ isset($list) ? $list->total() : 0 }}
                                                @lang('work_order.bread_crumbs.work_orders')
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <form action = "javascript:void(0)"
                                    class = "d-flex align-items-center user-member__form my-sm-0 my-2">
                                    <svg xmlns = "http://www.w3.org/2000/svg" width = "24" height = "24"
                                        viewBox = "0 0 24 24" fill = "none" stroke = "currentColor" stroke-width = "2"
                                        stroke-linecap = "round" stroke-linejoin = "round"
                                        class = "feather feather-search">
                                        <circle cx = "11" cy = "11" r="8"></circle>
                                        <line x1 = "21" y1 = "21" x2 = "16.65" y2 = "16.65"></line>
                                    </svg>
                                    <input type = "search" id = "search_by_id"
                                        class = "form-control mr-sm-2 border-0 box-shadow-none"
                                        placeholder = "@lang('work_order.button.search_by_wo_id')" aria-label = "Search"
                                        wire:model.live.debounce.250ms = "search">
                                </form>
                            </div>
                            @if (
                                !empty($user->user_type == 'building_manager') ||
                                    !empty($user->user_type == 'osool_admin') ||
                                    (!empty($user->user_type == 'building_manager_employee') &&
                                        (isset($userPrivileges->workorder) && !in_array('no_view', $userPrivileges->workorder))))
                                <div class = "action-btn d-sm-flex">
                                    <a href = "{{ route('workorder.openPmWorkOrdersList') }}" class = "btn btn-xs btn-squared btn-outline-primary ml-sm-10 mb-sm-0 mb-3 no-wrap">
                                        <i class = "las la-cog fs-16"></i>
                                        @lang('work_order.button.manage_pm_work_order')
                                    </a>
                                    <div class = "dropdown action-btn pb-lg-0 pb-xs-2 ml-sm-15 w-100">
                                        @if ($checkCreateButton)
                                            <button class = "btn btn-xs btn-default btn-primary dropdown-toggle w-100"
                                                type = "button" id = "dropdownMenu2" data-toggle = "dropdown"
                                                aria-haspopup = "true" aria-expanded = "false">
                                                <i class = "las la-plus fs-16"></i>
                                                @lang('work_order.button.create_work_order')
                                            </button>
                                        @endif
                                        <div class = "dropdown-menu text-left" aria-labelledby = "dropdownMenu2">
                                            <a href = "{{ route('workorder.c-reactive.maintance') }}"
                                                class = "dropdown-item">
                                                @lang('work_order.button.reactive_work_order')
                                            </a>
                                            <a href = "{{ route('workorder.c-preventive.maintance') }}"
                                                class = "dropdown-item">
                                                @lang('work_order.button.preventive_work_order')
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            @endif
                            <!-- <div class = "action-btn d-flex">
                                <a href = "{{ route('workorder.openPmWorkOrdersList') }}" class = "btn btn-xs btn-squared btn-outline-primary ml-10">
                                    <i class = "las la-cog fs-16"></i>
                                    @lang('work_order.button.manage_pm_work_order')
                                </a>
                            </div> -->
                        </div>
                    </div>
                </div>
            </div>
            <div class = "row">
                <div class = "col-lg-12">
                    @if ($showDiv)
                        <div class = "d-md-flex wo-left" id = "exportSection">
                            <div class = "d-md-flex wo-left">
                                <div class = "project-top-left wo-left-two">
                                    <div>
                                        <p class = "mb-0 mr-10 fs-14 color-light">&nbsp;</p>
                                    </div>
                                    <div class = "d-flex">
                                        <div
                                            class = "project-tap global-shadow order-lg-1 order-2 my-10 float-xl-left float-lg-left mr-sm-15">
                                            <div class = "btn-group w-100">
                                                @if (!$showCloseText)
                                                    <div wire:loading>
                                                        <button type = "button"
                                                            class = "btn btn-default py-2 px-3 mb-0 text-primary d-flex align-items-center"
                                                            wire:loading.attr = "disabled">
                                                            <span class = "spinner-border spinner-border-sm"
                                                                role = "status" aria-hidden = "true"></span>
                                                            <span class = "fs-13 fw-400 text">@lang('work_order.forms.label.show_selected')</span>
                                                        </button>
                                                    </div>
                                                    <button type = "button"
                                                        class = "btn btn-default py-2 px-3 mb-0 text-primary d-flex align-items-center"
                                                        wire:loading.class = "hide"
                                                        onclick = "showSelectedRowsByCheckBox(true)">
                                                        <span class = "fs-13 fw-400 text">@lang('work_order.forms.label.show_selected')</span>
                                                    </button>
                                                @else
                                                    <button type = "button"
                                                        class = "btn btn-default py-2 px-3 mb-0 text-danger d-flex align-items-center"
                                                        wire:loading.class = "hide"
                                                        onclick = "showSelectedRowsByCheckBox(false)">
                                                        <span data-feather = "x" class = "text-danger"></span>
                                                        <span
                                                            class = "fs-13 fw-400 text text-danger">@lang('work_order.forms.label.close_selected')</span>
                                                    </button>
                                                    <div wire:loading>
                                                        <button type = "button"
                                                            class = "btn btn-default py-2 px-3 mb-0 text-danger d-flex align-items-center"wire:loading.attr="disabled">
                                                            <span data-feather = "x" class = "text-danger"></span>
                                                            <span class = "spinner-border spinner-border-sm text-danger"
                                                                role = "status" aria-hidden = "true"></span>
                                                            <span
                                                                class = "fs-13 fw-400 text text-danger">@lang('work_order.forms.label.close_selected')</span>
                                                        </button>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                        <div
                                            class = "project-tap global-shadow order-lg-1 order-2 my-10 float-xl-left float-lg-left">
                                            <div class = "btn-group w-100">
                                                <label class = "py-2 px-3 mb-0 text-primary">
                                                    <a href = "#"
                                                        class = "btn-default btn-svg p-0 d-flex align-items-center"
                                                        data-toggle = "modal" data-target = "#export-modal"
                                                        id = "showExportModal">
                                                        <img src = "{{ asset('img/svg/export-icon.svg') }}"
                                                            class = "mr-2">
                                                        <span>@lang('work_order.forms.label.export')</span>
                                                    </a>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @else
                        <div class = "project-top-wrapper project-top-progress wo-top-bar">
                            <div class = "d-lg-flex justify-content-lg-between">
                                <div class = "d-md-flex wo-left" id = "filterElements">
                                    <div>
                                        <div>
                                            <p class = "mr-10 mb-10 fs-14 color-light">&nbsp;</p>
                                        </div>
                                        <div
                                            class = "d-flex flex-sm-row flex-column global-shadow order-lg-1 order-2 mb-10 float-xl-left float-lg-left w-100 bg-none">
                                            @if ($showProviderFilter)
                                                @php
                                                    $selectedMultipleSpId = [];
                                                @endphp

                                                @if (session()->has('selected_multiple_sp_id'))
                                                    @php
                                                        $selectedMultipleSpId = session()->get(
                                                            'selected_multiple_sp_id',
                                                        );
                                                    @endphp
                                                @endif

                                                <div
                                                    class = "rounded p-2 btn-primary mr-sm-2 d-flex align-items-center h-40">
                                                    <i data-feather = "users" class = "w-15 text-white mr-1"
                                                        style = "width:14px;height:14px;"></i>
                                                    @if ($countServiceProviders == count($explodedServiceProviders))
                                                        <span class = "fs-12 no-wrap fs-14 mr-2 sp_count_details">
                                                            @lang('work_order.forms.label.All_Service_Providers')
                                                        </span>
                                                    @else
                                                        <span class = "fs-12 no-wrap fs-14 mr-2 sp_count_details">
                                                            {{ count($explodedServiceProviders) }}
                                                            @lang('work_order.bread_crumbs.service_providers')
                                                        </span>
                                                    @endif
                                                    |
                                                    <span class = "cursor-pointer fs-12 ml-2" data-toggle = "modal"
                                                        data-target = "#select-sps">
                                                        @lang('work_order.forms.label.Change')
                                                    </span>
                                                </div>
                                            @endif
                                            <div class = "d-flex mt-sm-0 mt-2">
                                                <div class = "btn-group check-dropdown-toggle w-100 bg-white rounded h-40 d-flex align-items-center"
                                                    data-toggle = "modal" data-target = "#all-filters"
                                                    aria-haspopup = "true" aria-expanded = "false">
                                                    <label
                                                        class = "py-2 dropdown-toggle pl-3 pr-3 mb-0  cursor-pointer more-btn text-dark">
                                                        <i class = "las la-sliders-h text-dark"></i>
                                                        <span>@lang('work_order.forms.label.allFilter')</span>
                                                        <span class = "reset-filter d-none" data-toggle = "tooltip"
                                                            id = "xmark" title = "@lang('work_order.bread_crumbs.reset_filtering')">
                                                            <i class = "las la-times text-danger"></i>
                                                        </span>
                                                    </label>
                                                </div>
                                                <div
                                                    class = "btn-group check-dropdown-toggle w-100 bg-white rounded h-40 d-flex align-items-center ml-2">
                                                    <label
                                                        class = "py-2 dropdown-toggle pl-3 pr-3 mb-0 cursor-pointer more-btn text-dark"
                                                        type = "button" id = "dropdownMenu2" data-toggle = "dropdown"
                                                        aria-haspopup = "true" aria-expanded = "false">
                                                        <i class = "las la-sort"></i>
                                                        <span>
                                                            @lang('work_order.list.sort_by')
                                                        </span>
                                                        <span id = "option_value"></span>
                                                    </label>
                                                    <div class = "dropdown-menu" id = "filter_id_section_sort"
                                                        aria-labelledby = "dropdownMenu2" wire:ignore>
                                                        <a href = "javascript:void(0)" data-check = "false"
                                                            data-value = "sort_by_date_asc"
                                                            class = "dropdown-item sortby_filter">
                                                            @lang('work_order.list.date') (@lang('work_order.list.asc'))
                                                            <i id = "tick0"
                                                                class = "fa fa-check float-right display-none"
                                                                aria-hidden = "true"></i>
                                                        </a>
                                                        <a href = "javascript:void(0)" data-check = "false"
                                                            data-value = "sort_by_date_desc"
                                                            class = "dropdown-item sortby_filter active">
                                                            @lang('work_order.list.date') (@lang('work_order.list.desc'))
                                                            <i id = "tick0"
                                                                class = "fa fa-check float-right display-none"
                                                                aria-hidden = "true"></i>
                                                        </a>
                                                        <a href="javascript:void(0)" data-check = "false"
                                                            data-value = "sort_by_status_asc"
                                                            class = "dropdown-item sortby_filter">
                                                            @lang('work_order.bread_crumbs.status') (@lang('work_order.list.asc'))
                                                            <i id = "tick1"
                                                                class = "fa fa-check float-right display-none"
                                                                aria-hidden = "true"></i>
                                                        </a>
                                                        <a href = "javascript:void(0)" data-check = "false"
                                                            data-value = "sort_by_status_desc"
                                                            class = "dropdown-item sortby_filter">
                                                            @lang('work_order.bread_crumbs.status') (@lang('work_order.list.desc'))
                                                            <i id = "tick1"
                                                                class = "fa fa-check float-right display-none"
                                                                aria-hidden = "true"></i>
                                                        </a>
                                                        <a href = "javascript:void(0)" data-check = "false"
                                                            data-value = "sort_by_name_asc"
                                                            class = "dropdown-item sortby_filter">
                                                            @lang('work_order.list.technician') (@lang('work_order.list.asc'))
                                                            <i id = "tick2"
                                                                class = "fa fa-check float-right display-none"
                                                                aria-hidden = "true"></i>
                                                        </a>
                                                        <a href = "javascript:void(0)" data-check = "false"
                                                            data-value = "sort_by_name_desc"
                                                            class = "dropdown-item sortby_filter">
                                                            @lang('work_order.list.technician') (@lang('work_order.list.desc'))
                                                            <i id = "tick2"
                                                                class = "fa fa-check float-right display-none"
                                                                aria-hidden = "true"></i>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class = "wo-right pl-lg-3">
                                    <div class = "project-top-right mb-10 d-block">
                                        <div class = "d-flex">
                                            <div class = "mb-sm-0 mb-10 pr-lg-0 flex-fill">
                                                <script src="{{ asset('new_theme/js/jquery.min.js') }}"></script>
                                                <script>
                                                    $(document).ready(function() {
                                                        initCalendar(`{{ $this->projectDetails->created_at }}`);
                                                        filterByCalendar();
                                                    });
                                                </script>
                                                <p class = "mb-10 d-lg-block d-md-none d-none">&nbsp;</p>
                                                <div class = "input-container icon-left position-relative">
                                                    <span class = "input-icon icon-left">
                                                        <span data-feather = "calendar"></span>
                                                    </span>
                                                    <input type = "text" class = "form-control form-control-default"
                                                        name = "text" placeholder = "{{ $currentDate }}"
                                                        id = "calender_filter_workorder" autocomplete = "off">
                                                    <span class = "input-icon icon-right">
                                                        <span data-feather = "chevron-down"></span>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
            <div class = "">
                <div class = "userDatatable global-shadow p-30 bg-white radius-xl w-100 mb-30">
                    <div class = "table-responsive" style = "overflow-x: auto;">
                        <table class = "table mb-0 table-borderless dataTable no-footer">
                            <thead>
                                <tr class = "userDatatable-header">
                                    <th class = "check-hover pr-0 w-10">
                                        <input type = "checkbox" name = "select_all" value = "1"
                                            class = "mt-1 mb-1 mr-1" wire:loading.attr = "disabled"
                                            wire:model.live.debounce.250ms = "selectedAllRows">
                                    </th>
                                    @if (in_array($user->user_type, ['sp_admin', 'supervisor']))
                                        <th class = "@if (in_array('project_admin_id', $hiddenColumns)) d-none @endif">
                                            <span class = "projectDatatable-title">@lang('work_order.table.project_name')</span>
                                        </th>
                                    @endif
                                    <th>
                                        <span class = "projectDatatable-title">@lang('work_order.table.work_order_id')</span>
                                    </th>
                                    <th>
                                        <span class = "projectDatatable-title">@lang('work_order.table.description')</span>
                                    </th>
                                    <th class = "@if (in_array('property', $hiddenColumns)) d-none @endif">
                                        <span class = "projectDatatable-title">@lang('work_order.table.property_name')</span>
                                    </th>
                                    <th class = "@if (in_array('floor', $hiddenColumns)) d-none @endif">
                                        <span class = "projectDatatable-title">@lang('data_properties.property_forms.label.floor')</span>
                                    </th>
                                    <th class = "@if (in_array('room', $hiddenColumns)) d-none @endif">
                                        <span class = "projectDatatable-title">@lang('data_properties.property_forms.label.room')</span>
                                    </th>
                                    <th class = "@if (in_array('workorder_journey', $hiddenColumns)) d-none @endif">
                                        <span class = "projectDatatable-title">@lang('work_order.table.workorder_journey')</span>
                                    </th>
                                    <th class = "@if (in_array('mr_place', $hiddenColumns)) d-none @endif">
                                        <span class = "projectDatatable-title">@lang('data_maintanance_request.common.place')</span>
                                    </th>
                                    <th class = "@if (in_array('apartment_villa', $hiddenColumns)) d-none @endif">
                                        <span class = "projectDatatable-title">@lang('user_management_module.user_forms.label.tenants_appartment_and_villa')</span>
                                    </th>
                                    <th class = "@if (in_array('status', $hiddenColumns)) d-none @endif">
                                        <span class = "projectDatatable-title">@lang('work_order.table.status')</span>
                                    </th>
                                    <th class = "@if (in_array('supervisor_name', $hiddenColumns)) d-none @endif">
                                        <span class = "projectDatatable-title">@lang('work_order.table.service_provider_supervisor')</span>
                                    </th>
                                    <th class = "@if (in_array('assigned_worker', $hiddenColumns)) d-none @endif">
                                        <span class = "projectDatatable-title">@lang('work_order.table.assigned_worker')</span>
                                    </th>
                                    <th class = "@if (in_array('response_time', $hiddenColumns)) d-none @endif">
                                        <span class = "projectDatatable-title">@lang('work_order.table.response_time')</span>
                                    </th>
                                    <th class = "@if (in_array('pass_fail', $hiddenColumns)) d-none @endif">
                                        <span class = "projectDatatable-title">@lang('work_order.table.pass_fail')</span>
                                    </th>
                                    <th class = "@if (in_array('submission_date', $hiddenColumns)) d-none @endif">
                                        <span class = "projectDatatable-title">@lang('work_order.table.submission_date')</span>
                                    </th>
                                    <th class = "@if (in_array('target_date', $hiddenColumns)) d-none @endif">
                                        <span class = "projectDatatable-title">@lang('work_order.table.deadline')</span>
                                    </th>
                                    <th class = "@if (in_array('closed_at', $hiddenColumns)) d-none @endif">
                                        <span class = "projectDatatable-title">@lang('work_order.forms.label.closed_at')</span>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                @if (isset($list) && $list->count())
                                    @foreach ($list as $data)
                                        <tr wire:key = "row--{{ $data->id }}">
                                            <td class = "check-hover w-30px pr-0">
                                                <span class = "projectDatatable-title d-flex align-items-center">
                                                    <input type = "checkbox" name = "select_wo" class = "mt-1 mb-1"
                                                        value={{ $data->id }} wire:loading.attr = "disabled"
                                                        wire:model.live.debounce.250ms = "selectedRows">
                                                </span>
                                            </td>
                                            @if (in_array($user->user_type, ['sp_admin', 'supervisor']))
                                                <td>
                                                    <div class = "d-flex">
                                                        <div class = "userDatatable-inline-title">
                                                            <a
                                                                href = "{{ $this->encryptDecryptedString($data['id']) }}">
                                                                <span
                                                                    class = "fw-400 badge badge-round badge-grey badge-lg badge">
                                                                    {{ $selectedLanguage == 'en' ? $project?->project_name : $project?->project_name_ar }}
                                                                </span>
                                                            </a>
                                                        </div>
                                                    </div>
                                                </td>
                                            @endif
                                            <td>{!! $this->getFormatWorkOrderId($user->id, $checkCreateButton, $data) !!}</td>
                                            <td>
                                                <span class = "max-td"
                                                    title = "{{ $data->description }}">{{ $data->description }}</span>
                                            </td>
                                            <td class = "@if (in_array('property', $hiddenColumns)) d-none @endif">
                                                {!! App\Http\Helpers\WorkorderHelper::getFormattedPropertyName($data->propertyBuilding) !!}</td>
                                            <td class = "@if (in_array('floor', $hiddenColumns)) d-none @endif">
                                                {{ !empty($data->floor) ? $data->floor : '-' }}</td>
                                            <td class = "@if (in_array('room', $hiddenColumns)) d-none @endif">
                                                {{ !empty($data->room) ? $data->room : '-' }}</td>
                                            <td class = "@if (in_array('workorder_journey', $hiddenColumns)) d-none @endif">
                                                @php
                                                    $workOrderJourney =
                                                        $data->status == \App\Enums\WorkOrderStatus::Closed->value
                                                            ? ($data->workorder_journey = 'finished')
                                                            : $data->workorder_journey;
                                                @endphp

                                                {!! App\Http\Helpers\WorkorderHelper::generateWorkorderJourneyColumnContent($workOrderJourney) !!}
                                            </td>
                                            <td class = "@if (in_array('mr_place', $hiddenColumns)) d-none @endif">
                                                {{ !empty($data->maintenanceRequest->place) ? $data->maintenanceRequest->place : '-' }}
                                            </td>
                                            <td class = "@if (in_array('apartment_villa', $hiddenColumns)) d-none @endif">
                                                @php
                                                    $userRow = $this->getUserInformationsByValues(
                                                        'phone',
                                                        $data->mr_phone,
                                                    );
                                                @endphp

                                                {{ isset($userRow) && isset($userRow->apartment) ? $userRow->apartment : '-' }}
                                            </td>
                                            <td class = "@if (in_array('status', $hiddenColumns)) d-none @endif">
                                                @php
                                                    $status = $data->status;
                                                    $currentDateNewFormat = $this->changeDateFormat(
                                                        'Y-m-d',
                                                        $currentDate,
                                                    );

                                                    if ($data->status == \App\Enums\WorkOrderStatus::Open->value) {
                                                        if ($data->start_date > $currentDateNewFormat) {
                                                            $status = \App\Enums\WorkOrderStatus::Scheduled->value;
                                                        }
                                                    }
                                                @endphp

                                                @if ($data->contract_type == 'regular')
                                                    @switch ($status)
                                                        @case(\App\Enums\WorkOrderStatus::Open->value)
                                                            <span
                                                                class = "bg-opacity-success color-success rounded-pill userDatatable-content-status active">@lang('work_order.bread_crumbs.open')</span>
                                                        @break

                                                        @case(\App\Enums\WorkOrderStatus::InProgress->value)
                                                            <span
                                                                class = "badge-in-progress rounded-pill userDatatable-content-status active">@lang('work_order.bread_crumbs.in_progress')</span>
                                                        @break

                                                        @case(\App\Enums\WorkOrderStatus::OnHold->value)
                                                            <span
                                                                class = "bg-opacity-warning color-warning rounded-pill userDatatable-content-status active">@lang('work_order.bread_crumbs.on_hold')</span>
                                                        @break

                                                        @case(\App\Enums\WorkOrderStatus::Closed->value)
                                                            <span
                                                                class = "bg-opacity-dark color-light rounded-pill userDatatable-content-status active">@lang('work_order.bread_crumbs.closed')</span>
                                                        @break

                                                        @case(\App\Enums\WorkOrderStatus::Deleted->value)
                                                            <span
                                                                class = "bg-opacity-danger color-danger rounded-pill userDatatable-content-status active">@lang('work_order.bread_crumbs.deleted')</span>
                                                        @break

                                                        @case(\App\Enums\WorkOrderStatus::Reopened->value)
                                                            <span
                                                                class = "bg-opacity-primary color-primary rounded-pill userDatatable-content-status active">@lang('work_order.bread_crumbs.re_open')</span>
                                                        @break

                                                        @case(\App\Enums\WorkOrderStatus::Scheduled->value)
                                                            <span
                                                                class = "bg-opacity-dark color-dark rounded-pill userDatatable-content-status active">@lang('work_order.bread_crumbs.scheduled')</span>
                                                        @break

                                                        @case(\App\Enums\WorkOrderStatus::Scheduled->value)
                                                            @lang('work_order.bread_crumbs.scheduled')
                                                        @break

                                                        @default
                                                            -
                                                        @break
                                                    @endswitch
                                                @else
                                                    @switch($status)
                                                        @case(\App\Enums\WorkOrderStatus::Closed->value)
                                                            <span
                                                                class = "bg-opacity-dark color-light rounded-pill userDatatable-content-status active">@lang('work_order.bread_crumbs.closed')</span>
                                                        @break

                                                        @default
                                                            <span
                                                                class = "bg-opacity-secondary color-secondary rounded-pill userDatatable-content-status active">@lang('work_order.bread_crumbs.warrenty')</span>
                                                        @break
                                                    @endswitch
                                                @endif
                                            </td>
                                            <td class = "@if (in_array('supervisor_name', $hiddenColumns)) d-none @endif">
                                                {!! App\Http\Helpers\WorkorderHelper::getFormattedSupervisorsOrServiceProviderName(
                                                    $data->supervisor_id,
                                                    $data->assigned_to,
                                                    $data->worker_id,
                                                    $data->service_provider_id,
                                                ) !!}</td>
                                            <td class = "@if (in_array('assigned_worker', $hiddenColumns)) d-none @endif">
                                                {!! App\Http\Helpers\WorkorderHelper::generateAssignedWorkerColumnContent($data) !!}</td>
                                            <td class = "@if (in_array('response_time', $hiddenColumns)) d-none @endif">
                                                {!! App\Http\Helpers\WorkorderHelper::generateResponseTimeColumnContent($data) !!}</td>
                                            <td class = "@if (in_array('pass_fail', $hiddenColumns)) d-none @endif">
                                                {!! $this->getPassFailColumn($data->pass_fail, $data->workorder_journey, $data->contract_type, $data->status) !!}</td>
                                            <td class = "@if (in_array('submission_date', $hiddenColumns)) d-none @endif">
                                                {!! App\Http\Helpers\WorkorderHelper::formatSubmissionDate($data) !!}</td>
                                            <td class = "@if (in_array('target_date', $hiddenColumns)) d-none @endif">
                                                {!! App\Http\Helpers\WorkorderHelper::generateTargetDateColumnContent($data) !!}</td>
                                            <td class = "@if (in_array('closed_at', $hiddenColumns)) d-none @endif">
                                                {{ $data->job_completion_date ? $this->changeDateFormat('d-m-Y H:i', $data->job_completion_date) : '---' }}
                                            </td>
                                        </tr>
                                    @endforeach
                                @else
                                    <tr>
                                        <td colspan = "15">
                                            <div class = "row">
                                                <div class = "PropertyListEmpty">
                                                    <img src = "{{ asset('/empty-icon/To_do_list_rafiki.svg') }}"
                                                        class = "fourth_img" />
                                                    <h4 class = "first_title">@lang('general_sentence.empty_ui.No_workorders_yet')</h4>
                                                    <h6 class = "second_title">@lang('general_sentence.empty_ui.The_workorder_list_will_appear_here')</h6>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>
                    @if (!$this->valueIsRequired($list))
                        <div class = "row mt-3">
                            <div class = "col-lg-4">
                                <ul class = "atbd-pagination">
                                    <li>
                                        <div class = "paging-option">
                                            <div
                                                class = "dataTables_length d-flex justify-content-center justify-content-lg-start">
                                                <label class = "d-flex align-items-center">
                                                    @lang('general_sentence.show')
                                                    <select aria-controls = "workorder_table"
                                                        wire:model.live.debounce.250ms = "perPage"
                                                        class = "custom-select custom-select-sm form-control form-control-sm mx-2"
                                                        style = "min-height: 35px;">
                                                        <option value = "5">5</option>
                                                        <option value = "10">10</option>
                                                        <option value = "25">25</option>
                                                        <option value = "50">50</option>
                                                        <option value = "100">100</option>
                                                    </select>
                                                    @lang('general_sentence.results')
                                                </label>
                                            </div>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                            <div class = "col-lg-8">
                                <div class = "user-pagination">
                                    <div class = "user-pagination">
                                        <div class = "d-flex justify-content-lg-end justify-content-center mt-1 mb-30">
                                            {!! $list->links('vendor.pagination.livewire-pagination') !!}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
    <!-- Modals -->
    @if ($showProviderFilter)
        <div class = "modal fade" id = "select-sps" tabindex = "-1" aria-labelledby = "exampleModalLabel"
            aria-hidden = "true" wire:ignore.self>
            <div class = "modal-dialog">
                <div class = "modal-content">
                    <div class = "modal-header">
                        <h5 class = "modal-title" id = "exampleModalLabel">@lang('work_order.bread_crumbs.service_providers')</h5>
                        <button type = "button" class = "close" data-dismiss = "modal" aria-label = "Close">
                            <span aria-hidden = "true">&times;</span>
                        </button>
                    </div>
                    <div class = "input-group search-group properties-search border-bottom">
                        <div class = "input-group-prepend">
                            <span class = "input-group-text" id = "basic-addon1">
                                <i class = "fa fa-search" aria-hidden = "true"></i>
                            </span>
                        </div>
                        <input type = "text" class = "form-control rounded search_sp_filter"
                            placeholder = "@lang('work_order.forms.label.search_for_sp')" aria-label = "Username"
                            aria-describedby = "basic-addon1">
                    </div>
                    <div class = "modal-body">
                        <div class = "properties-list">
                            <ul class = "site-scrollbar-one">
                                <li class = "p-3">
                                    <div class = "row">
                                        @if (isset($serviceprovidersList) && $serviceprovidersList->count())
                                            @php
                                                $selectedMultipleSpId = [];
                                            @endphp

                                            @if (session()->has('selected_multiple_sp_id'))
                                                @php
                                                    $selectedMultipleSpId = session()->get('selected_multiple_sp_id');
                                                @endphp
                                            @endif

                                            @php
                                                $isAllChecked =
                                                    isset($selectedMultipleSpId) &&
                                                    $countServiceProviders == count($selectedMultipleSpId)
                                                        ? 'checked'
                                                        : '';
                                            @endphp

                                            <div class = "col-md-6 mb-3">
                                                <div
                                                    class = "bg-light-grey rounded p-2 d-flex align-items-center justify-content-between sp-list">
                                                    <div class = "d-flex align-items-center ">
                                                        <img src = "{{ asset('img/svg/all-sps.svg') }}"
                                                            class = "wh-40 rounded-circle mr-2">
                                                        <span>@lang('work_order.forms.label.All_Service_Providers')</span>
                                                    </div>
                                                    <div
                                                        class = "checkbox-theme-default custom-checkbox mt-1 custom-radio lh-0">
                                                        <input type = "checkbox" {{ $isAllChecked }}
                                                            name = "check-all-sp-list" value = ""
                                                            id = "check-all-sp-list">
                                                        <label for = "check-all-sp-list" class = "pl-0"></label>
                                                    </div>
                                                </div>
                                            </div>

                                            @foreach ($serviceprovidersList as $data)
                                                @php
                                                    $imgPath =
                                                        trim($data->logo) != ''
                                                            ? (trim(
                                                                ImagesUploadHelper::displayImage(
                                                                    $data->logo,
                                                                    'uploads/serviceprovider',
                                                                ),
                                                            ) != ''
                                                                ? ImagesUploadHelper::displayImage(
                                                                    $data->logo,
                                                                    'uploads/serviceprovider',
                                                                )
                                                                : asset(
                                                                    'uploads/profile_images/dummy_profile_image.png',
                                                                ))
                                                            : asset('uploads/profile_images/dummy_profile_image.png');
                                                    $checked = in_array($data['id'], $selectedMultipleSpId)
                                                        ? 'checked'
                                                        : '';
                                                @endphp

                                                <div class = "col-md-6 mb-3 service-provider"
                                                    data-name = "{{ $data['name'] }}"
                                                    wire:key="row-{{ $data->id }}">
                                                    <div
                                                        class = "bg-light-grey rounded p-2 d-flex align-items-center justify-content-between sp-list">
                                                        <div class = "d-flex align-items-center ">
                                                            <img src = "{{ $imgPath }}"
                                                                alt = "{{ $data['name'] }}"
                                                                class = "wh-40 rounded-circle mr-2">
                                                            <span>{{ $data['name'] }}</span>
                                                        </div>
                                                        <div
                                                            class = "checkbox-theme-default custom-checkbox mt-1 custom-radio lh-0">
                                                            <input type = "checkbox" class = "sp_list_check"
                                                                name = "sp-list_{{ $data->id }}"
                                                                value = "{{ $data->id }}"
                                                                id = "sp-list_{{ $data->id }}"
                                                                data-sp-id = "{{ $data['id'] }}"
                                                                {{ $checked }}>
                                                            <label for = "sp-list_{{ $data->id }}"
                                                                class = "pl-0"></label>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        @endif
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class = "modal-footer">
                        <button type = "button"
                            class = "btn btn-light btn-default btn-squared fw-400 text-capitalize radius-md cancel-btn"
                            data-dismiss = "modal">@lang('work_order.button.close')</button>
                        <button type = "button" class = "btn btn-primary apply_multiple_sp"
                            data-dismiss = "modal">@lang('work_order.button.apply')</button>
                    </div>
                </div>
            </div>
        </div>
    @endif
    <div class = "modal fade" id = "export-modal" tabindex = "-1" role = "dialog"
        aria-labelledby = "exampleModalLabel" aria-hidden = "true" wire:ignore.self>
        <div class = "modal-dialog modal-dialog-centered modal-sm" role = "document">
            <div class = "modal-content">
                <form id = "exportForm" wire:submit.prevent = "submitExportForm">
                    @csrf
                    <div class = "modal-body">
                        <div class = "form-group">
                            <label for = "emp_user_status">
                                @lang('work_order.forms.label.choose_file')
                                <small class = "required">*</small>
                            </label>
                            <div class = "radio-horizontal-list d-flex">
                                <div class = "radio-theme-default custom-radio">
                                    <input class = "radio" type = "radio"
                                        value = "{{ \App\Enums\FileType::PDF->value }}" name = "fileType"
                                        id = "type1" wire:model.live.debounce.250ms = "fileType">
                                    <label for = "type1">
                                        <span class = "radio-text">@lang('work_order.forms.label.pdf')</span>
                                    </label>
                                </div>
                                <div class = "radio-theme-default custom-radio">
                                    <input class = "radio" type = "radio"
                                        value = "{{ \App\Enums\FileType::CSV->value }}" name = "fileType"
                                        id = "type2" wire:model.live.debounce.250ms = "fileType">
                                    <label for = "type2">
                                        <span class = "radio-text">@lang('work_order.forms.label.csv')</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class = "form-group">
                            <label for = "emp_user_status">
                                @lang('work_order.forms.label.choose_language')
                                <small class = "required">*</small>
                            </label>
                            <div class = "radio-horizontal-list d-flex">
                                <div class = "radio-theme-default custom-radio">
                                    <input type = "radio" class = "radio"
                                        value = "{{ \App\Enums\Language::English->value }}" name = "fileLanguage"
                                        id = "lang1" wire:model.live.debounce.250ms = "fileLanguage">
                                    <label for = "lang1">
                                        <span class = "radio-text">@lang('work_order.forms.label.english')</span>
                                    </label>
                                </div>
                                <div class = "radio-theme-default custom-radio">
                                    <input type = "radio" class = "radio"
                                        value = "{{ \App\Enums\Language::Arabic->value }}" name = "fileLanguage"
                                        id = "lang2" wire:model.live.debounce.250ms = "fileLanguage">
                                    <label for = "lang2">
                                        <span class = "radio-text">@lang('work_order.forms.label.arabic')</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class = "form-group @if (isset($user->projectDetails) && $user->projectDetails->industry_type != 10) d-none @endif">
                            <label for = "emp_user_status">
                                @lang('work_order.forms.label.pdf_type')
                                <small class = "required">*</small>
                            </label>
                            <div class = "radio-horizontal-list d-flex">
                                <div class = "radio-theme-default custom-radio">
                                    <input type = "radio" class = "radio"
                                        value = "{{ \App\Enums\PdfType::Osool->value }}" id = "pdf_type1"
                                        name = "pdf_type" wire:model.live.debounce.250ms = "pdfType">
                                    <label for = "pdf_type1">
                                        <span class = "radio-text">@lang('work_order.forms.label.osool_format')</span>
                                    </label>
                                </div>
                                <div class = "radio-theme-default custom-radio">
                                    <input type = "radio" class = "radio"
                                        value = "{{ \App\Enums\PdfType::Civil->value }}" id = "pdf_type2"
                                        name = "pdf_type" wire:model.live.debounce.250ms = "pdfType">
                                    <label for = "pdf_type2">
                                        <span class = "radio-text">@lang('work_order.forms.label.civil_format')</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class = "modal-footer d-flex justify-content-between">
                        <div class = "ml-3">
                            <label>
                                <span id = "selected_count">{{ count($selectedRows) }}</span>
                                @lang('work_order.forms.label.wo_selected')
                            </label>
                        </div>
                        <div class = "button-group">
                            <button type = "button"
                                class = "btn btn-light btn-default btn-squared fw-400 text-capitalize b-light color-light"
                                data-dismiss = "modal">
                                @lang('work_order.button.cancel')
                            </button>
                            <div wire:loading class = "text-center" wire:target = "submitExportForm">
                                <button type = "button" class = "btn btn-primary" wire:loading.attr = "disabled">
                                    <span class = "spinner-border spinner-border-sm" role = "status"
                                        aria-hidden = "true"></span>
                                    @lang('work_order.common.loading')
                                </button>
                            </div>
                            <button type = "submit" class = "btn btn-primary" wire:loading.class = "hide"
                                wire:target = "submitExportForm">
                                <img src = "{{ asset('img/svg/export-icon-white.svg') }}" class = "mr-1">
                                @lang('work_order.forms.label.export')
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class = "modal fade" id = "export-confirm-modal" tabindex = "-1" role = "dialog" aria-hidden = "true"
        wire:ignore.self>
        <div class = "modal-dialog modal-dialog-centered modal-sm" role = "document">
            <div class = "modal-content">
                <div class = "modal-body">
                    <div class = "text-center">
                        <img src = "{{ asset('img/svg/check-circle.svg') }}" class = "mb-2">
                        <h3 class = "mb-3 fw-400">
                            @lang('work_order.common.successful')
                        </h3>
                        <p>
                            @lang('work_order.list.text_one_modal')
                            <a href = "{{ route('reports.manage_reports') }}" class = "fw-600">
                                @lang('work_order.list.text_two_modal')
                            </a>
                            @lang('work_order.list.page')
                        </p>
                    </div>
                </div>
                <div class = "modal-footer">
                    <button type = "button" class = "btn btn-primary"
                        data-dismiss = "modal">@lang('work_order.bread_crumbs.OK')</button>
                </div>
            </div>
        </div>
    </div>
    <div class = "modal fade" id = "export-fail-modal" tabindex = "-1" role = "dialog" aria-hidden = "true"
        wire:ignore.self>
        <div class = "modal-dialog modal-dialog-centered modal-sm" role = "document">
            <div class = "modal-content">
                <div class = "modal-body">
                    <div class = "text-center">
                        <img src = "{{ asset('img/svg/unsuccess-icon.svg') }}" class = "mb-2">
                        <h3 class = "mb-3 fw-400">@lang('work_order.common.something_went_wrong')</h3>
                        <p>@lang('work_order.common.please_try_later')</p>
                    </div>
                </div>
                <div class = "modal-footer">
                    <button type = "button" class = "btn btn-primary"
                        data-dismiss = "modal">@lang('work_order.bread_crumbs.OK')</button>
                </div>
            </div>
        </div>
    </div>
    <div class = "modal fade all-filters" id = "all-filters" tabindex = "-1" role = "dialog"
        aria-labelledby = "exampleModalLabel" aria-hidden = "true" wire:ignore.self>
        <div class = "modal-dialog" role = "document">
            <div class = "modal-content">
                <div class = "modal-header">
                    <h5 class = "modal-title" id = "exampleModalLabel">@lang('work_order.forms.label.allFilter')</h5>
                    <button type = "button" class = "close" data-dismiss = "modal" aria-label = "Close">
                        <span aria-hidden = "true">&times;</span>
                    </button>
                </div>
                <div class = "modal-body p-0 d-flex align-items-stretch filter-popup">
                    <div class = "nav nav-a flex-column nav-pills pl-2 pt-3 bg-light-grey" id = "v-pills-tab"
                        role = "tablist" aria-orientation = "vertical" wire:ignore>
                        <a class = "nav-link active" id = "nav-proerties" data-toggle = "pill"
                            href = "#tab-proerties" role = "tab" aria-controls = "v-pills-home"
                            aria-selected = "true">@lang('work_order.forms.place_holder.property')</a>

                        @include('livewire.work-orders.include.filter_building_manager_menu')

                        <a class = "nav-link {{ in_array($user->user_type, ['building_manager', 'building_manager_employee', 'supervisor']) ? 'd-none' : '' }}"
                            id = "nav-supervisor" data-toggle = "pill" href = "#tab-supervisor" role = "tab"
                            aria-controls = "tab-proerties" aria-selected = "false">@lang('data_contract.contract_forms.label.choose_supervisors')</a>
                        <a class = "nav-link" id = "nav-workers" data-toggle = "pill" href = "#tab-workers"
                            role = "tab" aria-controls = "tab-proerties"
                            aria-selected = "false">@lang('data_contract.contract_forms.label.workers')</a>
                        <a class = "nav-link" id = "nav-services" data-toggle = "pill" href = "#tab-services"
                            role = "tab" aria-controls = "tab-services"
                            aria-selected = "false">@lang('work_order.forms.label.choose_service')</a>
                        <a class = "nav-link" id = "nav-asset-number" data-toggle = "pill"
                            href = "#tab-asset-number" role = "tab" aria-controls = "tab-asset-number"
                            aria-selected = "false">@lang('work_order.bread_crumbs.assetNumber')</a>
                        <a class = "nav-link" id = "nav-type" data-toggle = "pill" href = "#tab-type"
                            role = "tab" aria-controls = "tab-type" aria-selected = "false">@lang('work_order.bread_crumbs.type')</a>
                        <a class = "nav-link" id = "nav-view-by" data-toggle = "pill" href = "#tab-view-by"
                            role = "tab" aria-controls = "tab-view-by"
                            aria-selected = "false">@lang('work_order.bread_crumbs.view_by')</a>
                        <a class = "nav-link" id = "nav-status" data-toggle = "pill" href = "#tab-status"
                            role = "tab" aria-controls = "tab-status"
                            aria-selected = "false">@lang('work_order.bread_crumbs.status')</a>
                        <a class = "nav-link" id = "nav-columns" data-toggle = "pill" href = "#tab-columns"
                            role = "tab" aria-controls = "tab-columns"
                            aria-selected = "false">@lang('work_order.bread_crumbs.show_hide_col')</a>
                        <a class = "nav-link" id = "nav-ratings" data-toggle = "pill" href = "#tab-ratings"
                            role = "tab" aria-controls = "tab-ratings"
                            aria-selected = "false">@lang('work_order.bread_crumbs.rating')</a>
                    </div>
                    <div class = "tab-content pl-2 pt-3 flex-fill mr-3" id = "v-pills-tabContent">
                        <div class = "tab-pane fade show active" id = "tab-proerties" role = "tabpanel"
                            aria-labelledby = "v-pills-home-tab" wire:ignore>
                            <div class = "check-dropdown multi-field">
                                <div class = "px-2 mb-3">
                                    <input id = "searchProperties" class = "form-control" type = "text"
                                        name = "search" placeholder = "@lang('work_order.forms.place_holder.search_by_property_name')">
                                </div>
                                @if (isset($propertiesList) && count($propertiesList) > 0)
                                    <div class = "pl-3">
                                        <input type = "checkbox" id = "all_properties" value = "All" class = "mr-2"
                                            checked>
                                        <label for = "ap">@lang('work_order.forms.place_holder.all')</label>
                                    </div>
                                    <ul id = "properties_div" class = "site-scrollbar">
                                        @foreach ($propertiesList as $row)
                                            @if ($row['property_type'] == 'building')
                                                <li class = "pl-3 property_li"
                                                    data-building = "{{ $row['building_name'] }}">
                                                    <div>
                                                        <input type = "checkbox" name = "buildings[]"
                                                            id = "{{ $row['building_id'] }}"
                                                            value = "{{ $row['building_id'] }}"
                                                            class = "mr-2 property">
                                                        <label for = "ap"
                                                            title = "{{ $row['building_name'] }}">{{ $row['building_name'] }}</label>
                                                    </div>
                                                </li>
                                            @else
                                                <li class = "pl-3 complex">
                                                    <div>
                                                        <input type = "checkbox" class="mr-2 property">
                                                        <label for = "ap">{{ $row['complex_name'] }}</label>
                                                    </div>
                                                    <ul>
                                                        @foreach ($row['property_buildings'] as $line)
                                                            <li class = "property_li"
                                                                data-building = "{{ $line['building_name'] }}">
                                                                <input type = "checkbox" name = "buildings[]"
                                                                    id = "{{ $line['id'] }}"
                                                                    value = "{{ $line['id'] }}"
                                                                    class = "mr-2 property">
                                                                <label for = "ap"
                                                                    title = "{{ $line['building_name'] }}">{{ $line['building_name'] }}</label>
                                                            </li>
                                                        @endforeach
                                                    </ul>
                                                </li>
                                            @endif
                                        @endforeach
                                    </ul>
                                @else
                                    <p class = "py-2 text-italic text-center">
                                        <i class = "las la-exclamation-circle mr-1"></i>
                                        @lang('work_order.forms.label.no_results_found')
                                    </p>
                                @endif
                                <div class = "d-flex justify-content-end mt-2">
                                    <a href = "#" class = "text-danger"
                                        id = "clear-properites">@lang('work_order.forms.label.clearSection')</a>
                                </div>
                            </div>
                        </div>

                        @include('livewire.work-orders.include.filter_building_manager_tab')

                        <div class = "tab-pane fade {{ in_array($user->user_type, ['building_manager', 'building_manager_employee', 'supervisor']) ? 'd-none' : '' }}"
                            id = "tab-supervisor" role = "tabpanel" aria-labelledby = "v-pills-profile-tab"
                            wire:ignore>
                            <div class = "check-dropdown checkbox-list multi-field1">
                                <div class = "px-2 mb-3">
                                    <input id = "searchSupervisor" class = "form-control" type = "text"
                                        name = "search" placeholder = "@lang('work_order.forms.place_holder.Search_Supervisor')" />
                                </div>
                                @if (isset($supervisorList) && count($supervisorList) > 0)
                                    <div class = "pl-3">
                                        <input type = "checkbox" id = "all_supervisors" value = "All"
                                            class = "mr-2" checked />
                                        <label for = "ap1">@lang('work_order.bread_crumbs.all')</label>
                                    </div>
                                    <ul id = "superisors_div" class = "site-scrollbar">
                                        @if ($user->user_type == 'sp_admin')
                                            <li class = "pl-3">
                                                <div>
                                                    <input type = "checkbox" name = "supervisors[]"
                                                        id = "{{ $user->id }}" value = "{{ $user->id }}"
                                                        class = "mr-2" />
                                                    <label for = "ap1"
                                                        title = "{{ $user->name }}">@lang('work_order.bread_crumbs.Myself')</label>
                                                </div>
                                            </li>
                                        @endif

                                        @foreach ($supervisorList as $row)
                                            <li class = "pl-3 supervisor_li" data-name = "{{ $row['name'] }}">
                                                <div>
                                                    <input type = "checkbox" name = "supervisors[]"
                                                        id = "{{ $row['id'] }}" value = "{{ $row['id'] }}"
                                                        class = "mr-2 supervisor" />
                                                    <label for = "ap1"
                                                        title = "{{ $row['name'] }}">{{ $row['name'] }}</label>
                                                </div>
                                            </li>
                                        @endforeach
                                    </ul>
                                @else
                                    <p class = "py-2 text-italic text-center">
                                        <i class = "las la-exclamation-circle mr-1"></i>
                                        @lang('work_order.forms.label.no_results_found')
                                    </p>
                                @endif
                                <div class = "d-flex justify-content-end mt-2">
                                    <a href = "#" class = "text-danger"
                                        id = "clear-supervisors">@lang('work_order.forms.label.clearSection')</a>
                                </div>
                            </div>
                        </div>
                        <div class = "tab-pane fade" id = "tab-workers" role = "tabpanel"
                            aria-labelledby = "v-pills-profile-tab" wire:ignore>
                            <div class = "check-dropdown checkbox-list multi-field2">
                                <div class = "px-2 mb-3">
                                    <input id = "searchWorkers" class = "form-control" type = "text"
                                        name = "search" placeholder = "@lang('work_order.forms.place_holder.Search_Workers')" />
                                </div>
                                @if (isset($workersList) && count($workersList) > 0)
                                    <div class = "pl-3">
                                        <input type = "checkbox" id = "all_workers" value = "All" class = "mr-2"
                                            checked />
                                        <label for = "ap2">@lang('work_order.bread_crumbs.all')</label>
                                    </div>
                                    <ul id = "workers_div" class = "site-scrollbar">
                                        @if ($user->user_type == 'sp_admin')
                                            <li class = "pl-3 worker_li fff" data-name = "{{ $user->name }}">
                                                <div>
                                                    <input type = "checkbox" name = "workers[]"
                                                        id = "{{ $user->id }}" value = "{{ $user->id }}"
                                                        class = "mr-2" />
                                                    <label for = "ap1"
                                                        title = "{{ $user->name }}">@lang('work_order.bread_crumbs.Myself')</label>
                                                </div>
                                            </li>
                                        @endif

                                        @foreach ($workersList as $row)
                                            <li class = "pl-3 worker_li" data-name = "{{ $row['name'] }}">
                                                <div>
                                                    <input type = "checkbox" name = "workers[]"
                                                        id = "{{ $row['id'] }}" value = "{{ $row['id'] }}"
                                                        class = "mr-2 workers" />
                                                    <label for = "ap1"
                                                        title = "{{ $row['name'] }}">{{ $row['name'] }}</label>
                                                </div>
                                            </li>
                                        @endforeach
                                    </ul>
                                @else
                                    <p class = "py-2 text-italic text-center">
                                        <i class = "las la-exclamation-circle mr-1"></i>
                                        @lang('work_order.forms.label.no_results_found')
                                    </p>
                                @endif
                                <div class = "d-flex justify-content-end mt-2">
                                    <a href = "#" class = "text-danger"
                                        id = "clear-workers">@lang('work_order.forms.label.clearSection')</a>
                                </div>
                            </div>
                        </div>
                        <div class = "tab-pane fade" id = "tab-services" role = "tabpanel"
                            aria-labelledby = "v-pills-messages-tab" wire:ignore>
                            <div class = "check-dropdown checkbox-list service-type-multi-field">
                                <div class = "px-2 mb-3">
                                    <input id = "searchServices" class = "form-control" type = "text"
                                        name = "search" placeholder = "@lang('work_order.forms.label.search_service')" />
                                </div>
                                @if (isset($servicesList) && count($servicesList) > 0)
                                    <div class = "pl-3">
                                        <input type = "checkbox" id = "service_all" value = "All" class = "mr-2"
                                            checked />
                                        <label for = "service_all">@lang('work_order.bread_crumbs.all')</label>
                                    </div>
                                    <ul id = "service_types_div" class = "site-scrollbar">
                                        @foreach ($servicesList as $row)
                                            <li class = "pl-3 asset_li"
                                                data-service = "{{ $row['asset_category'] }}">
                                                <div>
                                                    <input type = "checkbox" name = "service_types[]"
                                                        id = "{{ $row['id'] }}" value = "{{ $row['id'] }}"
                                                        class = "mr-2 service" />
                                                    <label for = "service_all"
                                                        title = "{{ $row['asset_category'] }}">
                                                        {{ $row['asset_category'] }}
                                                    </label>
                                                </div>
                                            </li>
                                        @endforeach
                                    </ul>
                                @else
                                    <p class = "py-2 text-italic text-center">
                                        <i class = "las la-exclamation-circle mr-1"></i>
                                        @lang('work_order.forms.label.no_results_found')
                                    </p>
                                @endif
                                <div class = "d-flex justify-content-end mt-2">
                                    <a href = "#" class = "text-danger"
                                        id = "clear-services">@lang('work_order.forms.label.clearSection')</a>
                                </div>
                            </div>
                        </div>
                        <div class = "tab-pane fade" id = "tab-asset-number" role = "tabpanel"
                            aria-labelledby = "v-pills-settings-tab" wire:ignore>
                            <div class = "check-dropdown">
                                <div class = "px-2 mb-3">
                                    <input id = "searchbar" class = "form-control" type = "text" name = "search"
                                        placeholder = "@lang('work_order.forms.label.searchAsset')" />
                                </div>
                                @if (isset($assets) && count($assets) > 0)
                                    <div class = "pl-3">
                                        <input type = "checkbox" id = "assets-ap" value = "All" class = "mr-2"
                                            checked />
                                        <label for = "ap">@lang('work_order.bread_crumbs.all')</label>
                                    </div>
                                    <ul id = "assets_div" class = "site-scrollbar max-h-350 overflow-y-auto">
                                        @foreach ($assets as $row)
                                            <li class = "pl-3 asset_number_li"
                                                data-number = "{{ $row['asset_symbol'] }} - {{ $row['asset_number'] }}">
                                                <div>
                                                    <input type = "checkbox" name = "assets[]"
                                                        id = "{{ $row['id'] }}" value = "{{ $row['id'] }}"
                                                        class = "mr-2 as_num" />
                                                    <label for = "{{ $row['id'] }}"
                                                        title = "{{ $row['asset_symbol'] }} - {{ $row['asset_number'] }}">{{ $row['asset_symbol'] }}
                                                        - {{ $row['asset_number'] }}</label>
                                                </div>
                                            </li>
                                        @endforeach
                                    </ul>
                                @else
                                    <p class = "py-2 text-italic text-center">
                                        <i class = "las la-exclamation-circle mr-1"></i>
                                        @lang('work_order.forms.label.no_results_found')
                                    </p>
                                @endif
                                <div class = "d-flex justify-content-end mt-2">
                                    <a href = "#" class = "text-danger"
                                        id = "clear-assets">@lang('work_order.forms.label.clearSection')</a>
                                </div>
                            </div>
                        </div>
                        <div class = "tab-pane fade" id = "tab-type" role = "tabpanel"
                            aria-labelledby = "v-pills-profile-tab" wire:ignore>
                            <ul class = "nav px-1 mb-4" id = "ap-tab" role = "tablist">
                                <li class = "nav-item">
                                    <input type = "radio" name = "type" value = "reactive" id = "reactive"
                                        style = "display: none">
                                    <a class = "nav-link type text-light border-light rounded-pill mr-2"
                                        id = "reactive-tab" data-toggle = "pill" href = "reactive" role = "tab"
                                        aria-controls = "reactive" aria-selected = "false">
                                        @lang('work_order.bread_crumbs.reactive')
                                    </a>
                                </li>
                                <li class = "nav-item">
                                    <input type = "radio" name  ="type" value = "preventive" id = "preventive"
                                        style = "display: none">
                                    <a class = "nav-link type text-light border-light rounded-pill mr-2"
                                        id = "preventive-tab" data-toggle = "pill" href = "preventive"
                                        role = "tab" aria-controls = "preventive" aria-selected = "false">
                                        @lang('work_order.bread_crumbs.preventive')
                                    </a>
                                </li>
                                <li class = "nav-item">
                                    <input type = "radio" name = "type" value = "" checked id = "type_all"
                                        style = "display: none">
                                    <a class = "nav-link type text-light border-light rounded-pill mr-2"
                                        id = "all-type" data-toggle = "pill" href = "all" role = "tab"
                                        aria-controls = "all" aria-selected = "false">
                                        @lang('work_order.bread_crumbs.all')
                                    </a>
                                </li>
                            </ul>
                        </div>
                        <div class = "tab-pane fade" id = "tab-view-by" role = "tabpanel"
                            aria-labelledby = "v-pills-profile-tab" wire:ignore>
                            <ul class = "nav px-1 passfail-tab mb-4" id = "ap-tab" role = "tablist">
                                <li class = "nav-item">
                                    <input type = "radio" name = "pass_fail" value = "pass" id = "pass"
                                        style = "display: none">
                                    <a class = "nav-link pass_fail text-light border-light rounded-pill mr-2"
                                        id = "pass-tab" data-toggle = "pill" href = "#timeline" role = "tab"
                                        aria-controls = "timeline" aria-selected = "false">
                                        @lang('work_order.bread_crumbs.pass')
                                    </a>
                                </li>
                                <li class = "nav-item">
                                    <input type = "radio" name = "pass_fail" value = "fail" id = "fail"
                                        style = "display: none">
                                    <a class = "nav-link pass_fail text-light border-light rounded-pill mr-2"
                                        id = "fail-tab" data-toggle = "pill" href = "#activity" role = "tab"
                                        aria-controls = "activity" aria-selected = "false">
                                        @lang('work_order.bread_crumbs.fail')
                                    </a>
                                </li>
                                <li class = "nav-item">
                                    <input type = "radio" checked name = "pass_fail" value = ""
                                        id = "all_pass_fail" style = "display: none">
                                    <a class = "nav-link pass_fail text-light border-light rounded-pill mr-2 active"
                                        id = "pass_fail_tab" data-toggle = "pill" href = "#ap-overview"
                                        role = "tab" aria-controls = "ap-overview" aria-selected = "false">
                                        @lang('work_order.bread_crumbs.all')
                                    </a>
                                </li>
                            </ul>
                        </div>
                        <div class = "tab-pane fade" id = "tab-status" role = "tabpanel"
                            aria-labelledby = "v-pills-profile-tab" wire:ignore>
                            <div>
                                <div class = "dropdown action-btn w-100">
                                    <div id = "filter_id_section" aria-labelledby = "dropdownMenu2"
                                        class = "status-list">
                                        <ul class = "nav px-1" id = "status-tab" role = "tablist">
                                            <li class = "nav-item mb-3">
                                                <a href = "#" data-check = "true" data-value = ""
                                                    id = "all_status_filter"
                                                    class = "status_filter nav-link type text-light border-light mr-2 rounded dropdown-item-checked">
                                                    <i class = "las la-check"></i>
                                                    @lang('work_order.bread_crumbs.all')
                                                    <i id = "tick0" class = "fa fa-check float-right"
                                                        aria-hidden = "true" style="display: none;"></i>
                                                </a>
                                            </li>
                                            <li class = "nav-item mb-3">
                                                <a href = "#" data-check = "false"
                                                    data-value = "{{ \App\Enums\WorkOrderStatus::Open->value }}"
                                                    id = "open_status_filter"
                                                    class = "status_filter nav-link type text-light border-light mr-2 rounded">
                                                    <i class = "las la-check"></i>
                                                    @lang('work_order.bread_crumbs.open')
                                                    <span
                                                        id = "open_row_count">({{ $statusCounters['open_work_orders_count'] }})</span>
                                                    <i id = "tick1" class = "fa fa-check float-right"
                                                        aria-hidden = "true" style = "display: none;"></i>
                                                </a>
                                            </li>
                                            <li class = "nav-item mb-3">
                                                <a href = "#" data-check = "false"
                                                    data-value = "{{ \App\Enums\WorkOrderStatus::InProgress->value }}"
                                                    id = "in_progress_status_filter"
                                                    class = "status_filter nav-link type text-light border-light mr-2 rounded">
                                                    <i class = "las la-check"></i>
                                                    @lang('work_order.bread_crumbs.in_progress')
                                                    <span
                                                        id = "progress_row_count">({{ $statusCounters['inprogress_work_orders_count'] }})</span>
                                                    <i id = "tick2" class = "fa fa-check float-right"
                                                        aria-hidden = "true" style = "display: none;"></i>
                                                </a>
                                            </li>
                                            <li class = "nav-item mb-3">
                                                <a href = "#" data-check = "false"
                                                    data-value = "{{ \App\Enums\WorkOrderStatus::OnHold->value }}"
                                                    id = "on_hold_status_filter"
                                                    class = "status_filter nav-link type text-light border-light mr-2 rounded">
                                                    <i class = "las la-check"></i>
                                                    @lang('work_order.bread_crumbs.on_hold')
                                                    <span
                                                        id = "hold_row_count">({{ $statusCounters['under_evaluation_work_orders_count'] }})</span>
                                                    <i id = "tick3" class="fa fa-check float-right"
                                                        aria-hidden = "true" style = "display: none;"></i>
                                                </a>
                                            </li>
                                            <li class = "nav-item mb-3">
                                                <a href = "#" data-check = "false"
                                                    data-value = "{{ \App\Enums\WorkOrderStatus::Closed->value }}"
                                                    id = "closed_status_filter"
                                                    class = "status_filter nav-link type text-light border-light mr-2 rounded">
                                                    <i class = "las la-check"></i>
                                                    @lang('work_order.bread_crumbs.closed')
                                                    <span
                                                        id = "closed_row_count">({{ $statusCounters['closed_work_orders_count'] }})</span>
                                                    <i id = "tick4" class = "fa fa-check float-right"
                                                        aria-hidden = "true" style = "display: none;"></i>
                                                </a>
                                            </li>
                                            <li class = "nav-item mb-3">
                                                <a href = "#" data-check = "false"
                                                    data-value = "{{ \App\Enums\WorkOrderStatus::Reopened->value }}"
                                                    id = "re_open_status_filter"
                                                    class = "status_filter nav-link type text-light border-light mr-2 rounded">
                                                    <i class = "las la-check"></i>
                                                    @lang('work_order.bread_crumbs.re_open')
                                                    <span
                                                        id = "reopen_row_count">({{ $statusCounters['reopened_work_orders_count'] }})</span>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class = "tab-pane fade" id = "tab-columns" role = "tabpanel"
                            aria-labelledby = "v-pills-profile-tab" wire:ignore>
                            <div id = "hideColumnsSection">
                                <p class = "mr-10 fs-14 color-light">
                                    @lang('work_order.bread_crumbs.show_hide_col')
                                </p>
                                <ul class = "nav px-1 show-hide-tab mb-4" id = "ap-tab" role = "tablist">
                                    @if (
                                        ($user->user_type == 'sp_admin' || $user->user_type == 'supervisor') &&
                                            Helper::check_global_sp($user->service_provider))
                                        <li class = "nav-item hide-show-columns cursor-pointer">
                                            <input type = "checkbox" name = "colums" value = "project_admin_id"
                                                id = "column0" style = "display: none"
                                                @if (!in_array('project_admin_id', $hiddenColumns)) checked @endif>
                                            <label
                                                class = "nav-link text-light border-light rounded-pill mr-2 cursor-pointer active"
                                                id = "colum0-tab" for = "column0">
                                                @lang('work_order.table.project_name')
                                                <i class = "las la-low-vision"></i>
                                            </label>
                                        </li>
                                    @endif
                                    <li class = "nav-item hide-show-columns cursor-pointer">
                                        <input type = "checkbox" name = "colums" value = "property"
                                            id = "column3" style = "display: none"
                                            @if (!in_array('property', $hiddenColumns)) checked @endif>
                                        <label
                                            class = "nav-link text-light border-light rounded-pill mr-2 cursor-pointer active"
                                            id = "colum3-tab" for = "column3">
                                            @lang('work_order.table.property_name')
                                            <i class = "las la-low-vision"></i>
                                        </label>
                                    </li>
                                    <li class = "nav-item hide-show-columns cursor-pointer">
                                        <input type = "checkbox" name = "colums" value = "workorder_journey"
                                            id = "column4" style = "display: none"
                                            @if (!in_array('workorder_journey', $hiddenColumns)) checked @endif>
                                        <label
                                            class = "nav-link text-light border-light rounded-pill mr-2 cursor-pointer active"
                                            id = "colum4-tab" for = "column4">
                                            @lang('work_order.table.workorder_journey')
                                            <i class = "las la-low-vision"></i>
                                        </label>
                                    </li>
                                    <li class = "nav-item hide-show-columns cursor-pointer">
                                        <input type = "checkbox" name = "colums" value = "status" id = "column5"
                                            style="display: none" @if (!in_array('status', $hiddenColumns)) checked @endif>
                                        <label
                                            class = "nav-link text-light border-light rounded-pill mr-2 cursor-pointer active"
                                            id = "colum5-tab" for = "column5">
                                            @lang('work_order.table.status')
                                            <i class = "las la-low-vision"></i>
                                        </label>
                                    </li>
                                    <li class = "nav-item hide-show-columns cursor-pointer">
                                        <input type = "checkbox" name = "colums" value = "supervisor_name"
                                            id = "column6" style = "display: none"
                                            @if (!in_array('supervisor_name', $hiddenColumns)) checked @endif>
                                        <label
                                            class = "nav-link text-light border-light rounded-pill mr-2 cursor-pointer active"
                                            id = "colum6-tab" for = "column6">
                                            @lang('work_order.table.service_provider_supervisor')
                                            <i class = "las la-low-vision"></i>
                                        </label>
                                    </li>
                                    <li class = "nav-item hide-show-columns cursor-pointer">
                                        <input type = "checkbox" name = "colums" value = "assigned_worker"
                                            id = "column7" style = "display: none"
                                            @if (!in_array('assigned_worker', $hiddenColumns)) checked @endif>
                                        <label
                                            class = "nav-link text-light border-light rounded-pill mr-2 cursor-pointer active"
                                            id = "colum7-tab" for = "column7">
                                            @lang('work_order.table.assigned_worker')
                                            <i class = "las la-low-vision"></i>
                                        </label>
                                    </li>
                                    <li class = "nav-item hide-show-columns cursor-pointer">
                                        <input type = "checkbox" name = "colums" value = "response_time"
                                            id = "column8" style = "display: none"
                                            @if (!in_array('response_time', $hiddenColumns)) checked @endif>
                                        <label
                                            class = "nav-link text-light border-light rounded-pill mr-2 cursor-pointer active"
                                            id = "colum8-tab" for = "column8">
                                            @lang('work_order.table.response_time')
                                            <i class = "las la-low-vision"></i>
                                        </label>
                                    </li>
                                    <li class = "nav-item hide-show-columns cursor-pointer">
                                        <input type = "checkbox" name = "colums" value = "pass_fail"
                                            id = "column9" style = "display: none"
                                            @if (!in_array('pass_fail', $hiddenColumns)) checked @endif>
                                        <label
                                            class = "nav-link text-light border-light rounded-pill mr-2 cursor-pointer active"
                                            id = "colum9-tab" for = "column9">
                                            @lang('work_order.table.pass_fail')
                                            <i class = "las la-low-vision"></i>
                                        </label>
                                    </li>
                                    <li class = "nav-item hide-show-columns cursor-pointer">
                                        <input type = "checkbox" name = "colums" value = "submission_date"
                                            id = "column10" style = "display: none"
                                            @if (!in_array('submission_date', $hiddenColumns)) checked @endif>
                                        <label
                                            class = "nav-link text-light border-light rounded-pill mr-2 cursor-pointer active"
                                            id = "colum10-tab" for = "column10">
                                            @lang('work_order.table.submission_date')
                                            <i class = "las la-low-vision"></i>
                                        </label>
                                    </li>
                                    <li class = "nav-item hide-show-columns cursor-pointer">
                                        <input type = "checkbox" name = "colums" value = "target_date"
                                            id = "column11" style = "display: none"
                                            @if (!in_array('target_date', $hiddenColumns)) checked @endif>
                                        <label
                                            class = "nav-link text-light border-light rounded-pill mr-2 cursor-pointer active"
                                            id = "colum11-tab" for = "column11">
                                            @lang('work_order.table.deadline')
                                            <i class = "las la-low-vision"></i>
                                        </label>
                                    </li>
                                    <li class = "nav-item hide-show-columns cursor-pointer">
                                        <input type = "checkbox" name = "colums" value = "floor" id = "column12"
                                            style = "display: none"
                                            @if (!in_array('floor', $hiddenColumns)) checked @endif>
                                        <label
                                            class = "nav-link text-light border-light rounded-pill mr-2 cursor-pointer active"
                                            id = "colum12-tab" for = "column12">
                                            @lang('data_properties.property_forms.label.floor')
                                            <i class = "las la-low-vision"></i>
                                        </label>
                                    </li>
                                    <li class = "nav-item hide-show-columns cursor-pointer">
                                        <input type = "checkbox" name = "colums" value = "room" id = "column13"
                                            style = "display: none"
                                            @if (!in_array('room', $hiddenColumns)) checked @endif>
                                        <label
                                            class = "nav-link text-light border-light rounded-pill mr-2 cursor-pointer active"
                                            id = "colum13-tab" for = "column13">
                                            @lang('data_properties.property_forms.label.room')
                                            <i class = "las la-low-vision"></i>
                                        </label>
                                    </li>
                                    @if (isset($project) && $project->use_tenant_module == 1 && $project->tenant_status != 2)
                                        <li class = "nav-item hide-show-columns cursor-pointer">
                                            <input type = "checkbox" name = "colums" value = "mr_place"
                                                id = "column14" style = "display: none"
                                                @if (!in_array('mr_place', $hiddenColumns)) checked @endif>
                                            <label
                                                class = "nav-link text-light border-light rounded-pill mr-2 cursor-pointer active"
                                                id = "colum15-tab" for = "column14">
                                                @lang('data_maintanance_request.common.place')
                                                <i class = "las la-low-vision"></i>
                                            </label>
                                        </li>
                                        <li class = "nav-item hide-show-columns cursor-pointer">
                                            <input type = "checkbox" name = "colums" value = "apartment_villa"
                                                id = "column15" style = "display: none"
                                                @if (!in_array('apartment_villa', $hiddenColumns)) checked @endif>
                                            <label
                                                class = "nav-link text-light border-light rounded-pill mr-2 cursor-pointer active"
                                                id = "colum16-tab" for = "column15">
                                                @lang('user_management_module.user_forms.label.tenants_appartment_and_villa')
                                                <i class = "las la-low-vision"></i>
                                            </label>
                                        </li>
                                    @endif
                                    <li class = "nav-item hide-show-columns cursor-pointer">
                                        <input type = "checkbox" name = "colums" value = "closed_at"
                                            id = "column16" style = "display: none"
                                            @if (!in_array('closed_at', $hiddenColumns)) checked @endif>
                                        <label
                                            class = "nav-link text-light border-light rounded-pill mr-2 cursor-pointer active"
                                            id = "colum14-tab" for = "column16">
                                            @lang('work_order.forms.label.closed_at')
                                            <i class = "las la-low-vision"></i>
                                        </label>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class = "tab-pane fade" id = "tab-ratings" role = "tabpanel"
                            aria-labelledby = "v-pills-profile-tab" wire:ignore>
                            <ul class = "nav px-1 stars-tab mb-4" id = "ap-tab2" role = "tablist">
                                <li class = "nav-item ratingsFilterTabClass1">
                                    <input type = "radio" name = "jratings" value = "5" id = "5stars"
                                        style = "display: none" class = "ratingsFilterTabClass">
                                    <a class = "nav-link ratings text-light border-light rounded-pill mr-2"
                                        id = "5stars_tab" data-toggle = "pill" href = "#timeline" role = "tab"
                                        aria-controls = "timeline" aria-selected = "false">
                                        5
                                        <i class = "lar la-star d-inline-block"></i>
                                    </a>
                                </li>
                                <li class = "nav-item ratingsFilterTabClass1">
                                    <input type = "radio" name = "jratings" value = "4" id = "4stars"
                                        style = "display: none" class = "ratingsFilterTabClass">
                                    <a class = "nav-link ratings text-light border-light rounded-pill mr-2"
                                        id = "4stars_tab" data-toggle = "pill" href = "#activity" role = "tab"
                                        aria-controls = "activity" aria-selected = "false">
                                        4
                                        <i class = "lar la-star d-inline-block"></i>
                                    </a>
                                </li>
                                <li class = "nav-item ratingsFilterTabClass1">
                                    <input type = "radio" name = "jratings" value = "3" id = "3stars"
                                        style = "display: none" class = "ratingsFilterTabClass">
                                    <a class = "nav-link ratings text-light border-light rounded-pill mr-2 active"
                                        id = "3stars_tab" data-toggle = "pill" href = "#ap-overview"
                                        role = "tab" aria-controls = "ap-overview" aria-selected = "false">
                                        3
                                        <i class="lar la-star d-inline-block"></i>
                                    </a>
                                </li>
                                <li class = "nav-item ratingsFilterTabClass1">
                                    <input type="radio" name = "jratings" value = "2" id = "2stars"
                                        style = "display: none" class = "ratingsFilterTabClass">
                                    <a class="nav-link ratings text-light border-light rounded-pill mr-2 active"
                                        id="2stars_tab" data-toggle = "pill" href = "#ap-overview"
                                        role = "tab" aria-controls = "ap-overview" aria-selected = "false">
                                        2
                                        <i class = "lar la-star d-inline-block"></i>
                                    </a>
                                </li>
                                <li class = "nav-item ratingsFilterTabClass1">
                                    <input type = "radio" name = "jratings" value = "1" id = "1stars"
                                        style = "display: none" class = "ratingsFilterTabClass">
                                    <a class = "nav-link ratings text-light border-light rounded-pill mr-2 active"
                                        id = "1stars_tab" data-toggle = "pill" href = "#ap-overview"
                                        role = "tab" aria-controls = "ap-overview" aria-selected = "false">
                                        1
                                        <i class = "lar la-star d-inline-block"></i>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class = "modal-footer">
                    <a class = "btn btn-primary apply-filters" href = "#"
                        data-dismiss = "modal">@lang('work_order.button.apply')</a>
                    <a class = "btn btn-outline-danger reset-filters" href = "#"
                        data-dismiss = "modal">@lang('work_order.button.reset')</a>
                </div>
            </div>
        </div>
    </div>
</div>
