{"__meta": {"id": "X832a538c39864ac8fcda4f956a38384e", "datetime": "2025-07-31 11:49:05", "utime": 1753951745.485079, "method": "GET", "uri": "/send-test-email", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 3, "messages": [{"message": "[11:49:04] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1753951744.571501, "xdebug_link": null, "collector": "log"}, {"message": "[11:49:04] LOG.warning: Callables of the form [\"Swift_Message\", \"Swift_Mime_SimpleMessage::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\Message.php on line 46", "message_html": null, "is_string": false, "label": "warning", "time": 1753951744.707407, "xdebug_link": null, "collector": "log"}, {"message": "[11:49:04] LOG.warning: Callables of the form [\"Swift_MimePart\", \"Swift_Mime_MimePart::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\MimePart.php on line 30", "message_html": null, "is_string": false, "label": "warning", "time": 1753951744.739517, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753951744.087846, "end": 1753951745.485114, "duration": 1.3972680568695068, "duration_str": "1.4s", "measures": [{"label": "Booting", "start": 1753951744.087846, "relative_start": 0, "end": 1753951744.538866, "relative_end": 1753951744.538866, "duration": 0.4510200023651123, "duration_str": "451ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753951744.538878, "relative_start": 0.4510319232940674, "end": 1753951745.485116, "relative_end": 1.9073486328125e-06, "duration": 0.9462380409240723, "duration_str": "946ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 36650608, "peak_usage_str": "35MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "applications.admin.email-template.register (\\resources\\views\\applications\\admin\\email-template\\register.blade.php)", "param_count": 24, "params": ["body", "details", "connection", "queue", "chainConnection", "chainQueue", "chainCatchCallbacks", "delay", "afterCommit", "middleware", "chained", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "mail::themes.default (\\resources\\views\\vendor\\mail\\html\\themes\\default.css)", "param_count": 24, "params": ["body", "details", "connection", "queue", "chainConnection", "chainQueue", "chainCatchCallbacks", "delay", "afterCommit", "middleware", "chained", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "css"}, {"name": "applications.admin.email-template.register (\\resources\\views\\applications\\admin\\email-template\\register.blade.php)", "param_count": 24, "params": ["body", "details", "connection", "queue", "chainConnection", "chainQueue", "chainCatchCallbacks", "delay", "afterCommit", "middleware", "chained", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}]}, "route": {"uri": "GET send-test-email", "middleware": "web", "uses": "Closure() {#1913\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#753 …}\n  file: \"C:\\laragon\\www\\Osool-B2G\\routes\\web.php\"\n  line: \"152 to 176\"\n}", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\laragon\\www\\Osool-B2G\\routes\\web.php&line=152\">\\routes\\web.php:152-176</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "livewire": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 1, "mails": [{"to": "<<EMAIL>>", "subject": "Welcome to Osool!", "headers": "Message-ID: <<EMAIL>>\r\nDate: Thu, 31 Jul 2025 11:49:04 +0300\r\nSubject: Welcome to Osool!\r\nFrom: Osool Team <<EMAIL>>\r\nTo: <EMAIL>\r\nMIME-Version: 1.0\r\nContent-Type: multipart/alternative;\r\n boundary=\"_=_swift_1753951744_d6afdef85d92d7b667a81e92fb1fa50b_=_\"\r\n", "body": null, "html": null}]}, "gate": {"count": 0, "messages": []}, "session": {"_token": "82Y3S2Ii8XAEUfJqJwoJcxv4amqMyZ5Eyd3DAmvL", "captcha_answer": "39", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/send-test-email\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/send-test-email", "status_code": "<pre class=sf-dump id=sf-dump-1371901359 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1371901359\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-693847827 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-693847827\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1329128728 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1329128728\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-144239031 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6IkZZZHJwRXk1ck1nM3NEaHhlMkY2d1E9PSIsInZhbHVlIjoiZ3NnY2k3TWZuQ2R5OTJ5dzRHT1k3eHhtWUFUM1JaTWN6YlRCN1JKM1hUZko5Z1h4Rnl1N2xub3A2SktJMFZDeWs1dFQ5NUtTZlB2ZEppU2o1d1BnYTQ3MDVxd1lWa1Blc2RuR1VIOTRod2NpOU5qUkowQ3phNkdXdnIyZ2FzcVAiLCJtYWMiOiI1MDYzYTAyYmEyYmRhOGFiMDhkYTBlOTc3Y2FjMmM5Y2MwNWQzMTIzZDY2Zjg3MjUyYjAwZWFiMmFmYzUwN2QzIiwidGFnIjoiIn0%3D; osool_session=i8vGCoi2ISXNXLd6CyUgdsj8VYZYvswSFfoufhft</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-144239031\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1998834667 data-indent-pad=\"  \"><span class=sf-dump-note>array:38</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6IkZZZHJwRXk1ck1nM3NEaHhlMkY2d1E9PSIsInZhbHVlIjoiZ3NnY2k3TWZuQ2R5OTJ5dzRHT1k3eHhtWUFUM1JaTWN6YlRCN1JKM1hUZko5Z1h4Rnl1N2xub3A2SktJMFZDeWs1dFQ5NUtTZlB2ZEppU2o1d1BnYTQ3MDVxd1lWa1Blc2RuR1VIOTRod2NpOU5qUkowQ3phNkdXdnIyZ2FzcVAiLCJtYWMiOiI1MDYzYTAyYmEyYmRhOGFiMDhkYTBlOTc3Y2FjMmM5Y2MwNWQzMTIzZDY2Zjg3MjUyYjAwZWFiMmFmYzUwN2QzIiwidGFnIjoiIn0%3D; osool_session=i8vGCoi2ISXNXLd6CyUgdsj8VYZYvswSFfoufhft</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">51437</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"16 characters\">/send-test-email</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"16 characters\">/send-test-email</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753951744.0878</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753951744</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1998834667\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2035452318 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">82Y3S2Ii8XAEUfJqJwoJcxv4amqMyZ5Eyd3DAmvL</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2035452318\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 08:49:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InFxOGRkZmdUNWFWaHRLR1JwcVVzSEE9PSIsInZhbHVlIjoieFpHUC9PaEtETXgvd09Sb00rWDhKek56TlZxWEw1dzJrYklrL05GNmxqdm52eVJ3b0pnWlFucDZqMzVsU2U0SmFndzlzdlRWbnIwVmxiejBjZ1Q0a1ZaY0FmbW1ZbXNZR2tlQkxtTHJpTjVXaW1vUEVqN0p2bkZ1d2UwN0VOR3giLCJtYWMiOiI1OWE1ODkwM2EzOGIxNWZmN2ZlNzdjMDY4YjlmY2Q2YWUzOTAxMTY3ZjBlNjVlZjhlN2E5YjU4MmFhMzBlZTY4IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 10:49:05 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6IjhwbXZ0ZXJIVGlzWnl5V29reGVYVUE9PSIsInZhbHVlIjoiTWc0b2ROSldPRGVHMEt3RVE3aUVGVmFQZ3RVQnBBbU5ZQmNlVWpQRFFvbmN3T0ZBSmllSFVEVzNXMUdqbkdNQ1N6SVRVMmFLWXR2MVBXOU5IRkZRMDdzMVlMelFPZnN4VkdrdEQ3MEJvV2FNeGtReHZtSzNWMVY5ditSWFUvNHgiLCJtYWMiOiIzOWFlNmFhNjQzM2IxODcxNmIwZThkZTBhOThkZWQ1YTlkOTAzODI5ZjQ5Njg0YWYwNWI3ZDcwNjRlYzZkYWEzIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 10:49:05 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InFxOGRkZmdUNWFWaHRLR1JwcVVzSEE9PSIsInZhbHVlIjoieFpHUC9PaEtETXgvd09Sb00rWDhKek56TlZxWEw1dzJrYklrL05GNmxqdm52eVJ3b0pnWlFucDZqMzVsU2U0SmFndzlzdlRWbnIwVmxiejBjZ1Q0a1ZaY0FmbW1ZbXNZR2tlQkxtTHJpTjVXaW1vUEVqN0p2bkZ1d2UwN0VOR3giLCJtYWMiOiI1OWE1ODkwM2EzOGIxNWZmN2ZlNzdjMDY4YjlmY2Q2YWUzOTAxMTY3ZjBlNjVlZjhlN2E5YjU4MmFhMzBlZTY4IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 10:49:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6IjhwbXZ0ZXJIVGlzWnl5V29reGVYVUE9PSIsInZhbHVlIjoiTWc0b2ROSldPRGVHMEt3RVE3aUVGVmFQZ3RVQnBBbU5ZQmNlVWpQRFFvbmN3T0ZBSmllSFVEVzNXMUdqbkdNQ1N6SVRVMmFLWXR2MVBXOU5IRkZRMDdzMVlMelFPZnN4VkdrdEQ3MEJvV2FNeGtReHZtSzNWMVY5ditSWFUvNHgiLCJtYWMiOiIzOWFlNmFhNjQzM2IxODcxNmIwZThkZTBhOThkZWQ1YTlkOTAzODI5ZjQ5Njg0YWYwNWI3ZDcwNjRlYzZkYWEzIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 10:49:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1546485770 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">82Y3S2Ii8XAEUfJqJwoJcxv4amqMyZ5Eyd3DAmvL</span>\"\n  \"<span class=sf-dump-key>captcha_answer</span>\" => <span class=sf-dump-num>39</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://osool-b2g.test/send-test-email</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1546485770\", {\"maxDepth\":0})</script>\n"}}