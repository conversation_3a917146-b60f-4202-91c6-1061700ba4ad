@extends('layouts.app')
@section('styles')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />
    @if (App::getLocale() == 'en')
        <link rel="stylesheet" href="{{ asset('css/workorder/style.css') }}">
    @else
        <link rel="stylesheet" href="{{ asset('css/workorder/style_ar.css') }}">
    @endif
    <style type="text/css">
        #allasset_category_checkbox,
        .asset_category_checkbox,
        .properties_checkbox,
        #allproperties_checkbox,
        .allcheckBarcode,
        .checkBarcode {
            accent-color: blue;
        }
    </style>
@endsection
@section('content')
                <div class="contents assets_managements">
        <div class="container-fluid p-0">

            <div class="row">
                <div class="col-lg-12">
                    <div class="row">


                        <div class="col-lg-12">
                            <div class="breadcrumb-main user-member justify-content-sm-between">
                                <div class="d-flex flex-wrap justify-content-center breadcrumb-main__wrapper">
                                    <!-- Title and Property Count -->
                                    <div
                                        class="d-flex align-items-center user-member__title justify-content-center mr-sm-25">
                                        <h4 class="text-capitalize fw-500 breadcrumb-title page-title__left">
                                            {{ __('assets_managements.common.assets_title_page') }}
                                        </h4>
                                        <span class="sub-title ml-sm-25 pl-sm-25">
                                            <span id="prop_count"></span>
                                            {{ __('assets_managements.common.assets') }}
                                        </span>
                                        <span class="sub-title ml-sm-25 pl-sm-25 text-primary d-none"
                                            id="selected_asset_count_zone">
                                            <span id="selected_asset_count" class="text-primary"></span>

                                        </span>
                                    </div>
                                    <!-- Search Form -->

                                </div>
                                <!-- Add New Assets Button -->
                                <div class="d-flex">
                                    <div class="action-btn">
                                        <!-- Check if user has creation privileges -->
                                        @if (Auth::user()->user_type == 'super_admin' ||
                                                Auth::user()->user_type == 'osool_admin' ||
                                                Auth::user()->user_type == 'building_manager' ||
                                                Auth::user()->user_type == 'admin' ||
                                                Auth::user()->user_type == 'admin_employee')
                                            <a href="javascript:void(0)" data-toggle="modal" data-target="#add-asset"
                                                class="btn px-15 btn-primary">
                                                <i
                                                    class="las la-plus fs-16"></i>{{ __('assets_managements.buttons.add_new_asset') }}
                                            </a>
                                        @endif
                                        @php
                                            $user_privileges = json_decode(Auth::user()->user_privileges);
                                        @endphp
                                        @if (Auth::user()->user_type == 'building_manager_employee')
                                            @if (isset($user_privileges->assets) && in_array('create', $user_privileges->assets))
                                                <a href="javascript:void(0)" data-toggle="modal" data-target="#add-asset"
                                                    class="btn px-15 btn-primary">
                                                    <i
                                                        class="las la-plus fs-16"></i>{{ __('assets_managements.buttons.add_new_asset') }}
                                                </a>
                                            @endif
                                        @endif
                                    </div>

                                        <div class="btn-group dropdown check-dropdown-toggle bg-white rounded ml-2 history-btn"
                                            data-toggle="tooltip" data-placement="bottom">
                                            <label
                                                onclick="window.location.href='{{ route('asset-management.all_history') }}';"
                                                class="py-2 dropdown-toggle pl-3 pr-3 mb-0 cursor-pointer more-btn text-dark"
                                                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                <i class="las la-history fs-18"></i>
                                                <span> {{ __('assets_managements.titles_link_btn.history') }}</span>
                                            </label>

                                        </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row" id="row-filter-form">
                        <div class="col-12">
                            <!--=========Actual Design of asset filter section End===========-->
                            <div class="py-1 project-top-wrapper project-top-progress wo-top-bar px-2 px-sm-0 assets-filter">
                                <div class="d-md-flex justify-content-md-between">
                                    <!-- Default dropup button -->
                                    <div class=" filter-bar d-md-flex justify-content-md-between w-100">
                                        <div class="">
                                            <div class="btn-group dropdown check-dropdown-toggle bg-white rounded mr-2 filter-btn"
                                                data-toggle="modal" data-target="#all-filters">
                                                <label
                                                    class="py-2 dropdown-toggle pl-3 pr-3 mb-0 cursor-pointer more-btn text-dark">
                                                    <i class="las la-sliders-h text-dark"></i>
                                                    <span>{{ __('work_order.forms.label.allFilter') }}</span>
                                                    <span class="reset-filter d-none" data-toggle="tooltip"
                                                        id="allFilter-xmark"
                                                        title="{{ __('work_order.bread_crumbs.reset_filtering') }} ">
                                                        <i class=" las la-times text-danger"></i>
                                                    </span>
                                                </label>

                                            </div>

                                            <div class="btn-group dropdown check-dropdown-toggle bg-white rounded mr-sm-2 sort-btn">
                                                <label
                                                    class="py-2 dropdown-toggle pl-3 pr-3 mb-0 cursor-pointer more-btn text-dark"
                                                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <i class="las la-sort-amount-up"></i>
                                                    <span> {{ __('assets_managements.buttons.sort_by') }}</span>
                                                </label>
                                                <div class="dropdown-menu" aria-labelledby="dropdownMenuButton"
                                                    id="filter_id_section">
                                                    <a class="dropdown-item"
                                                        data-value="new_first">{{ __('assets_managements.links.sort_by_new_first') }}
                                                    </a>
                                                    <a class="dropdown-item"
                                                        data-value="old_first">{{ __('assets_managements.links.sort_by_old_first') }}</a>
                                                    <a class="dropdown-item"
                                                        data-value="alphabetical">{{ __('assets_managements.links.sort_by_alphabetical') }}</a>
                                                </div>
                                            </div>



                                            <div class="btn-group bg-white rounded px-1 mr-2 mt-2 mt-md-0 tag-section">
                                                <div class="d-flex mr-2 dropdown-toggle position-relative" id="dropdownMenuButton">
                                                    <div
                                                        class="d-flex align-items-center tag-dropdown bg-light-grey rounded px-2">
                                                        <span data-feather="search" class="mr-2"></span>
                                                        <div class="">
                                                            <a class="" id="asset_search_by_toggle" type="button"
                                                                data-toggle="dropdown" aria-haspopup="true"
                                                                aria-expanded="false">
                                                                {{ __('assets_managements.selects.asset_tag') }}

                                                                <span data-feather="chevron-down" class="ml-2"></span>
                                                            </a>
                                                            <div class="dropdown-menu max-w-100 min-w-100 py-0"
                                                                aria-labelledby="dropdownMenuButton">
                                                                <a class="dropdown-item fs-12 px-2"
                                                                    id="dropdown-item-asset-tag"
                                                                    data-value="Asset_Tag">{{ __('assets_managements.selects.asset_tag') }}</a>
                                                                <a class="dropdown-item fs-12 px-2"
                                                                    id="dropdown-item-asset-name"
                                                                    data-value="Asset_Name">{{ __('assets_managements.selects.asset_name') }}</a>
                                                            </div>

                                                            <input type="hidden" name="asset_search_by"
                                                                id="asset_search_by" value="Asset_Tag">
                                                        </div>
                                                    </div>
                                                </div>
                                                <label class="mb-0 service_type_label cursor-pointer more-btn text-dark">
                                                    <input type="text" class="form-control h-100 border-0 pl-1"
                                                        placeholder="{{ __('assets_managements.placeholder.enter_asset_tag') }}"
                                                        id="filter_text">
                                                </label>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>


                            <!--=========Actual Design End===========-->
                            <div class="filter-form justify-content-md-between">
                                <div class="d-md-flex justify-content-md-between">
                                    {{--  <div class="btn-Modal-popup">

                                        <div class="btn-group check-dropdown-toggle w-100 bg-white rounded h-40 d-flex align-items-center"
                                            data-toggle="modal" data-target="#all-filters" aria-haspopup="true"
                                            aria-expanded="false">
                                            <label
                                                class="py-2 dropdown-toggle pl-3 pr-3 mb-0  cursor-pointer more-btn text-dark">
                                                <i class="las la-sliders-h text-dark"></i>
                                                <span>{{ __('work_order.forms.label.allFilter') }}</span>
                                                <span class="reset-filter d-none" data-toggle="tooltip" id="allFilter-xmark"
                                                    title="{{ __('work_order.bread_crumbs.reset_filtering') }} ">
                                                    <i class=" las la-times text-danger"></i>
                                                </span>
                                            </label>
                                        </div>

                                    </div> --}}

                                    {{--   <div class=" ml-2">
                                        <div class="dropdown">
                                            <button
                                                class="btn btn-sm btn-default text-dark btn-white dropdown-toggle h-100 w-100"
                                                type="button" id="dropdownMenu2" data-toggle="dropdown"
                                                aria-haspopup="true" aria-expanded="false">

                                                <i class="las la-sort-amount-up"></i>

                                                {{ __('assets_managements.buttons.sort_by') }}
                                            </button>
                                            <div class="dropdown-menu dropdown-menu-right" id="filter_id_section"
                                                aria-labelledby="dropdownMenu2">
                                                <a href="javascript:void(0)" data-value="new_first" class="dropdown-item">
                                                    {{ __('assets_managements.links.sort_by_new_first') }}
                                                    <i id="tick0" class="fa fa-check float-right" aria-hidden="true"
                                                        style="display: none;"></i>
                                                </a>
                                                <a href="javascript:void(0)" data-value="old_first" class="dropdown-item">
                                                    {{ __('assets_managements.links.sort_by_old_first') }}
                                                    <i id="tick1" class="fa fa-check float-right" aria-hidden="true"
                                                        style="display: none;"></i>
                                                </a>
                                                <a href="javascript:void(0)" data-value="alphabetical"
                                                    class="dropdown-item">
                                                    {{ __('assets_managements.links.sort_by_alphabetical') }}
                                                    <i id="tick2" class="fa fa-check float-right" aria-hidden="true"
                                                        style="display: none;"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div> --}}
                                    {{--   <div class="ml-2 d-flex">
                                        <div class="custom-search-form bg-white  d-flex ">
                                            <div class="container-select">
                                                <label class="center-icon">
                                                    <i class="las la-search fixed-rotate  "></i>
                                                </label>
                                                <select name="asset_search_by" id="asset_search_by"
                                                    class="select2  form-control">
                                                    <option value="Asset_Tag">
                                                        {{ __('assets_managements.selects.asset_tag') }}
                                                    </option>
                                                    <option value="Asset_Name">
                                                        {{ __('assets_managements.selects.asset_name') }} </option>
                                                </select>
                                            </div>
                                            <input type="text" class="custom-input"
                                                placeholder="{{ __('assets_managements.placeholder.enter_asset_tag') }}"
                                                id="filter_text">
                                        </div>

                                        <a title="{{ __('assets_managements.titles_link_btn.history') }}"
                                            href="{{ route('asset-management.all_history') }}"
                                            class="ml-2 link-history la-2x px-1"><i class="las la-history "></i></a>
                                    </div> --}}
                                </div>

                            </div>


                        </div>
                    </div>
                    {{-- Statrt Tables --}}
                    <div class="row mt-3 ">
                        <div class="col-lg-12">
                            <div id="hiddenButtons" style="display:none">
                                <ul id=""
                                    class="border-0 list-group hiddenButtons list-group-horizontal d-block ">
                                    <li class="p-0 mr-2 border-0 list-group-item no-bg d-inline-block">
                                        <button
                                            onclick="openQrCodeModal()"
                                            class="mb-3 btn btn-outline-primary hiddenButtons btn-sm w-100 mb-sm-0 no-wrap fs-sm-12"
                                            type="button">
                                            <i class="las fa-qrcode fs-16"></i>
                                            {{ __('assets_managements.buttons.DownloadQrCode') }}
                                        </button>
                                    </li>
                                    @if (Auth::user()->user_type == 'super_admin' ||
                                            Auth::user()->user_type == 'osool_admin' ||
                                            Auth::user()->user_type == 'admin' ||
                                            Auth::user()->user_type == 'admin_employee' ||
                                            (Auth::user()->user_type == 'building_manager' && Helper::loggedinUserHasAssetPrivilege('create')) ||
                                            (Auth::user()->user_type == 'building_manager_employee' && Helper::loggedinUserHasAssetPrivilege('create')))
                                        <li class="p-0 mr-2 border-0 list-group-item no-bg d-inline-block"><button
                                                onclick="CollectCheckBoxData('assets-table','assetIds')"
                                                class="mb-3 btn btn-outline-danger hiddenButtons w-100 mb-sm-0  btn-sm no-wrap fs-sm-12"
                                                data-toggle="modal" data-target="#confirmDeleteAssetsList"><i
                                                    class="las la-trash fs-16"></i>
                                                {{ __('assets_managements.buttons.deleteSelectedAssests') }}</button>
                                        </li>
                                    @endif

                                    <li class="p-0 mr-2 border-0 list-group-item no-bg d-inline-block"><button
                                            class="mb-3 btn btn-outline-dark border border-dark hiddenButtons w-100 mb-sm-0  btn-sm no-wrap fs-sm-12"
                                            data-toggle="modal" onclick="CancelCheckBox('assets-table')"><i
                                                class="las la-times-circle fs-16"></i>
                                            {{ __('assets_managements.buttons.cancel') }}</button>
                                    </li>

                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-1">
                        <div class="col-lg-12 mb-30">
                            <div class="card">
                                <div class="card-body">
                                    <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                                        <div class="table-responsive">
                                            <table class="table mb-0 table-borderless" id="assets-table">
                                                <thead>
                                                    <tr class="userDatatable-header">

                                                        <th>
                                                            <span class="projectDatatable-title"><input type="checkbox"
                                                                    id="allcheckBarcode" class="allcheckBarcode"
                                                                    name="allcheckBarcode"></span>



                                                        </th>
                                                        <th>
                                                            <span
                                                                class="projectDatatable-title">{{ __('data_properties.property_forms.label.identifier') }}</span>
                                                        </th>
                                                        <th>
                                                            <span
                                                                class="projectDatatable-title">{{ __('assets_managements.tables.assets_list.thead.asset_name') }}</span>
                                                        </th>
                                                        <th>
                                                            <div>
                                                                <span
                                                                    class="projectDatatable-title ">{{ __('assets_managements.tables.assets_list.thead.service_type') }}</span>
                                                            </div>
                                                        </th>
                                                        <th>
                                                            <div>
                                                                <span
                                                                    class="projectDatatable-title ">{{ __('assets_managements.tables.assets_list.thead.asset_tag') }}</span>
                                                            </div>
                                                        </th>
                                                        <th>
                                                            <div>
                                                                <span
                                                                    class="projectDatatable-title ">{{ __('assets_managements.tables.assets_list.thead.property_name') }}</span>
                                                            </div>
                                                        </th>
                                                        <th>
                                                            <span
                                                                class="projectDatatable-title">{{ __('assets_managements.tables.assets_list.thead.zone') }}</span>
                                                        </th>
                                                        <th>
                                                            <span
                                                                class="projectDatatable-title">{{ __('assets_managements.tables.assets_list.thead.unit') }}</span>
                                                        </th>


                                                        </th>

                                                        <th>
                                                            <span
                                                                class="projectDatatable-title">{{ __('assets_managements.tables.assets_list.thead.actions') }}</span>
                                                        </th>
                                                        <th>
                                                        </th>
                                                    </tr>
                                                </thead>

                                            </table>
                                        </div>
                                    </div><!-- End: .userDatatable -->

                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
            <div class="modal fade new-member" id="edit-asset" role="dialog" tabindex="-1"
                aria-labelledby="staticBackdropLabel" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content radius-xl">
                        <div class="modal-header">
                            <h6 class="modal-title fw-500" id="staticBackdropLabel">
                                {{ __('data_properties.property_forms.label.edit_asset') }}</h6>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span data-feather="x"></span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="create-asset-modal">
                                <div class="tab-wrapper">
                                    <div class="atbd-tab tab-horizontal">
                                        <ul class="mb-4 nav nav-tabs vertical-tabs" role="tablist">
                                            <li class="nav-item">
                                                <a class="nav-link active" id="tab-v-4-tab" data-toggle="tab"
                                                    href="#tab-v-4" role="tab" aria-controls="tab-v-4"
                                                    aria-selected="true">{{ __('data_properties.property_forms.label.basic_information_heading') }}</a>
                                            </li>
                                            <li class="nav-item">
                                                <a class="nav-link" id="tab-v-5-tab" data-toggle="tab" href="#tab-v-5"
                                                    role="tab" aria-controls="tab-v-5"
                                                    aria-selected="false">{{ __('data_properties.property_forms.label.general_information_heading') }}</a>
                                            </li>
                                            <li class="nav-item">
                                                <a class="nav-link" id="tab-v-6-tab" data-toggle="tab" href="#tab-v-6"
                                                    role="tab" aria-controls="tab-v-6"
                                                    aria-selected="false">{{ __('data_properties.property_forms.label.file_heading') }}</a>
                                            </li>

                                        </ul>
                                        <form action="{{ route('asset-management.update') }}" method="post"
                                            id="asset_edit_form" enctype="multipart/form-data">
                                            @csrf
                                            <input type="hidden" id="ajax_check_asset_url2"
                                                value="{{ URL::to('/') }}">
                                            <input type="hidden" id="ajax_check_asset_url3"
                                                value="{{ url('/') }}">
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>
            </div>
            <!-- Modal add Asset-->
            <div class="modal fade new-member" id="add-asset" role="dialog" tabindex="-1"
                aria-labelledby="staticBackdropLabel" aria-hidden="true">

                <form action="{{ route('asset-management.store') }}" method="POST" id="asset_create_form"
                    enctype="multipart/form-data">
                    @csrf
                    <input type="hidden" id="building_id" name="building_id" value="">
                    <input type="hidden" id="building_name_selected">

                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content radius-xl">
                            <div class="modal-header">
                                <h6 class="modal-title fw-500" id="staticBackdropLabel">
                                    {{ __('data_properties.property_button.Add_new_asset') }}</h6>
                                <button type="button" class="close add_new_asset" data-dismiss="modal"
                                    aria-label="Close">
                                    <span data-feather="x"></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="create-asset-modal">
                                    <div class="tab-wrapper">
                                        <div class="atbd-tab tab-horizontal">
                                            <ul class="mb-4 nav nav-tabs vertical-tabs" role="tablist">

                                                <li class="nav-item">
                                                    <a class="nav-link active" id="tab-v-1-tab" data-toggle="tab"
                                                        href="#tab-v-1" role="tab" aria-controls="tab-v-1"
                                                        aria-selected="true">{{ __('data_properties.property_forms.label.basic_information_heading') }}
                                                        <i class="ml-2 fas fa-exclamation-circle danger d-none"
                                                            id="errorExlaim"></i></a>
                                                </li>
                                                <li class="nav-item">
                                                    <a class="nav-link" id="tab-v-2-tab" data-toggle="tab"
                                                        href="#tab-v-2" role="tab" aria-controls="tab-v-2"
                                                        aria-selected="false">{{ __('data_properties.property_forms.label.general_information_heading') }}</a>
                                                </li>
                                                <li class="nav-item">
                                                    <a class="nav-link" id="tab-v-3-tab" data-toggle="tab"
                                                        href="#tab-v-3" role="tab" aria-controls="tab-v-3"
                                                        aria-selected="false">{{ __('data_properties.property_forms.label.file_heading') }}</a>
                                                </li>

                                            </ul>


                                            <div class="tab-content">
                                                <div class="tab-pane fade show active" id="tab-v-1" role="tabpanel"
                                                    aria-labelledby="tab-v-1-tab">

                                                    <div class="mb-20 form-group">
                                                        <label
                                                            for="issue_type">{{ __('data_properties.common.property_name') }}<span
                                                                class="required">*</span></label>
                                                        <select class="form-control select2" id="property_id" name="property_id">
                                                            <option selected disabled value="">
                                                                {{ __('assets_managements.placeholder.choose_property_name') }}
                                                            </option>
                                                            @foreach ($data_add['properties'] as $propertie)
                                                                <option value="{{ $propertie->id }}"
                                                                    data-building-id="{{ $propertie->building_id }}"
                                                                    data-building-name="{{ $propertie->building_name }}"
                                                                    data-building-prop_type="{{ $propertie->property_type }}"
                                                                    data-building-asset_tag="{{ $propertie->building_tag }}">
                                                                    {{ $propertie->building_name }}
                                                                </option>
                                                            @endforeach
                                                        </select>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label
                                                                    for="floor">{{ __('data_properties.property_forms.label.floor') }}<span
                                                                        class="required">*</span></label>
                                                                <div class="atbd-select">

                                                                    <select class="form-control" id="floor"
                                                                        name="floor">
                                                                        <option selected disabled value="">
                                                                            {{ __('data_properties.property_forms.place_holder.Choose_Floor') }}
                                                                        </option>

                                                                    </select>
                                                                </div>

                                                                <div id="floor_error" class="required"></div>
                                                            </div>
                                                        </div>

                                                        <div class="mb-20 col-md-6">
                                                            <div class="form-groupp">
                                                                <label
                                                                    for="region_id">{{ __('data_properties.property_forms.label.unite') }}<span
                                                                        class="required">*</span></label>
                                                                <div class="atbd-select">

                                                                    <select class="form-controll" id="room"
                                                                        name="room">
                                                                        <option selected disabled value="">
                                                                            {{ __('data_properties.property_forms.place_holder.room') }}
                                                                        </option>

                                                                    </select>
                                                                </div>
                                                                <div id="room_error2" class="required"></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="form-group ">
                                                        <label
                                                            for="region_id">{{ __('data_properties.property_forms.label.Asset_Name') }}<span
                                                                class="required">*</span></label>
                                                        <div class="atbd-select">
                                                            <select class="form-control" id="asset_name_id"
                                                                name="asset_name_id">
                                                                <option selected value="">
                                                                    {{ __('data_properties.property_forms.place_holder.Choose_name') }}
                                                                </option>
                                                                @foreach ($data_add['asset_names'] as $name)
                                                                    <option value="{{ $name->id }}"
                                                                        data="{{ $name->asset_symbol }}">
                                                                        {{ $name->asset_name }}
                                                                    </option>
                                                                @endforeach
                                                            </select>
                                                        </div>
                                                        <div id="name_error" class="required"></div>
                                                    </div>

                                                    <div class="form-group asset-category-div">
                                                        <label
                                                            for="region_id">{{ __('configration_assets.asset_categories_forms.label.service_type') }}<span
                                                                class="required">*</span></label>
                                                        <div class="atbd-select">
                                                            <select class="form-control" id="asset_category_id"
                                                                name="asset_category_id[]" multiple>
                                                                <option value="">
                                                                    {{ __('data_properties.property_forms.place_holder.Choose_Category') }}
                                                                </option>

                                                            </select>
                                                        </div>
                                                        <div id="category_error" class="required"></div>
                                                    </div>

                                                    <div class="form-group mb-20">
                                                        <input class=""type="checkbox" id="noAssetSymbol"
                                                            name="noAssetSymbol" value="1">
                                                        <label class="m-auto"
                                                            for="noAssetSymbol">{{ __('data_properties.property_forms.label.hideAssetSymbol') }}</label>
                                                    </div>

                                                    <div class="mb-20 form-group">

                                                        <label
                                                            for="issue_type">{{ __('data_properties.property_forms.label.Asset_Number') }}<span
                                                                class="required">*</span></label>
                                                        <div class="input-group">
                                                            <div class="input-group-prepend">


                                                                <input name="asset_symbol" id="asset_symbol"
                                                                    class="form-control" value=""
                                                                    placeholder="{{ __('data_properties.property_forms.place_holder.Symbol') }}"
                                                                    type="text" readonly="readonly">

                                                            </div>
                                                            <input type="text" class="form-control" value=""
                                                                placeholder="001" name="asset_number" id="asset_number"
                                                                onchange="populate_tag();">


                                                            <input type="hidden" class="form-control" id="asset_tag">

                                                            <input type="hidden" id="prop_type">

                                                        </div>


                                                        <div id="number_error" class="required"></div>
                                                    </div>

                                                    <div class="form-group mb-20" id="assetSymbolDiv">
                                                        <label
                                                            for="issue_type">{{ __('work_order.asset_information.asset_tag') }}</label>
                                                        <input type="text" class="form-control" id="asset_tag_data"
                                                            value="" readonly="readonly" disabled>
                                                    </div>

                                                    <div class="form-group">
                                                        <label
                                                            for="name1">{{ __('data_properties.property_forms.label.property_barcode_choose_one') }}<span
                                                                class="required">*</span></label>

                                                        <div class="radio-theme-default custom-radio ">
                                                            <input class="radio" type="radio" name="radio_barcode"
                                                                value="barcode_upload" id="radio_barcode_upload">
                                                            <label for="radio_barcode_upload">
                                                                <span
                                                                    class="radio-text">{{ __('data_properties.property_forms.label.upload_my_own_barcode') }}</span>
                                                            </label>
                                                        </div>
                                                        <div class="radio-theme-default custom-radio ">
                                                            <input class="radio" type="radio" name="radio_barcode"
                                                                value="barcode_generate" id="radio_barcode_generate">
                                                            <label for="radio_barcode_generate">
                                                                <span
                                                                    class="radio-text">{{ __('data_properties.property_forms.label.generate_using_osool') }}</span>
                                                            </label>
                                                        </div>

                                                        <div id="radio_barcode" class="required"></div>

                                                    </div>
                                                    <div class="mb-20 form-group" style="display:none;"
                                                        id="generateBarcode">
                                                        <div class="qr-code-generator">
                                                            <div class="mb-3 input-group d-sm-flex d-block">
                                                                <div class="input-group-prepend">
                                                                    <input type="text" name="qr_url" id="qr_url"
                                                                        class="qr-url form-control w-100" value=""
                                                                        placeholder="12345Abcde" minlength="5"
                                                                        maxlength="15" onchange="uniqueQrNumber()" />
                                                                </div>
                                                                <div class="mt-3 mt-sm-0">
                                                                    <input type="hidden" class="qr-size" value=""
                                                                        minlength="5">
                                                                    <input type="button"
                                                                        class="generate-qr-code btn btn-light btn-default btn-squared fw-400 text-capitalize b-light color-light w-100 m-w-100"
                                                                        value="{{ __('data_properties.property_forms.label.generate_barcode') }}" />
                                                                </div>
                                                                </br></br>
                                                            </div>
                                                            <div id="qr_url_error"></div>
                                                            <div id="qrcode">&nbsp</div>
                                                            <div class="qrlabel"></div>
                                                        </div>
                                                        <input type="hidden" name="qr_img_str_own" class=""
                                                            id="qr_img_str_own" value="">
                                                        <img class="qrcode-preview" src="" alt="Qrcode"
                                                            style="display: none;" />
                                                    </div>
                                                    <div class="mb-20 form-group" style="display:none;"
                                                        id="generateBarcodeByOsool">
                                                        <!-- <label for="name1">Enter barcode</label> -->
                                                        <div class="qr-code-generator">
                                                            <div class="input-group-prepend">
                                                                <input type="text" name="qr_url1" id="qr-url1"
                                                                    class="qr-url1 form-control" placeholder="12345Abcde">
                                                                <input type="hidden" class="qr-size1" value=""
                                                                    min="" max="">
                                                            </div>
                                                            <input type="button"
                                                                class="generate-qr-code1 btn btn-light btn-default btn-squared fw-400 text-capitalize b-light color-light"
                                                                value="{{ __('data_properties.property_forms.label.generate_barcode') }}">

                                                            <div id="qrcode1" class="view-img barcode-img"></div>
                                                            <div class="qrlabel"></div>

                                                        </div>
                                                        <input type="hidden" name="qr_img_str_osool" class=""
                                                            id="qr_img_str_osool" value="">
                                                    </div>

                                                    <input type="hidden" id="ajax_check_asset_url"
                                                        value="{{ route('property.ajax_check_asset_url', ['0', 'param2']) }}">



                                                </div>
                                                <div class="tab-pane fade" id="tab-v-2"
                                                    role="tabpanel"aria-labelledby="tab-v-2-tab">
                                                    <div class="form-group form-group-calender">
                                                        <label>{{ __('data_properties.property_forms.label.purchase_date') }}</label>
                                                        <div class="position-relative">
                                                            <input type="text" name="purchase_date" readonly
                                                                id="purchase_date" class=" form-control"
                                                                placeholder="{{ __('data_properties.property_forms.place_holder.purchase_date') }}">
                                                            <a id ="calendar-ic"><span data-feather="calendar"></span></a>
                                                        </div>
                                                    </div>
                                                    <div class="form-group">
                                                        <label>{{ __('data_properties.property_forms.label.model_number') }}</label>
                                                        <input type="text" name="model_number" id="model_number"
                                                            class="form-control"
                                                            placeholder="{{ __('data_properties.property_forms.place_holder.model_no') }}">
                                                        <div id="model_error"></div>
                                                    </div>
                                                    <div class="form-group">
                                                        <label>{{ __('data_properties.property_forms.label.manufacturer_name') }}</label>
                                                        <input type="text" name="manufacturer_name"
                                                            id="manufacturer_name" class="form-control"
                                                            placeholder="{{ __('data_properties.property_forms.place_holder.manufacturer_name') }}">
                                                        <div id="manufacturer_error"></div>
                                                    </div>
                                                    <!-- New Asset Information Design-->
                                                    <div class="row">
                                                        <div class="col-md-5">
                                                            <div class="form-group">
                                                                <label>{{ __('data_properties.property_forms.label.assets_status') }}
                                                                    {{ __('data_properties.property_forms.label.optional') }}</label>
                                                                <div class="atbd-select">
                                                                    <select class="form-control select2"
                                                                        name ="asset_status"id="asset_status">
                                                                        <option disabled selected value="">
                                                                            {{ __('data_properties.property_forms.label.select') }}
                                                                        </option>
                                                                        @foreach($data_add['asset_statuses'] as $status)
                                                                        <option value="{{strtolower($status->name)}}">
                                                                            {{ $status->localized_name }}
                                                                        </option>
                                                                        @endforeach
                                                                    </select>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-7 asset_damge_files_section d-none">
                                                            <div class="form-group form-group-calender">
                                                                <label>{{ __('data_properties.property_forms.label.date_of_asset_damage') }}
                                                                    {{ __('data_properties.property_forms.label.optional') }}</label>
                                                                <div class="position-relative">
                                                                    <input type="text" disabled name="date_of_damage"
                                                                        id="date_of_damage"
                                                                        class="form-control date-picker"
                                                                        placeholder="{{ __('data_properties.property_forms.label.date_of_asset_damage') }}">
                                                                    <a href="#"><span
                                                                            data-feather="calendar"></span></a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="mb-1 form-group d-none asset_damge_files_section">
                                                        <label>{{ __('data_properties.property_forms.label.files_damage') }}
                                                            {{ __('data_properties.property_forms.label.optional') }}</label>
                                                        <div class="item-inner">
                                                            <input type="hidden" name ="damage_images" value =""
                                                                id="damage_images">
                                                            <div class="item-content">
                                                                <div class="atbd-upload">
                                                                    <label style="cursor: pointer;" for="file_upload2"
                                                                        class="mb-0 btn btn-lg btn-outline-lighten btn-upload">
                                                                        <span data-feather="upload"></span
                                                                            onclick="$('#file_upload2').click()">
                                                                        {{ __('work_order.common.Upload_images') }}
                                                                        <input data-required="image" type="file"
                                                                            name="damage_files[]" id="file_upload2"
                                                                            class="image-input upload-one"
                                                                            accept="image/png, image/jpeg,image/jpg,Application/pdf"
                                                                            multiple value="">
                                                                    </label>
                                                                </div>
                                                                <div class="asset-damage">
                                                                    <!--If the file is pdf-->
                                                                    <ul class="mt-2 thumb-Images" id="imgList">

                                                                    </ul>
                                                                    <!--If the file is pdf-->

                                                                    <output id="Filelist"
                                                                        class="mt-10 image-list"></output>
                                                                </div>

                                                                <div id="logo_id_error"></div>
                                                            </div>

                                                            <!--item-content-->
                                                        </div>
                                                    </div>

                                                    <!--End New Asset Information Design-->
                                                    <div class="form-group">
                                                        <label>{{ __('data_properties.property_forms.label.general_information') }}</label>
                                                        <textarea class="form-control" name="general_information" id="general_information"
                                                            placeholder="{{ __('data_properties.property_forms.place_holder.general_info') }}"></textarea>
                                                        <div id="general_information_error"></div>
                                                    </div>



                                                </div>
                                                <div class="tab-pane fade" id="tab-v-3" role="tabpanel"
                                                    aria-labelledby="tab-v-3-tab">
                                                    <div class="form-group">
                                                        <!-- <label for="logo">File </label> -->
                                                        <div class="item-inner">
                                                            <div class="item-content">
                                                                <!-- <div class="image-upload"> <label style="cursor: pointer;" for="file_upload"> -->

                                                                <!-- <div class="h-100">
                                                                                                                                                                                           <div class="dplay-tbl">
                                                                                                                                                                                               <div class="dplay-tbl-cell"> <span data-feather="upload"></span>

                                                                                                                                                                                                   <p class="drag_drop_txt">{{ __('data_service_provider.serviceProvider_forms.place_holder.logo_drag_and_image') }}</p>
                                                                                                                                                                                                   <p class="drag_drop_txt2">{{ __('data_service_provider.serviceProvider_forms.place_holder.log_or') }} <span class="required">{{ __('data_service_provider.serviceProvider_forms.place_holder.logo_browse') }}</span> {{ __('data_service_provider.serviceProvider_forms.place_holder.logoto_choose_a_file') }}</p>
                                                                                                                                                                                               </div>
                                                                                                                                                                                           </div>
                                                                                                                                                                                       </div> -->
                                                                <!--upload-content-->
                                                                <!-- <input data-required="image" type="file" name="gen_image" id="file_upload" class="image-input" value=""> -->
                                                                <!-- </label> </div> -->

                                                                <!-- <img src="" alt="" class="uploaded-image" id="output"> -->
                                                                <!-- <label id=""></label> -->

                                                                <!-- </div> -->
                                                                <!--item-content-->

                                                                <!--Upload Files New Design-->

                                                                <div class="upload-files">
                                                                    <div class="atbd-tag-wrap">

                                                                        <div class="atbd-upload">
                                                                            <div class="atbd-upload__button">
                                                                                <a href="javascript:void(0)"
                                                                                    id="upload_file_button"
                                                                                    class="btn btn-lg btn-outline-lighten btn-upload"
                                                                                    onclick="$('#file_upload').click()">
                                                                                    <span data-feather="upload"></span>
                                                                                    {{ __('data_properties.property_forms.label.upload_files') }}</a>
                                                                                <input type="file" name="gen_image[]"
                                                                                    class="upload-one" id="file_upload"
                                                                                    multiple="">
                                                                            </div>
                                                                            <ul>
                                                                                <div class="atbd-upload__file"
                                                                                    id="hidden_label_file_name">

                                                                                </div>
                                                                            </ul>
                                                                        </div>

                                                                    </div>
                                                                </div>
                                                                <!--Upload Files New Design-->




                                                            </div>
                                                        </div>

                                                    </div>


                                                </div>

                                            </div>









                                            <div class="button-group d-flex justify-content-end pt-25">
                                                <button type="" data-dismiss="modal" aria-label="Close"
                                                    class="btn btn-light btn-default btn-squared fw-400 text-capitalize b-light color-light ">
                                                    {{ __('data_properties.property_button.cancel') }}
                                                </button>
                                                <button type="button" id="prevBtn" data-tab=""
                                                    style="display:none;"
                                                    class="btn btn-light btn-default btn-squared fw-400 text-capitalize b-light color-light">
                                                    {{ __('data_properties.property_forms.label.Previous') }}
                                                </button>
                                                <button type="submit" data-tab="basic_info"
                                                    class="btn btn-primary btn-default btn-squared text-capitalize"
                                                    value=" {{ __('data_properties.property_button.submit') }}">
                                                    {{ __('data_properties.property_button.submit') }} </button>
                                            </div>
                                        </div>

                                    </div>


                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- CONFIRM DELETE Bulk ASSET MODAL START -->

    <div class="modal new-member bouncein-new" id="confirmDeleteAssetsList" role="dialog" tabindex="-1"
        aria-labelledby="staticBackdropLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-md">
            <div class="modal-content radius-xl bouncein-new">

                <div class="modal-body">
                    <div class="create-asset-modal">
                        <h2 class="mb-1 fs-20"><i class="fa fa-exclamation-circle cursor-pointer" aria-hidden="true"></i>
                            {{ __('configration_general_settings.gen_settings_forms.tenant_view.are_you_sure_delete_asset') }}
                        </h2>

                        <!-- <p>{{ __('data_properties.property_forms.label.asset_lost_permanently') }}  </p> -->
                        <p>{{ __('configration_general_settings.gen_settings_forms.tenant_view.are_you_sure_delete_asset_msg') }}
                        </p>


                        <form action="{{ route('asset-management.deleteBulkAssets') }}" method="post"
                            id="form_delete_assets_list">


                            @csrf
                            <input type="hidden" id="assetIds" name="assetIds" value="">
                            <div class="button-group d-flex justify-content-end pt-25">


                                <div class="button-group d-flex justify-content-end pt-25">
                                    <button type="button" class="btn btn-light btn-squared text-capitalize"
                                        data-dismiss="modal" aria-label="Close">
                                        {{ __('data_properties.property_button.cancel') }}

                                    </button>
                                    <button type="submit" class="btn btn-danger btn-default btn-squared text-capitalize">
                                        {{ __('data_properties.property_button.delete') }}

                                    </button>

                                </div>

                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- CONFIRM DELETE Bulk ASSET MODAL END -->
    <!-- CONFIRM DELETE SINGLE ASSET MODAL START -->

    <div class="modal new-member bouncein-new" id="confirmDeleteOneAsset" role="dialog" tabindex="-1"
        aria-labelledby="staticBackdropLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-md">
            <div class="modal-content radius-xl bouncein-new">

                <div class="modal-body">
                    <div class="create-asset-modal">
                        <h2 class="mb-1 fs-20"><i class="fa fa-exclamation-circle cursor-pointer" aria-hidden="true"></i>
                            {{ __('configration_general_settings.gen_settings_forms.tenant_view.are_you_sure_delete_asset_unselected') }}
                        </h2>

                        <!-- <p>{{ __('data_properties.property_forms.label.asset_lost_permanently') }}  </p> -->
                        <p>{{ __('configration_general_settings.gen_settings_forms.tenant_view.are_you_sure_delete_asset_msg') }}
                        </p>
                        <form action="{{ route('asset-management.deleteBulkAssets') }}" method="post"
                            id="form_delete_one_asset">
                            @csrf
                            <input type="hidden" id="asset_Id" name="asset_Id" value="">
                            <div class="button-group d-flex justify-content-end pt-25">
                                <div class="button-group d-flex justify-content-end pt-25">
                                    <button type="button" class="btn btn-light btn-squared text-capitalize"
                                        data-dismiss="modal" aria-label="Close">
                                        {{ __('data_properties.property_button.cancel') }}

                                    </button>
                                    <button type="submit" class="btn btn-danger btn-default btn-squared text-capitalize">
                                        {{ __('data_properties.property_button.delete') }}

                                    </button>

                                </div>

                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- CONFIRM DELETE SINGLE ASSET MODAL END -->
    <!-- Modal All Filters -->
    <div class="modal fade all-filters" id="all-filters" tabindex="-1" role="dialog"
        aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">{{ __('work_order.forms.label.allFilter') }}
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body p-0 d-flex align-items-stretch filter-popup">
                    <div class="nav nav-a flex-column nav-pills pl-2 pt-3 bg-light-grey" id="v-pills-tab" role="tablist"
                        aria-orientation="vertical">
                        <a class="nav-link active" id="nav-proerties" data-toggle="pill" href="#tab-proerties"
                            role="tab" aria-controls="v-pills-home" aria-selected="true">
                            {{ __('work_order.forms.place_holder.property') }} </a>

                        <a class="nav-link" id="nav-services" data-toggle="pill" href="#tab-services" role="tab"
                            aria-controls="tab-services"
                            aria-selected="false">{{ __('assets_managements.tables.assets_list.thead.service_type') }}</a>



                        <a class="nav-link" id="nav-ratings" data-toggle="pill" href="#others_options" role="tab"
                            aria-controls="others_options"
                            aria-selected="false">{{ __('assets_managements.common.other_options') }}</a>
                    </div>
                    <div class="tab-content pl-2 pt-3 flex-fill mr-3" id="v-pills-tabContent">
                        <div class="tab-pane fade show active" id="tab-proerties" role="tabpanel"
                            aria-labelledby="v-pills-home-tab">
                            <div class="check-dropdown multi-field">

                                <div class="px-2 mb-3">
                                    <input id="searchbarPropreiete" class="form-control"
                                        onkeyup="filterOptions(this,'allproperties_checkbox', 'properties_checkbox')"
                                        type="text" name="search"
                                        placeholder="{{ __('work_order.forms.place_holder.search_by_property_name') }}">
                                </div>
                                <div class="pl-3"><input type="checkbox" id="allproperties_checkbox" value="All"
                                        class="mr-2" checked><label
                                        for="ap">{{ __('work_order.forms.place_holder.all') }}</label>
                                </div>
                                <ul class="site-scrollbar">
                                    @foreach ($data_add['properties'] as $properties)
                                        <li class="pl-3">
                                            <div><input type="checkbox" name="buildings[]"
                                                    class="properties_checkbox mr-2" id="{{ $properties->id }}"
                                                    value="{{ $properties->building_id }}"><label for="ap"
                                                    title="{{ $properties->building_name }}">{{ $properties->building_name }}</label>
                                            </div>
                                        </li>
                                    @endforeach
                                </ul>
                                <div class="d-flex justify-content-end mt-2 fixed">
                                    <a href="javascript:void(0)" onclick="resetProprieteSection()"
                                        class="text-danger">{{ __('work_order.forms.label.clearSection') }}</a>
                                </div>
                            </div>
                        </div>

                        <div class="tab-pane fade" id="tab-services" role="tabpanel"
                            aria-labelledby="v-pills-messages-tab">
                            <div class="check-dropdown checkbox-list service-type-multi-field">
                                <div class="px-2 mb-3">
                                    <input id="searchbarService" class="form-control"
                                        onkeyup="filterOptions(this,'allasset_category_checkbox', 'asset_category_checkbox')"
                                        type="text" name="search"
                                        placeholder="{{ __('work_order.forms.label.search_service') }}" />
                                </div>


                                <div class="pl-3"><input type="checkbox" id="allasset_category_checkbox"
                                        value="All" class="mr-2" checked><label
                                        for="ap">{{ __('work_order.forms.place_holder.all') }}</label>
                                </div>
                                <ul class="site-scrollbar">
                                    @foreach ($data_add['asset_category'] as $asset_category)
                                        <li class="pl-3">
                                            <div>
                                                <input type="checkbox" name="buildings[]"
                                                    class="asset_category_checkbox mr-2" id="{{ $asset_category->id }}"
                                                    value="{{ $asset_category->id }}">
                                                <label for="ap" title="{{ $asset_category->asset_category }}">
                                                    {{ $asset_category->asset_category }}</label>
                                            </div>
                                        </li>
                                    @endforeach
                                </ul>
                                <div class="d-flex justify-content-end mt-2">
                                    <a href="javascript:void(0)" onclick="resetServiceTypeSection()"
                                        class="text-danger">{{ __('work_order.forms.label.clearSection') }}</a>
                                </div>

                            </div>
                        </div>



                        <div class="tab-pane fade" id="others_options" role="tabpanel"
                            aria-labelledby="v-pills-profile-tab">

                            <ul class="nav px-1 show-hide-tab mb-4" id="ap-tab" role="tablist">
                                <li class="nav-item hide-show-columns cursor-pointer">
                                    <input type="checkbox" class="btn-checkbox" value="has_general_information"
                                        id="btn-checkbox-1" style="display: none">
                                    <label class="nav-link text-light border-light rounded-pill mr-2 cursor-pointer"
                                        id="label-btn-checkbox-1" for="btn-checkbox-1">
                                        {{ __('assets_managements.buttons.has_general_information') }} <i
                                            class="las la-plus"></i>
                                    </label>
                                </li>
                                <li class="nav-item hide-show-columns cursor-pointer">
                                    <input type="checkbox" class="btn-checkbox" value="has_manufacturer_name"
                                        id="btn-checkbox-2" style="display: none">
                                    <label class="nav-link text-light border-light rounded-pill mr-2 cursor-pointer"
                                        id="label-btn-checkbox-2" for="btn-checkbox-2">
                                        {{ __('assets_managements.buttons.has_manufacturer_name') }} <i
                                            class="las la-plus"></i>
                                    </label>
                                </li>
                                <li class="nav-item hide-show-columns cursor-pointer">
                                    <input type="checkbox" class="btn-checkbox" value="has_model_number"
                                        id="btn-checkbox-3" style="display: none">
                                    <label class="nav-link text-light border-light rounded-pill mr-2 cursor-pointer"
                                        id="label-btn-checkbox-3" for="btn-checkbox-3">
                                        {{ __('assets_managements.buttons.has_model_number') }} <i
                                            class="las la-plus"></i>
                                    </label>
                                </li>
                                <li class="nav-item hide-show-columns cursor-pointer">
                                    <input type="checkbox" class="btn-checkbox" value="has_files_damages"
                                        id="btn-checkbox-4" style="display: none">
                                    <label class="nav-link text-light border-light rounded-pill mr-2 cursor-pointer"
                                        id="label-btn-checkbox-4" for="btn-checkbox-4">
                                        {{ __('assets_managements.buttons.has_files_damages') }} <i
                                            class="las la-plus"></i>
                                    </label>
                                </li>
                                <li class="nav-item hide-show-columns cursor-pointer">
                                    <input type="checkbox" class="btn-checkbox" value="hide_asset_symbol"
                                        id="btn-checkbox-5" style="display: none">
                                    <label class="nav-link text-light border-light rounded-pill mr-2 cursor-pointer"
                                        id="label-btn-checkbox-5" for="btn-checkbox-5">
                                        {{ __('assets_managements.buttons.assets_with_hidden_symbols') }} <i
                                            class="las la-plus"></i>
                                    </label>
                                </li>
                            </ul>





                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <a class="btn btn-primary apply-filters"
                        href="javascript:void(0)">{{ __('work_order.button.apply') }}</a>
                    <a class="btn btn-outline-danger reset-filters"
                        href="javascript:void(0)">{{ __('work_order.button.reset') }}</a>
                </div>
            </div>
        </div>
    </div>
    <!-- Modal edit -->


    <!-- Modal Transfert Asset-->
    <div class="modal fade new-member" id="transfer-asset" role="dialog" tabindex="-1"
        aria-labelledby="staticBackdropLabel" aria-hidden="true">

        <form action="{{ route('asset-management.transfer') }}" method="POST" id="asset_transfer_form"
            enctype="multipart/form-data">
            @csrf
            <input type="hidden" id="building_id_to_transfer" name="building_id_to_transfer" value="">
            <input type="hidden" id="asset_id_to_transfer" name="asset_id_to_transfer" value="">


            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content radius-xl">
                    <div class="modal-header">
                        <h6 class="modal-title fw-500" id="staticBackdropLabel">
                            {{ __('assets_managements.common.tranfert_asset') }}</h6>
                        <button type="button" class="close add_new_asset" data-dismiss="modal" aria-label="Close">
                            <span data-feather="x"></span>
                        </button>
                    </div>
                    <div class="modal-body ">

                        <div class="row" id="data_transfer">
                            <div class="col-md-6">
                                <div class="mb-20 form-group">
                                    <label
                                        for="issue_type">{{ __('assets_managements.tables.assets_list.thead.asset_name') }}</label>
                                    <input class="form-control" id="old_asset_name" name="old_asset_name" readonly />

                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-20 form-group">
                                    <label
                                        for="issue_type">{{ __('assets_managements.tables.assets_list.thead.service_type') }}</label>
                                    <input class="form-control" id="old_service_type" name="old_service_type" readonly />

                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-20 form-group">
                                    <label
                                        for="issue_type">{{ __('assets_managements.tables.assets_list.thead.asset_tag') }}</label>
                                    <input class="form-control" id="old_asset_tag" name="old_asset_tag" readonly />

                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-20 form-group">
                                    <label
                                        for="issue_type">{{ __('assets_managements.tables.assets_list.thead.property_name') }}</label>
                                    <input class="form-control" id="old_property_name" name="old_property_name"
                                        readonly />

                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-20 form-group">
                                    <label
                                        for="issue_type">{{ __('assets_managements.tables.assets_list.thead.zone') }}</label>
                                    <input class="form-control" id="old_zone" name="old_zone" readonly />

                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-20 form-group">
                                    <label
                                        for="issue_type">{{ __('assets_managements.tables.assets_list.thead.unit') }}</label>
                                    <input class="form-control" id="old_unit" name="old_unit" readonly />

                                </div>
                            </div>

                        </div>
                        <hr class="mt-1 mb-3">
                        <div class="row ">

                            <div class="col-12">

                                <div class="mb-20 form-group">
                                    <label for="issue_type">{{ __('data_properties.common.property_name') }}<span
                                            class="required">*</span></label>
                                    <select class="form-control" id="property_id_to_transfer"
                                        name="property_id_to_transfer">
                                        <option selected disabled value="">
                                            {{ __('assets_managements.placeholder.choose_property_name') }}
                                        </option>
                                        @foreach ($data_add['properties'] as $propertie)
                                            <option value="{{ $propertie->id }}"
                                                data-building-id_to_transfer="{{ $propertie->building_id }}">
                                                {{ $propertie->building_name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    <div id="property_error_to_tranfer" class="required"></div>

                                </div>
                            </div>

                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="floor">{{ __('data_properties.property_forms.label.floor') }}<span
                                            class="required">*</span></label>
                                    <div class="atbd-select">

                                        <select class="form-control" id="floor_to_transfer" name="floor_to_transfer">
                                            <option selected disabled value="">
                                                {{ __('data_properties.property_forms.place_holder.Choose_Floor') }}
                                            </option>

                                        </select>
                                    </div>

                                    <div id="floor_error_to_tranfer" class="required"></div>
                                </div>
                            </div>

                            <div class="mb-20 col-md-6">
                                <div class="form-groupp">
                                    <label for="region_id">{{ __('data_properties.property_forms.label.unite') }}<span
                                            class="required">*</span></label>
                                    <div class="atbd-select">

                                        <select class="form-control" id="room_to_transfer" name="room_to_transfer">
                                            <option selected disabled value="">
                                                {{ __('data_properties.property_forms.place_holder.room') }}
                                            </option>

                                        </select>
                                    </div>
                                    <div id="room_error_to_tranfer" class="required"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row" id="row_tag_update_without_symbol" style="display: none;">
                            <div class="col-12">
                                <div class="form-group mb-20">
                                    <label for="issue_type">{{ __('assets_managements.common.tag_update') }}
                                        {{ __('data_properties.property_forms.label.optional') }}</label>
                                    <input type="text" class="form-control" id="asset_tag_data_to_transfer"
                                        value="" name="asset_tag_data_to_transfer">
                                    <div id="asset_tag_data_to_transfer_error" class="required"></div>
                                </div>
                            </div>

                        </div>
                        <div class="row" id="row_tag_update_with_symbol" style="display: none;">
                            <div class="col-12">
                                <label for="issue_type">{{ __('assets_managements.common.tag_update') }}
                                    {{ __('data_properties.property_forms.label.optional') }}</label>

                                <div class="input-group">
                                    <div class="input-group-prepend">


                                        <input name="asset_symbol_to_transfer" id="asset_symbol_to_transfer"
                                            class="form-control" value="" placeholder="Symbol" type="text"
                                            readonly="readonly">

                                    </div>
                                    <input type="text" class="form-control" name="asset_number_to_transfer"
                                        id="asset_number_to_transfer">




                                </div>
                                <div id="asset_number_to_transfer_error" class="required"></div>

                            </div>
                        </div>

                        <div class="button-group d-flex justify-content-end pt-25">
                            <button type="" data-dismiss="modal" aria-label="Close"
                                class="btn btn-light btn-default btn-squared fw-400 text-capitalize b-light color-light ">
                                {{ __('data_properties.property_button.cancel') }}
                            </button>
                            <button type="button" id="prevBtn" data-tab="" style="display:none;"
                                class="btn btn-light btn-default btn-squared fw-400 text-capitalize b-light color-light">
                                {{ __('data_properties.property_forms.label.Previous') }}
                            </button>
                            <button type="submit" data-tab="basic_info"
                                class="btn btn-primary btn-default btn-squared text-capitalize"
                                value=" {{ __('data_properties.property_button.submit') }}">
                                {{ __('data_properties.property_button.submit') }} </button>
                        </div>



                    </div>
                </div>
            </div>
        </form>
    </div>
    @include('applications.admin.assets-managements.message-export-modal')
    <input type="hidden" name="asset_name_route" value="{{ route('property.get_asset_names') }}"
        id="asset_name_route" />
    <input type="hidden" name="asset_documents_url" value="{{ route('property.asset_documents_url') }}"
        id="asset_documents_url" />
    <input type="hidden" name="get_count_asset_documents" value="{{ route('property.get_count_asset_documents') }}"
        id="get_count_asset_documents" />
    <input type="hidden" name="delete_asset_documents" value="{{ route('property.delete_asset_documents') }}"
        id="delete_asset_documents" />

    @livewire('assets-management.qr-code-modal')
@endsection
@section('scripts')
    <script type="text/javascript" charset="utf-8" src="{{ asset('js/admin/asset/assetManagement.js') }}"></script>

    <script src="https://s3-us-west-2.amazonaws.com/s.cdpn.io/130527/qrcode.js"></script>
    <script src="https://cdn.jsdelivr.net/gh/davidshimjs/qrcodejs@gh-pages/qrcode.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/js-cookie/latest/js.cookie.js"></script>
    <script>
        var assetCategoryRoute = '{{ route('property.get-asset-categories') }}';

        $("#asset_name_id").change(function() {

            $(this).find("option:selected").each(function() {

                var optionValue = $(this).attr("data");

                $('#asset_symbol').val(optionValue);

            });
        });
        $(document).on('keyup mouseup', '#asset_number', function() {
            var asset_name = $("#asset_name_id").find(":selected").attr("data") == undefined || '' ? '' : $(
                "#asset_name_id").find(":selected").attr("data");
            var asset_number = $("#asset_number").val() == undefined || '' ? '' : $("#asset_number").val();
            var asset_tag_name = asset_name + asset_number;
            if ($('#noAssetSymbol').is(":checked")) {
                var asset_tag_name = asset_number;
            } else {
                var asset_tag_name = asset_name + asset_number;
            }

            $("#asset_tag_data").val(asset_tag_name);
        });

        $('#noAssetSymbol').change(function() {
            var asset_name = $("#asset_name_id").find(":selected").attr("data");
            var asset_number = $("#asset_number").val();
            if (this.checked) {
                var asset_tag_name = asset_number;
            } else {
                var asset_tag_name = asset_name + asset_number;
            }
            $("#asset_tag_data").val(asset_tag_name);
        });

        $("#building_name_dd,#floor,#room,#asset_name_id,#roomAsset,#floorAsset,#asset_category_id_asset,#Asset_name_id,#sizeSelectAsset,#asset_number_Asset,#floorAjax,#ajax_floor,#ajax_room_type,#room_type, floor_to_transfer,room_to_transfer,.select2")
            .select2({
                placeholder: function() {
                    $(this).data('placeholder');
                },
                language: {
                    noResults: function() {
                        return translations.general_sentence.validation.No_results_found;
                    }
                }
            });
        $("#asset_category_id").select2({
            placeholder: translations.data_properties.property_forms.place_holder.Choose_Category,
            dropdownCssClass: "tag",
            language: {
                noResults: () => translations.general_sentence.validation.No_results_found,
            },
        });
        document.addEventListener('DOMContentLoaded', function() {
            $('#property_id').on('change', function() {
                var selectedOption = this.options[this.selectedIndex];
                var buildingId = selectedOption.getAttribute('data-building-id');

                var buildingName = selectedOption.getAttribute('data-building-name');
                var building_property_type = selectedOption.getAttribute('data-building-prop_type');
                var building_asset_tag = selectedOption.getAttribute('data-building-asset_tag');
                document.getElementById('building_id').value = buildingId;
                document.getElementById('building_name_selected').value = buildingName;
                document.getElementById('prop_type').value = building_property_type;
                document.getElementById('asset_tag').value = building_asset_tag;
                $("#room").empty();
                $("#room").append(
                    $("<option></option>")
                    .attr("value", "")
                    .text(translations.work_order.forms.place_holder.Choose_Room)
                );
                if (buildingId) {
                    fetchFloors(buildingId);
                } else {
                    updateFloorOptions([]);
                }
            });
        });

        function fetchFloors(buildingId) {
            $.ajax({
                url: 'get-floors',
                method: 'GET',
                data: {
                    building_id: buildingId
                },
                success: function(response) {
                    updateFloorOptions(response.floors);
                },
                error: function(xhr, status, error) {
                    console.error('Erreur:', error);
                }
            });
        }

        function updateFloorOptions(floors) {
            var floorSelect = document.getElementById('floor');
            floorSelect.innerHTML = '<option selected disabled value="">' +
                '{{ __('data_properties.property_forms.place_holder.Choose_Floor') }}' +
                '</option>';
            floors.forEach(floor => {
                var option = document.createElement('option');
                option.value = floor;
                option.textContent = floor;
                floorSelect.appendChild(option);
            });
        }

        function uniqueQrNumber() {
            var x = document.getElementById("qr_url").value;
            var property_id = document.getElementById('property_id').value;
            $.ajax({
                url: ' {{ route('property.units.getAllAssetBarcodeValues') }}?property_id=' + property_id + '',
                type: 'GET',
                dataType: 'json',
                success: function(data) {
                    if (data.includes(x)) {
                        document.getElementById("qr_url").value = '';
                        swal("Barcode should be unique");
                    }

                },
                error: function(req, err) {
                    alert: ("Request:" + JSON.stringify(req));
                }
            });
            console.log(x);
        }



        $("#purchase_date").datepicker({
            maxDate: new Date(),
            dateFormat: 'dd-mm-yy',
        })
        $("#hidden_label_file_name").hide();
        $(document).on('change', '#asset_status', function(e) {
            if (this.value == 'damaged') {
                $('#date_of_damage').prop('disabled', false)
                $('.asset_damge_files_section').removeClass('d-none')

            } else {
                $('#date_of_damage').prop('disabled', true)
                $('.asset_damge_files_section').addClass('d-none')

            }


        });

        document.addEventListener("DOMContentLoaded", init, false);
        var AttachmentArray = [];
        var arrCounter = 0;
        var filesCounterAlertStatus = false;
        var ul = document.createElement("ul");
        ul.className = "thumb-Images";
        ul.id = "imgList";

        function init() {
            document
                .querySelector("#file_upload2")
                .addEventListener("change", handleFileSelect, false);
        }
        document.total_asset_damage_files = [];
        document.total_asset_damage_files_length = 0;
        //the handler for file upload event
        function handleFileSelect(e) {
            if (!e.target.files) return;
            var files = e.target.files;
            var file_size_error = false;
            var file_type_error = false;
            for (var i = 0, f;
                (f = files[i]); i++) {
                document.total_asset_damage_files_length++
                if (document.total_asset_damage_files_length <= 3) {
                    var file_size_in_kb = (files[i].size / 10048);
                    var file_type = files[i].type;
                    var file_name = files[i].name;
                    if (file_size_in_kb > 10048) {
                        file_size_error = true;
                    }
                    var supported_types = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];

                    if (!supported_types.includes(file_type)) {
                        file_type_error = true;
                    }

                    if (file_size_error == true || file_type_error == true) {
                        var error_message = '';
                        if (file_size_error == true && file_type_error == true) {
                            error_message = translations.general_sentence.validation
                                .Please_upload_only_jpg_jpeg_png_image_of_max_size_10mb;
                        } else if (file_size_error == true && file_type_error == false) {
                            error_message = translations.general_sentence.validation
                                .File_size_should_not_be_more_than_10_mb;
                        } else {
                            error_message = translations.general_sentence.validation
                                .Please_upload_only_jpg_jpeg_png_imagezip;
                        }
                        swal({
                            title: error_message,
                            icon: "warning",
                            buttons: true,
                            dangerMode: true,
                            confirmButtonText: translations.general_sentence.button_and_links.ok,
                            buttons: [
                                translations.general_sentence.button_and_links.ok,
                            ],

                        })
                        // swal();

                        return false;
                    }

                    var data = new FormData($('#asset_create_form')[0]);
                    data.append('damage_files1', files[i])
                    data.append('triggered_from', 'damage_files')
                    $.ajax({
                        url: $('#asset_documents_url').val(),
                        dataType: 'json',
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        cache: false,
                        enctype: 'multipart/form-data',
                        contentType: false,
                        processData: false,
                        data: data,
                        type: 'post',
                        success: function(res) {
                            document.total_asset_damage_files.push(res[0]);
                        }
                    });

                }


                var fileReader = new FileReader();
                // Closure to capture the file information and apply validation.
                fileReader.onload = (function(readerEvt) {
                    return function(e) {
                        //console.log(readerEvt.type);

                        //Apply the validation rules for attachments upload
                        ApplyFileValidationRules(e, readerEvt);

                        CheckFileType(readerEvt.type);


                        //Render attachments thumbnails.

                        //Check the file count
                        if ($('#imgList li').length >= 3) {

                            toastr.error(
                                translations.work_order.common.max_3_images, {
                                    timeOut: 5000,
                                    positionClass: "toast-top-center",
                                    preventDuplicates: true,
                                    preventOpenDuplicates: true,
                                }
                            );
                            $('.custom-file').hide();

                            $('.max-file-choseen-error').show();
                        } else {

                            RenderThumbnail(e, readerEvt);

                            $('.max-file-choseen-error').hide();
                            $('.custom-file').show();

                        }


                        //Fill the array of attachment
                        FillAttachmentArray(e, readerEvt);
                    };
                })(f);
                fileReader.readAsDataURL(f);
            }
            document
                .getElementById("file_upload2")
                .addEventListener("change", handleFileSelect, false);
        }


        jQuery(function($) {
            $("div").on("click", ".img-wrap .close", function() {
                $('.custom-file').show();
                var id = $(this)
                    .closest(".img-wrap")
                    .find("img")
                    .data("id");

                //to remove the deleted item from array
                var elementPos = AttachmentArray.map(function(x) {
                    return x.FileName;
                }).indexOf(id);
                if (elementPos !== -1) {
                    AttachmentArray.splice(elementPos, 1);
                }
                document.total_asset_damage_files.splice(elementPos, 1);
                document.total_asset_damage_files_length--

                //to remove image tag
                $(this)
                    .parent()
                    .find("img")
                    .not()
                    .remove();

                //to remove div tag that contain the image
                $(this)
                    .parent()
                    .find("div")
                    .not()
                    .remove();

                //to remove div tag that contain caption name
                $(this)
                    .parent()
                    .parent()
                    .find("div")
                    .not()
                    .remove();

                //to remove li tag
                var lis = document.querySelectorAll("#imgList li");

                for (var i = 0;
                    (li = lis[i]); i++) {
                    if (li.innerHTML == "") {
                        li.parentNode.removeChild(li);
                    }
                }
            });
        });

        function clearAttachments() {
            AttachmentArray = [];
            document.total_asset_damage_files = [];
            document.total_asset_damage_files_length = 0;
            $("#imgList").empty();
            $("#imgList1").empty();
            $('.custom-file').show();
            $('.custom-file1').show();
        }

        //Apply the validation rules for attachments upload
        function ApplyFileValidationRules(e, readerEvt) {

            //To check files count according to upload conditions
            if (CheckFilesCount(AttachmentArray) == false) {
                if (!filesCounterAlertStatus) {
                    filesCounterAlertStatus = true;
                    //swal(translations.general_sentence.validation.max_3_pictures);
                    toastr.error(
                        translations.work_order.common.max_3_images, {
                            timeOut: 5000,
                            positionClass: "toast-top-center",
                            preventDuplicates: true,
                            preventOpenDuplicates: true,
                        }
                    );
                }
                e.preventDefault();
                return;
            }
        }

        //To check file type according to upload conditions
        function CheckFileType(fileType) {
            if (fileType == "application/pdf") {
                return true;

            }
            if (fileType == "image/jpeg") {
                return true;
            } else if (fileType == "image/png") {
                return true;
            } else if (fileType == "image/gif") {
                return true;
            } else {
                toastr.error(
                    translations.general_sentence.validation.Please_upload_only_jpg_jpeg_png_image, {
                        timeOut: 5000
                    }
                );
                return false;
            }
            return true;
        }

        //To check file Size according to upload conditions
        function CheckFileSize(fileSize) {
            if (fileSize < 300000) {
                return true;
            } else {
                return false;
            }
            return true;
        }

        //To check files count according to upload conditions
        function CheckFilesCount(AttachmentArray) {
            var len = 0;
            for (var i = 0; i < AttachmentArray.length; i++) {
                if (AttachmentArray[i] !== undefined) {
                    len++;
                }
            }

            //To check the length does not exceed 10 files maximum

            if (len > 2) {
                $('.custom-file').hide();
                return false;
            } else {
                return true;
            }
        }

        //Render attachments thumbnails.
        function RenderThumbnail(e, readerEvt) {
            var pdfImage = '<?= asset('img/pdf.png') ?>';

            var li = document.createElement("li");

            ul.appendChild(li);
            if (readerEvt.name.split(".").includes('pdf')) {
                li.innerHTML = [
                    '<div class="img-wrap"> <span class="close">&times;</span>' +
                    '<img class="thumb" src="',
                    pdfImage,
                    '" title="',
                    escape(readerEvt.name),
                    '" data-id="',
                    readerEvt.name,
                    '"/>' + "</div>"
                ].join("");
            } else {
                li.innerHTML = [
                    '<div class="img-wrap"> <span class="close">&times;</span>' +
                    '<img class="thumb" src="',
                    e.target.result,
                    '" title="',
                    escape(readerEvt.name),
                    '" data-id="',
                    readerEvt.name,
                    '"/>' + "</div>"
                ].join("");
            }


            var div = document.createElement("div");
            div.className = "FileNameCaptionStyle";
            li.appendChild(div);
            div.innerHTML = [readerEvt.name].join("");
            document.getElementById("Filelist").insertBefore(ul, null);
        }

        function FillAttachmentArray(e, readerEvt) {
            AttachmentArray[arrCounter] = {
                AttachmentType: 1,
                ObjectType: 1,
                FileName: readerEvt.name,
                FileDescription: "Attachment",
                NoteText: "",
                MimeType: readerEvt.type,
                Content: e.target.result.split("base64,")[1],
                FileSizeInBytes: readerEvt.size
            };
            arrCounter = arrCounter + 1;
        }

        $.extend($.fn.dataTable.defaults, {
            language: {
                "processing": translations.general_sentence.empty_ui.processing,
                "lengthMenu": translations.general_sentence.length_menu,
                "emptyTable": "{{ __('assets_managements.tables.global.assets_empty_table') }}",
            },
        });

        function updatePlaceholder() {
            const selectedValue = $('#asset_search_by').val();
            const placeholderText = selectedValue === 'Asset_Name' ?
                "{{ __('assets_managements.placeholder.enter_asset_name') }}" :
                "{{ __('assets_managements.placeholder.enter_asset_tag') }}";

            $('#filter_text').attr('placeholder', placeholderText);
        }
        $(document).ready(function() {

            setupCheckboxHandler('allproperties_checkbox', 'properties_checkbox');
            setupCheckboxHandler('allasset_category_checkbox', 'asset_category_checkbox');

            var assetstable = $('#assets-table').DataTable({
                responsive: true,
                autoWidth: false,
                processing: true,
                serverSide: true,
                searching: false,

                paging: true,
                info: false,
                stateSave: true,
                dom: '<"top"i>rt<"bottom"flp><"clear">',
                lengthChange: true,
                oLanguage: {
                    oPaginate: {
                        sNext: current_locale == "ar" ?
                            '<span class="pagination-fa"><i class="fa fa-chevron-left" ></i></span>' :
                            '<span class="pagination-fa"><i class="fa fa-chevron-right" ></i></span>',
                        sPrevious: current_locale == "ar" ?
                            '<span class="pagination-fa"><i class="fa fa-chevron-right" ></i></span>' :
                            '<span class="pagination-fa"><i class="fa fa-chevron-left" ></i></span>',
                    },
                    url: current_locale == "ar" ?
                        "//cdn.datatables.net/plug-ins/9dcbecd42ad/i18n/Arabic.json" :
                        "//cdn.datatables.net/plug-ins/9dcbecd42ad/i18n/English.json",
                },
                "drawCallback": function(settings) {
                    if (settings.json.recordsTotal < 10) {
                        $('.paginate_button').hide()
                        $('#assets-table_length').hide()
                    } else {
                        $('.paginate_button').show()
                        $('#assets-table_length').show()
                    }
                    $('#prop_count').html(settings.json.recordsTotal);
                    $('#' + settings.nTable.id + ' .checkBarcode').each(function() {
                        if (selectedCheckboxes.includes(this.value)) {
                            $(this).prop('checked', true);
                        }
                    });
                    updateAllCheckboxState(settings.nTable.id);

                },
                ajax: {
                    url: '{{ route('asset-management.index') }}',
                    data: function(d) {
                        d.asset_search_by = $('#asset_search_by').val();
                        d.filter_text = $('#filter_text').val();
                        d.sort_by = $('#filter_id_section .dropdown-item.active').data('value');
                        d.properties = [];
                        $('input.properties_checkbox:checked').each(function() {
                            d.properties.push($(this).val());
                        });

                        d.asset_categories = [];
                        var allCheckboxes = $('input.asset_category_checkbox');
                        var allChecked = true;

                        allCheckboxes.each(function() {
                            if (!$(this).is(':checked')) {
                                allChecked = false;
                            }
                        });

                        if (allChecked) {
                            d.asset_categories = null;
                        } else {
                            allCheckboxes.each(function() {
                                if ($(this).is(':checked')) {
                                    d.asset_categories.push($(this).val());
                                }
                            });
                        }

                        d.other_options = [];
                        $('#ap-tab input.btn-checkbox:checked').each(function() {
                            console.log('Checked Other Option:', $(this)
                                .val());
                            d.other_options.push($(this).val());
                        });

                    }
                },
                initComplete: function() {
                    $('#assets-table').on('processing.dt', function(e, settings, processing) {
                        $('#overlayer-again').css('display', processing ? 'block' : 'none');
                    });
                },
                columns: [{
                        data: 'checkbox',
                        name: 'checkbox',
                        orderable: false,
                        searchable: false
                    },
                    {
                        data: 'asset_identifier',
                        name: 'asset_identifier',
                        orderable: false,
                        searchable: false
                    },
                    {
                        data: 'asset_name',
                        name: 'asset_name',
                        orderable: false,
                        searchable: false
                    },
                    {
                        data: 'service_type',
                        name: 'service_type',
                        orderable: false,
                        searchable: false
                    },
                    {
                        data: 'asset_tag',
                        name: 'asset_tag',
                        orderable: false,
                        searchable: false
                    },
                    {
                        data: 'property_name',
                        name: 'property_name',
                        orderable: false,
                        searchable: false
                    },
                    {
                        data: 'zone',
                        name: 'zone',
                        orderable: false,
                        searchable: false
                    },
                    {
                        data: 'unit',
                        name: 'unit',
                        orderable: false,
                        searchable: false
                    },

                    {
                        data: 'actions',
                        name: 'actions',
                        orderable: false,
                        searchable: false
                    }
                ]
            });

            $('#filter_text').on('keyup', function() {

                assetstable.draw();
            });
            $('#asset_search_by').on('change', function() {
                updatePlaceholder();
                if ($('#filter_text').val().trim() !== '') {
                    assetstable.draw();
                }

            });

            $('#filter_id_section .dropdown-item').on('click', function() {
                $('#filter_id_section .dropdown-item').removeClass('active');
                $(this).addClass('active');
                assetstable.draw();
            });


            function ApplyFilter() {
                assetstable.draw();
                toggleLoadingWithDelay(1000);
                $('#all-filters').modal('hide');
            }

            function toggleLoadingWithDelay(timeInMilliseconds) {
                setLoading(true);
                setTimeout(function() {
                    setLoading(false);
                }, timeInMilliseconds);
            }

            function ResetFilter() {
                resetProprieteSection();
                resetServiceTypeSection();
                resetOtherOptionsSection(false);
                assetstable.draw();
                $('#all-filters').modal('hide');
                toggleAllFilterXmark();
            }
            $('.apply-filters').on('click', function() {
                ApplyFilter();
                toggleAllFilterXmark();
            });

            $('.reset-filters').on('click', function() {
                ResetFilter();
            });
            document.getElementById('allFilter-xmark').addEventListener('click', function(event) {
                event.stopPropagation();
                ResetFilter();
            });

        });
        var selectedCheckboxes = [];

        function toggleAllCheckboxes(tableId) {
            var isChecked = $('#allcheckBarcode').prop('checked');
            $('#' + tableId + ' .checkBarcode').prop('checked', isChecked);
            if (isChecked) {
                $('#' + tableId + ' .checkBarcode:checked').each(function() {
                    if (!selectedCheckboxes.includes(this.value)) {
                        selectedCheckboxes.push(this.value);
                    }
                });
            } else {
                $('#' + tableId + ' .checkBarcode').each(function() {
                    var index = selectedCheckboxes.indexOf(this.value);
                    if (index !== -1) {
                        selectedCheckboxes.splice(index, 1);
                    }
                });
            }
            toggleHiddenButtons(tableId);
        }


        function updateAllCheckboxState(tableId) {
            if ($('#' + tableId + ' .checkBarcode').length > 0 &&
                $('#' + tableId + ' .checkBarcode:checked').length ===
                $('#' + tableId + ' .checkBarcode').length) {
                $('#allcheckBarcode').prop('checked', true);
            } else {
                $('#allcheckBarcode').prop('checked', false);
            }
            toggleHiddenButtons(tableId);
        }

        $('#allcheckBarcode').change(function() {
            toggleAllCheckboxes('assets-table');
        });


        $(document).on('change', '#assets-table .checkBarcode', function() {
            var checkbox = $(this);
            var value = checkbox.val();

            if (checkbox.is(':checked')) {
                if (!selectedCheckboxes.includes(value)) {
                    selectedCheckboxes.push(value);
                }
            } else {
                var index = selectedCheckboxes.indexOf(value);
                if (index !== -1) {
                    selectedCheckboxes.splice(index, 1);
                }
            }
            updateAllCheckboxState('assets-table');
            toggleHiddenButtons('assets-table');
        });

        function toggleHiddenButtons(tableId) {
            if (selectedCheckboxes.length > 0) {
                $('#hiddenButtons').show();
                $('#row-filter-form').hide();

                $('#selected_asset_count_zone').removeClass('d-none');
                if (selectedCheckboxes.length === 1) {
                    $('#selected_asset_count').html('<span class="text-primary">' + selectedCheckboxes.length + '</span> ' +
                        "{{ __('assets_managements.common.one_asset_selected') }}");
                } else {
                    $('#selected_asset_count').html('<span class="text-primary">' + selectedCheckboxes.length + '</span> ' +
                        "{{ __('assets_managements.common.assets_selected') }}");
                }

            } else {
                $('#hiddenButtons').hide();
                $('#row-filter-form').show();
                $('#selected_asset_count_zone').addClass('d-none');
            }
        }

        function CancelCheckBox(tableId) {
            $('#' + tableId + ' .checkBarcode').prop('checked', false);
            $('#' + tableId + ' #allcheckBarcode').prop('checked', false);
            selectedCheckboxes = [];
            toggleHiddenButtons(tableId);
        }

        function CollectCheckBoxData(tableId, field) {
            var data = selectedCheckboxes;
            document.getElementById(field).value = data;
        }

        function setIdAssetForDelete(assetId) {
            $('#confirmDeleteOneAsset').modal('show');
            document.getElementById('asset_Id').value = assetId;

        }
        /* function to intergair action of a checkbox option 'AllCheckBox' to a List of Checkboxes */
        function setupCheckboxHandler(allCheckboxId, checkboxClass) {
            const allCheckbox = document.getElementById(allCheckboxId);
            const checkboxes = document.querySelectorAll(`.${checkboxClass}`);

            function initializeCheckboxes() {
                if (allCheckbox.checked) {
                    checkboxes.forEach(checkbox => {
                        checkbox.checked = true;
                    });
                } else {
                    const allChecked = Array.from(checkboxes).every(checkbox => checkbox.checked);
                    allCheckbox.checked = allChecked;
                }
            }
            allCheckbox.addEventListener('change', function() {
                checkboxes.forEach(checkbox => {
                    checkbox.checked = allCheckbox.checked;
                });
            });
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const allChecked = Array.from(checkboxes).every(checkbox => checkbox.checked);
                    allCheckbox.checked = allChecked;
                });
            });
            initializeCheckboxes();
        }

        function filterOptions(input, allCheckboxId, classListCheckbox) {
            const filter = input && input.value ? input.value.trim().toLowerCase() : '';
            const checkboxes = document.querySelectorAll(`.${classListCheckbox}`);
            const allCheckboxContainer = document.getElementById(allCheckboxId).parentElement;
            let allVisible = true;

            checkboxes.forEach(checkbox => {
                const label = checkbox.nextElementSibling.textContent || checkbox.nextElementSibling.innerText;
                if (label.toLowerCase().includes(filter)) {
                    checkbox.parentElement.style.display = '';
                } else {
                    checkbox.parentElement.style.display = 'none';
                    allVisible = false;
                }
            });
            if (allVisible) {
                allCheckboxContainer.style.display = '';
                const visibleCheckboxes = Array.from(checkboxes)
                    .filter(checkbox => checkbox.parentElement.style.display !== 'none');
                const allChecked = visibleCheckboxes.length > 0 && visibleCheckboxes.every(checkbox => checkbox.checked);
                document.getElementById(allCheckboxId).checked = allChecked;
            } else {
                allCheckboxContainer.style.display = 'none';
            }
        }

        function setCheckboxState(allCheckboxId, status) {
            const allCheckbox = document.getElementById(allCheckboxId);

            if (!allCheckbox) {
                console.error(`Checkbox with ID "${allCheckboxId}" not found.`);
                return;
            }
            allCheckbox.checked = status;
            allCheckbox.dispatchEvent(new Event('change'));
        }

        function resetInput(inputId) {
            const inputElement = document.getElementById(inputId);

            if (inputElement) {
                inputElement.value = '';
                inputElement.placeholder = inputElement.getAttribute('placeholder');
            } else {
                console.error(`Input with ID "${inputId}" not found.`);
            }
        }

        function resetProprieteSection() {
            setCheckboxState('allproperties_checkbox', 'true');
            resetInput('searchbarPropreiete');
            filterOptions('searchbarPropreiete', 'allproperties_checkbox', 'properties_checkbox');
        }

        function resetServiceTypeSection() {
            setCheckboxState('allasset_category_checkbox', 'true');
            resetInput('searchbarService');
            filterOptions('allasset_category_checkbox', 'allasset_category_checkbox', 'asset_category_checkbox');
        }

        function resetOtherOptionsSection(status) {
            $('.btn-checkbox').each(function() {
                $(this).prop('checked', status);
                var label = $('label[for="' + $(this).attr('id') + '"]');
                if (status) {
                    label.find('i').removeClass('la-plus').addClass('la-minus');
                } else {
                    label.find('i').removeClass('la-minus').addClass('la-plus');
                }
            });
        }




        document.addEventListener('DOMContentLoaded', function() {
            const apTab = document.getElementById('ap-tab');
            const checkboxes = apTab.querySelectorAll('.btn-checkbox');
            checkboxes.forEach(checkbox => {
                const label = document.querySelector(`label[for="${checkbox.id}"]`);
                updateIcon(checkbox, label);
                label.addEventListener('click', function(event) {
                    event.preventDefault();
                    checkbox.checked = !checkbox.checked;
                    updateIcon(checkbox, label);
                });
            });

            function updateIcon(checkbox, label) {
                const icon = label.querySelector('i');
                if (checkbox.checked) {
                    icon.classList.remove('la-plus');
                    icon.classList.add('la-minus');
                } else {
                    icon.classList.remove('la-minus');
                    icon.classList.add('la-plus');
                }
            }
        });
        $('#edit-asset').on('hidden.bs.modal', function() {
            clearAttachments();
            $(this).find('.nav-tabs a:first').tab('show');
            {{ Session::forget('token') }}
        });

        $('#add-asset').on('hidden.bs.modal', function() {
            clearAttachments();
            $(this).find('.nav-tabs a:first').tab('show');
            {{ Session::forget('token') }}
        });

        function transferAsset(id, buildingId, assetName, serviceType, assetTag, propertyName, zone, unit, hideAssetSymbol,
            assetNumber, assetSymbol) {
            document.getElementById('old_asset_name').value = assetName;
            document.getElementById('old_service_type').value = serviceType;
            document.getElementById('old_asset_tag').value = assetTag;
            document.getElementById('old_property_name').value = propertyName;
            document.getElementById('old_zone').value = zone;
            document.getElementById('old_unit').value = unit;
            var select = document.getElementById('property_id_to_transfer');
            var options = select.getElementsByTagName('option');
            for (var i = 0; i < options.length; i++) {
                var option = options[i];
                var optionBuildingId = option.getAttribute('data-building-id_to_transfer');
                if (optionBuildingId == buildingId) {
                    option.style.display = 'none';
                } else {
                    option.style.display = 'block';
                }
            }
            document.getElementById('asset_id_to_transfer').value = id;
            if (hideAssetSymbol == 1) {
                document.getElementById('row_tag_update_without_symbol').style.display = 'block';
                document.getElementById('row_tag_update_with_symbol').style.display = 'none';
                document.getElementById('asset_tag_data_to_transfer').placeholder = assetTag;
            } else {
                document.getElementById('row_tag_update_without_symbol').style.display = 'none';
                document.getElementById('row_tag_update_with_symbol').style.display = 'block';
                document.getElementById('asset_symbol_to_transfer').value = assetSymbol;
                document.getElementById('asset_number_to_transfer').placeholder = '';

                document.getElementById('asset_number_to_transfer').placeholder = assetNumber;
            }
            $('#transfer-asset').modal('show');
        }


        function transfertfetchFloors(buildingId) {
            $.ajax({
                url: 'get-floors',
                method: 'GET',
                data: {
                    building_id: buildingId
                },
                success: function(response) {
                    transfertupdateFloorOptions(response.floors);
                },
                error: function(xhr, status, error) {
                    console.error('Erreur:', error);
                }
            });
        }

        function transfertupdateFloorOptions(floors) {
            var floorSelect = document.getElementById('floor_to_transfer');
            floorSelect.innerHTML = '<option selected disabled value="">' +
                '{{ __('data_properties.property_forms.place_holder.Choose_Floor') }}' +
                '</option>';
            floors.forEach(floor => {
                var option = document.createElement('option');
                option.value = floor;
                option.textContent = floor;
                floorSelect.appendChild(option);
            });
        }
        document.getElementById('property_id_to_transfer').addEventListener('change', function() {
            var selectedOption = this.options[this.selectedIndex];
            var buildingId = selectedOption.getAttribute('data-building-id_to_transfer');
            document.getElementById('building_id_to_transfer').value = buildingId;
            $("#room_to_transfer").empty();
            $("#room_to_transfer").append(
                $("<option></option>")
                .attr("value", "")
                .text(translations.work_order.forms.place_holder.Choose_Room)
            );
            if (buildingId) {
                transfertfetchFloors(buildingId);
            } else {
                transfertupdateFloorOptions([]);
            }
        });


        $(document).on('change', '#asset_transfer_form #floor_to_transfer', function(e) {
            e.preventDefault();
            var floor = $(this).find(":selected").val();
            var building_id = $('#building_id_to_transfer').val();
            var app_url = $('#app_url').val();
            $.ajax({
                type: 'POST',
                url: app_url + "/property/asset/get_asset_rooms",
                data: {
                    '_token': $("input[name='_token']").val(),
                    floor: floor,
                    building_id: building_id
                },
                beforeSend: function(xhr) {
                    xhr.overrideMimeType("text/plain; charset=x-user-defined");
                },
                dataType: "json",
                success: function(data) {

                    $("#room_to_transfer").empty();
                    $("#room_to_transfer").append(
                        $("<option></option>")
                        .attr("value", "")
                        .text(translations.work_order.forms.place_holder.Choose_Room)
                    );
                    $.each(data, function(key, value) {
                        $("#room_to_transfer").append(
                            $("<option></option>")
                            .attr("value", value.id + ',,,' + value.room)
                            .text(value.room)
                        );
                    });
                },
                error: function(data) {
                    var errors = $.parseJSON(data.responseText);
                    toastr.error(data, translations.general_sentence.validation.Error, {
                        timeOut: 1500,
                        positionClass: "toast-top-center",
                        progressBar: true,
                        preventDuplicates: true,
                        preventOpenDuplicates: true,
                    });
                },
            });
        });


        $("#asset_transfer_form").validate({
            ignore: "",
            rules: {
                floor_to_transfer: {
                    required: true
                },
                room_to_transfer: {
                    required: true
                },
                property_id_to_transfer: {
                    required: true
                },
                asset_number_to_transfer: {
                    minlength: 3,
                    maxlength: 45,
                    remote: {
                        url: "{{ route('asset-management.verifAssetNumber') }}",
                        type: "post",
                        data: {
                            asset_symbol: function() {
                                return $('#asset_symbol_to_transfer').val();
                            },
                            asset_number: function() {
                                return $('#asset_number_to_transfer').val();
                            },
                            asset_id: function() {
                                return $('#asset_id_to_transfer').val();
                            },
                            locale: "en",
                            _token: $('meta[name="csrf-token"]').attr("content"),

                        },


                    },
                },
                asset_tag_data_to_transfer: {
                    minlength: 3,
                    maxlength: 45,
                    remote: {
                        url: "{{ route('asset-management.verifAssetTag') }}",
                        type: "post",
                        data: {
                            asset_number: function() {
                                return $('#asset_tag_data_to_transfer').val();
                            },
                            asset_id: function() {
                                return $('#asset_id_to_transfer').val();
                            },
                            locale: "en",
                            _token: $('meta[name="csrf-token"]').attr("content"),

                        },


                    },
                },
            },
            messages: {
                floor_to_transfer: {
                    required: translations.general_sentence.validation.This_field_is_required,
                },
                room_to_transfer: {
                    required: translations.general_sentence.validation.This_field_is_required,
                },
                property_id_to_transfer: {
                    required: translations.general_sentence.validation.This_field_is_required,
                },
                asset_number_to_transfer: {
                    minlength: translations.general_sentence.validation.Please_enter_3_numbers,
                    maxlength: translations.general_sentence.validation.Please_enter_3_numbers,
                    remote: translations.general_sentence.validation.Asset_already_exist_Enter_different_asset,
                },
                asset_tag_data_to_transfer: {
                    minlength: translations.general_sentence.validation.Please_enter_3_numbers,
                    maxlength: translations.general_sentence.validation.Please_enter_3_numbers,
                    remote: translations.general_sentence.validation.Asset_already_exist_Enter_different_asset,
                },

            },
            errorPlacement: function(error, element) {
                error.addClass("invalid-feedback");
                if (element.attr("name") == " floor_to_transfer") {
                    error.appendTo($("#floor_error_to_tranfer"));
                } else if (element.attr("name") == "room_to_transfer") {
                    error.appendTo($("#room_error_to_tranfer"));
                } else if (element.attr("name") == "property_id_to_transfer") {
                    error.appendTo($("#property_error_to_tranfer"));
                } else if (element.attr("name") == "asset_number_to_transfer") {
                    error.appendTo($("#asset_number_to_transfer_error"));
                } else if (element.attr("name") == "asset_tag_data_to_transfer") {
                    error.appendTo($("#asset_tag_data_to_transfer_error"));
                } else {
                    error.insertAfter(element);
                }

            },
            highlight: function(element, errorClass, validClass) {
                $(element).addClass("is-invalid");
            },
            unhighlight: function(element, errorClass, validClass) {
                $(element).removeClass("is-invalid");
            },
            submitHandler: function(form) {
                event.preventDefault();
                transferAssetWithAjax(form);

            }
        });


        function transferAssetWithAjax(form) {
            var formData = new FormData(form);
            formData.append('_token', $('meta[name="csrf-token"]').attr('content'));
            setLoading(true);
            $.ajax({
                url: $(form).attr('action'),
                type: $(form).attr('method'),
                data: formData,
                contentType: false,
                processData: false,
                success: function(response) {
                    $('#transfer-asset').modal('hide');
                    $('#assets-table').DataTable().ajax.reload();
                    setLoading(false);
                    var message = response.message +
                        " {{ __('assets_managements.messages.transfer_success_message') }}";

                    showSuccessMessage(message);
                },
                error: function(xhr) {
                    console.log('Transfer ERROR :');
                    console.log(xhr.responseText);
                    showErrorMessage("{{ __('assets_managements.messages.transfer_error_message') }}");
                    $('#transfer-asset').modal('hide');
                    setLoading(false);

                },
                complete: function() {
                    CancelCheckBox('assets-table');
                }
            });
        }

        function editAssetWithAjax(form) {
            var formData = new FormData(form);
            formData.append('_token', $('meta[name="csrf-token"]').attr('content'));
            setLoading(true);
            $.ajax({
                url: $(form).attr('action'),
                type: $(form).attr('method'),
                data: formData,
                contentType: false,
                processData: false,
                success: function(response) {
                    $('#edit-asset').modal('hide');
                    $('#assets-table').DataTable().ajax.reload();
                    setLoading(false);
                    showSuccessMessage("{{ __('assets_managements.messages.update_success_message') }}");
                },
                error: function(xhr) {
                    console.log('Edit ERROR :');
                    console.log(xhr.responseText);
                    setLoading(false);
                    showErrorMessage("{{ __('assets_managements.messages.edit_error_message') }}");
                    $('#edit-asset').modal('hide');

                },
                complete: function() {
                    CancelCheckBox('assets-table');
                }
            });
        }

        function addAssetWithAjax(form) {
            var asset_category_id = $("#asset_category_id").val();
            var formData = new FormData(form);
            formData.append('_token', $('meta[name="csrf-token"]').attr('content'));
            formData.delete('asset_category_id[]');
            formData.append('asset_category_id', JSON.stringify(asset_category_id));
            if ($('#asset_create_form input[name="radio_barcode"]:checked').val() === 'barcode_upload') {
                if ($('#qr_url').val() == null || $('#qr_url').val().length < 5) {
                    return false;
                }

                $('#qrcode').empty();
                $('.qrlabel').empty();

                let enterText11 = $('#qr_url').val();
                new QRCode(document.getElementById("qrcode"), {
                    text: enterText11,
                    width: 100,
                    height: 100,
                    colorDark: "#000000",
                    colorLight: "#ffffff",
                    correctLevel: QRCode.CorrectLevel.H
                });

                var label = enterText11;
                if ($(".dynamicqrlabel")) {
                    $(".dynamicqrlabel").remove();
                }
                $('.qrlabel').append(`<div class="ml-2 dynamicqrlabel"><h5>${label}</h5></div>`);

                setTimeout(function() {
                    let dataUrl = document.querySelector('#qrcode').querySelector('img').src;
                    $('#qr_img_str_own').val(dataUrl);
                    $('.qrcode-preview').attr('src', dataUrl);
                }, 1000);

                if ($('#qr_url').val() == null) {
                    return false;
                }
            }
            setLoading(true);
            $.ajax({
                url: $(form).attr('action'),
                type: $(form).attr('method'),
                data: formData,
                contentType: false,
                processData: false,
                success: function(response) {
                    $('#add-asset').modal('hide');
                    $('#assets-table').DataTable().ajax.reload();
                    if (response.success) {
                        showSuccessMessage(response.message);
                    } else {
                        showErrorMessage(response.message);
                        console.log(response.message);
                    }
                    resetFormAddAssets('add-asset');
                    setLoading(false);

                },
                error: function(xhr) {
                    console.log('Add New Assets ERROR :');
                    console.log(xhr.responseText);
                    setLoading(false);
                    showErrorMessage("{{ __('assets_managements.messages.add_error_message') }}");
                },
                complete: function() {
                    CancelCheckBox('assets-table');

                }
            });
        }

        $("#form_delete_one_asset").submit(function(e) {
            e.preventDefault();
            deleteSubmitFormViaAjax(
                '#form_delete_one_asset',
                '#confirmDeleteOneAsset',
                "{{ __('assets_managements.messages.delete_success_message') }}",
                "{{ __('assets_managements.messages.delete_error_message') }}"
            );
        });

        $("#form_delete_assets_list").submit(function(e) {
            e.preventDefault();
            deleteSubmitFormViaAjax(
                '#form_delete_assets_list',
                '#confirmDeleteAssetsList',
                "{{ __('assets_managements.messages.bulk_delete_success_message') }}",
                "{{ __('assets_managements.messages.bulk_delete_error_message') }}"
            );
        });

        function deleteSubmitFormViaAjax(formSelector, modalSelector, successMessage, errorMessage) {
            var form = $(formSelector);
            var submitButton = form.find('button[type="submit"]');
            $(modalSelector).modal('hide');
            setLoading(true);
            var formData = new FormData(form[0]);
            $.ajax({
                url: form.attr('action'),
                type: form.attr('method'),
                data: formData,
                contentType: false,
                processData: false,
                success: function(response) {
                    $('#assets-table').DataTable().ajax.reload();
                    setLoading(false);
                    showSuccessMessage(successMessage);
                },
                error: function(xhr) {
                    console.log(xhr.responseText);
                    showErrorMessage(errorMessage);
                },
                complete: function() {
                    CancelCheckBox('assets-table');
                    setLoading(false);

                }
            });
        }




        function showSuccessMessage(message) {
            toastr.success(message, translations.general_sentence.tostr_lang.success, {
                timeOut: 2200,
                progressBar: true,
                positionClass: 'toast-top-center',
            });
        }

        function showErrorMessage(message) {
            toastr.error(message, translations.general_sentence.tostr_lang.error, {
                timeOut: 2200,
                progressBar: true,
                positionClass: 'toast-top-center',
            });
        }

        function existFilter() {
            const allPropertiesChecked = document.getElementById('allproperties_checkbox').checked;
            const allAssetCategoryChecked = document.getElementById('allasset_category_checkbox').checked;
            const btnCheckboxes = document.querySelectorAll('.btn-checkbox');
            const anyBtnCheckboxChecked = Array.from(btnCheckboxes).some(checkbox => checkbox.checked);
            return !allPropertiesChecked || !allAssetCategoryChecked || anyBtnCheckboxChecked;
        }

        function toggleAllFilterXmark() {
            const allFilterXmark = document.getElementById('allFilter-xmark');
            if (existFilter()) {
                allFilterXmark.classList.remove('d-none');
            } else {
                allFilterXmark.classList.add('d-none');
            }
        }
        document.getElementById('dropdown-item-asset-tag').addEventListener('click', function(e) {
            e.preventDefault();
            updateDropdown('dropdown-item-asset-tag');
        });

        document.getElementById('dropdown-item-asset-name').addEventListener('click', function(e) {
            e.preventDefault();
            updateDropdown('dropdown-item-asset-name');
        });


        function updateDropdown(itemId) {
            var selectedItem = document.getElementById(itemId);
            var selectedValue = selectedItem.getAttribute('data-value');
            var selectedText = selectedItem.textContent.trim();
            document.getElementById('asset_search_by_toggle').textContent = selectedText;
            document.getElementById('asset_search_by').value = selectedValue;
            $('#asset_search_by').trigger('change');
            updatePlaceholder();
        }
        function resetFormAddAssets(containerId) {
            const container = document.getElementById(containerId);
            if (!container) {
                console.error(`Container with ID "${containerId}" not found.`);
                return;
            }
            const inputs = container.querySelectorAll('input');
            inputs.forEach(input => {
                if (input.type === 'checkbox' || input.type === 'radio') {
                    input.checked = false;
                } else if (input.type === 'file') {
                    input.value = '';
                } else {
                    input.value = '';
                }
            });
            const textareas = container.querySelectorAll('textarea');
            textareas.forEach(textarea => {
                textarea.value = '';
            });

            const normalSelects = container.querySelectorAll('select');
            normalSelects.forEach(select => {
                select.selectedIndex = 0;
            });
            resetSelect2ToDefault(containerId, 'floor');
            resetSelect2ToDefault(containerId, 'room');
            resetSelect2ToDefault(containerId, 'asset_name_id');
            resetSelect2ToDefault(containerId, 'asset_category_id');
            $('#generateBarcodeByOsool').hide();
            $('#generateBarcode').hide();

        }


        function resetSelect2ToDefault(containerId, selectId) {
            const container = document.getElementById(containerId);
            if (!container) {
                console.error(`Container with ID "${containerId}" not found.`);
                return;
            }
            const selectElement = container.querySelector(`#${selectId}`);
            if (!selectElement) {
                console.error(`Select element with ID "${selectId}" not found in container "${containerId}".`);
                return;
            }
            $(selectElement).val(null).trigger('change.select2');
        }

        function openQrCodeModal() {
            if (selectedCheckboxes.length === 0) {
              alert('Please select at least one asset.');
              return;
            }
            Livewire.emit('assetOpenModal', selectedCheckboxes);
        }
    </script>
@endsection
