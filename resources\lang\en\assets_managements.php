<?php


     /*
    |--------------------------------------------------------------------------
    |  Assets Management Page Translations
    |--------------------------------------------------------------------------
    | English version file
    | This file contains translations for labels, buttons, messages
    | and other text elements used in the assets management section
     */

   return [
    'common' => [
   'assets_title_page' =>'Assets',
   'assets' =>'Assets',
   'assetsQrCode' =>'Assets-QrCode',
   'empty_qr_code' =>'No QR code available',
   'other_options' =>'Other Options',
   'tranfert_asset' =>'Transfer Asset',
   'tag_update' =>'Asset Tag Update',
   'basic_information' =>'Basic Information',
   'general_information' =>'General Information',
   'purchase_date'=>'Purchase Date',
   'model_number'=>'Model Number',
   'manufacturer_name'=>'Manufacturer Name',
   'assets_status'=>'Asset Status',
   'assets_files'=>'Files',
   'asset_Qrcode'=>'Asset Qr Code',
   'asset_work_order'=>'Asset Work Orders',
   'work_orders'=>'Work Orders',
   'asset_hitory'=>'Asset History',
   'assets_hitorys'=>'Assets History',
   'hitory'=>'History',
  'assets_selected' => 'Assets Selected',
'one_asset_selected' => 'Asset Selected',
'new_module' => 'New',
'download' => 'Download',
'close' => 'Close',
'asset_qr_code_error' => 'Invalid asset QR code or doesn’t exist.',
'return_assets' => 'Return to assets list',
    ],

    'buttons' => [
         'add_new_asset' => 'Add New Asset',
         'sort_by' => 'Sort By',
         'DownloadSelectedbarcode'=>'Download Selected QR code',
         'deleteSelectedAssests'=>'Delete Selected Assets',
         'cancel'=>'Cancel',
         'has_general_information' => 'Has General Information',
        'has_manufacturer_name' => 'Has Manufacturer Name',
        'has_model_number' => 'Has Model Number',
        'has_files_damages' => 'Has Files of Damage',
        'assets_with_hidden_symbols' => 'Assets with Hidden Symbols',
        'DownloadQrCode'=>'Download QR code',
        'download'=>'Download',
        'view'=>'View',
        'delete_processing' => 'Deleting',
        'history'=>'History',
        'sticker_type'=>'Adhesive type for thermal printing'
    ],
    'links' => [
         'sort_by_new_first' => 'New First',
         'sort_by_old_first' => 'Old First',
         'sort_by_alphabetical' => 'Alphabetical',
    ],
    'messages' => [
        'add_success_message' => 'Asset has been added',
        'add_error_message' => 'An error occurred while adding the asset',
       'transfer_success_message' => 'Asset has been transferred ',
       'transfer_error_message' => 'An error occurred while transferring the asset',
      'update_success_message' => 'Asset has been updated',
    'update_error_message' => 'An error occurred while updating the asset',
       'delete_success_message' => 'Asset have been deleted ',
    'delete_error_message' => 'An error occurred while deleting the asset',
    'bulk_delete_success_message' => 'Assets have been deleted',
    'bulk_delete_error_message' => 'An error occurred while deleting assets',
    'download_error' => 'An error occurred while downloading QR Codes',
        'sure_delete_asset' => 'Are you sure you want to delete this asset?',
        'sub_sure_delete_asset' => 'Caution: Deleting asset will be affecting associated Work Orders',
        'no_general_information' =>'There are no general information added to this asset',
        'no_files_uploaded' =>'There are no files uploaded to this asset',
        'no_qr_code' =>'There are QR Code generated to this asset',
        'no_work_orders' =>'There are no work orders related to this asset',
        'no_asset_history' =>'There are no history records to this asset',

    ],
    'titles_link_btn' => [
         'rotate' => 'Rotate',
         'view' => 'View',
         'transfer' => 'Transfer',
         'edit' => 'Edit',
         'remove' => 'Remove',
         'history' => 'History',
    ],
    'selects' => [
       'asset_tag' => 'Asset Tag',
       'asset_name' => 'Asset Name',
    ],
    'placeholder' => [
         'choose_property_name' => 'Choose Property',
         'enter_name_or_number' => 'Enter name or number',
         'enter_asset_name' => 'Enter asset name',
         'enter_asset_tag' => 'Enter asset tag',

    ],
    'tables' => [
        'assets_list' => [
            'thead' => [
                'asset_name' => 'Asset Name',
                'service_type' => 'Service Type',
                'asset_tag' => 'Asset Tag',
                'asset_number' => 'Asset Number',
                'property_name' => 'Property Name',
                'unit' => 'Unit',
                'zone' => 'Zone',
                'actions' => 'Actions',
            ],
        ],
        'work_orders_list' => [
            'thead' => [
                'work_order_id' => 'Work Order ID',
                'work_order_date_time' => 'Date & Time',
                'work_order_status' => 'Status',
            ],
        ],
        'asset_history_list' => [
            'thead' => [
                'log_type' => 'Log Type',
                'action_taker' => 'Action Taker',
            ],
            'logs_types' => [
                'edited' => 'Edited',
                'added' => 'Added',
                'transferred' => 'Transferred',
                'deleted' => 'Deleted',

            ],
        ],
        'global' => [
            'assets_empty_table' => 'No assets found',
            'history_empty_table' => 'No history found',

        ],
    ],

    'custodian'=>[
        'Add Custodian' => 'Add Custodian',
        'Manual Entry' => 'Manual Entry',
        'Select Custodian' => 'Select Custodian',
        'Full Name' => 'Full Name',
        'Email' => 'Email',
        'Mobile Number' => 'Mobile Number',
        'Start Date' => 'Start Date',
        'End Date' => 'End Date',
        'Cancel' => 'Cancel',
        'Save' => 'Save',
        'Current Custodians' => 'Current Custodians',
        'Name' => 'Name',
        'add_success'=>'Custodian added successfully.'
    ]
];
