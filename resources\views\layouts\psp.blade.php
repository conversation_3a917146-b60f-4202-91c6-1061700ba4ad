<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ (Session::get('locale')=='ar' ? 'rtl' : 'ltr') }}">
<style>
    [type="search"]::-webkit-search-cancel-button,
    [type="search"]::-webkit-search-decoration {
        -webkit-appearance: none;
    }

    input[type="search" i] {
        -webkit-appearance: none !important;
    }
</style>
@if(url()->current() == url('/').'/workspace/post-create-admin')
<script>
    function DisableBackButton() {
        window.history.forward()
    }

    DisableBackButton();
    window.onload = DisableBackButton;
    window.onpageshow = function (evt) {
        if (evt.persisted) DisableBackButton()
    }
    window.onunload = function () {
        void (0)
    }
</script>
@endif

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title @if(App::getLocale()=='en' ) lang="en" @else lang="ar" @endif>@lang('import.osool')</title>

    <meta name="description" content="@yield('page_description', $pageDescription ?? '')" />

    @livewireStyles

    <link rel="stylesheet" href="{{ asset('new_theme/css/new_font.css')}}">

    @include('layouts.partials._styles')

    @yield('styles')

    <link rel="apple-touch-icon" sizes="180x180" href="{{ asset('favicon/apple-touch-icon.png')}}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ asset('favicon/favicon-32x32.png')}}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ asset('favicon/favicon-16x16.png')}}">
    <link rel="shortcut icon" type="image/png" sizes="32x32" href="{{ asset('favicon/favicon-32x32.png')}}">
    <link rel="shortcut icon" type="image/png" sizes="16x16" href="{{ asset('favicon/favicon-16x16.png')}}">
    <link rel="manifest" href="{{ asset('favicon/site.webmanifest')}}">
    <link rel="mask-icon" href="{{ asset('favicon/safari-pinned-tab.svg')}}" color="#5bbad5">

    <script src="{{ asset('new_theme/js/axios.min.js')}}"
        integrity="sha512-0qU9M9jfqPw6FKkPafM3gy2CBAvUWnYVOfNPDYKVuRTel1PrciTj+a9P3loJB+j0QmN2Y0JYQmkBBS8W+mbezg=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>

    @if(App::getLocale()=='ar')
    <style>
        .toggle-icon {
            transform: rotate(180deg);
        }
    </style>
    @endif
</head>
@if (Request()->route()->getPrefix() == '/maintenance')

<body class="layout-light side-menu">
    @include('layouts.partials._header_maintanance')
    @else

    <body class="layout-light side-menu @auth() overlayScroll @endauth">
        @endif
        <input type="hidden" id="app_url" value="<?= url(''); ?>">
        @if (Request()->route()->getPrefix() == '/maintenance')
        @elseif(in_array(Route::current()->getName(), ['password.request', 'password.reset', 'update_reset_password',
        'reset_password']))
        @else
        @auth()
        <div class="mobile-search"></div>
        <div class="mobile-author-actions"></div>
        @include('layouts.partials._header')
        @endauth
        @endif
        <!-- for reset password no sidebar -->
        @if(Request()->route()->getPrefix() != '/maintenance')
        <main class="main-content">
            @endif
            @auth()
            @if(Request()->route()->getPrefix() != '/maintenance')
            @if (Route::is('workspace.home') || Route::is('workspace.admin.create'))
            <livewire:menu.aside-workspace-list />
            @else
            @if(in_array(Route::current()->getName(), ['password.request', 'password.reset', 'update_reset_password',
            'reset_password']))
            @else
            <livewire:menu.aside-nav-list />
            @endif
            @endif
            @endif
            @endauth

            @if (Request()->route()->getPrefix() == '/maintenance')
            <!--@include('layouts.partials._aside_maintanance')-->
            @endif

            @auth
            @php
            $url = url()->current();
            $segments = explode('/', $url);
            @endphp

            @if(auth()->user()->allow_akaunting && (in_array('inventory', $segments) || in_array('sales', $segments) ||
            in_array('purchases', $segments) || in_array('performance-indicator', $segments)))
            @include('layouts.partials._akaunting_debug')
            @endif
            @endauth
            <header
                class="site-header site-header--menu-right landing-1-menu site-header--absolute site-header--sticky bg-white shadow-sm">
                <div class="container-fluid">
                    <nav class="navbar site-navbar">
                        <!-- Brand Logo-->
                        <div class="brand-logo">
                            <a href="{{ url('/') }}">
                                <!-- light version logo (logo must be black)-->
                                <img src="{{ asset('home/image/home/<USER>') }}" alt="" class="light-version-logo">
                                <!-- Dark version logo (logo must be White)-->
                                <img src="{{ asset('home/image/home/<USER>') }}" alt="" class="dark-version-logo">
                            </a>
                        </div>
                        <div class="menu-block-wrapper">
                            <div class="menu-overlay"></div>
                            <nav class="menu-block" id="append-menu-header">
                                <div class="mobile-menu-head">
                                    <img src="{{ asset('home/image/home/<USER>') }}" alt="" class="">
                                    <div class="go-back">
                                        <i class="fa fa-angle-left"></i>
                                    </div>
                                    <div class="current-menu-title"></div>
                                    <div class="mobile-menu-close">&times;</div>
                                </div>
                                <ul class="site-menu-main">
                                    <li class="nav-item">
                                        <a href="#menu1" class="nav-link-item"> {{__('landing_page.menu.wahts_osool')}}</a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="#menu2" class="nav-link-item">{{__('landing_page.menu.osool_advantage')}} </a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="#menu3" class="nav-link-item">
                                            {{__('landing_page.menu.beneficiaries_of_osool')}}</a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="#menu4" class="nav-link-item"> {{__('landing_page.menu.contact_us')}}</a>
                                    </li>
                                    <li class="nav-item mt-sm-0 mt-3 mt-sm-0">
                                        <span class="nav-link-item no-hover pr-0">
                                            <a href="{{ route('psp-registration.index') }}"
                                                class="btn btn-border-lb btn-hover-slide position-relative overflow-hidden"><span
                                                    class="rounded d-block position-relative"> Sign Up</span></a>
                                        </span>
                                    </li>
                                    <li class="nav-item mt-sm-0 mt-3 mt-sm-0">
                                        <span class="nav-link-item no-hover pr-0">
                                            <a href="javascript:void(0);"
                                                class="btn btn-border-lb btn-hover-slide position-relative overflow-hidden"
                                                data-bs-toggle="modal" data-bs-target="#osool-popup"><span
                                                    class="rounded d-block position-relative">
                                                    {{__('landing_page.menu.get_started')}}</span></a>
                                        </span>
                                    </li>
                                    <li class="d-flex align-items-center">
                                        <span class="nav-link-item no-hover">
                                            <a class="btn bg-db text-white focus-reset lan-btn" href="{{ url('login') }}">
                                                {{__('landing_page.menu.login')}}
                                            </a>
                                        </span>
                                    </li>
                                    <li class="nav-item">
                                        @if (App::getLocale()=='en')
                                        <a href="{{route('changeLanguage'," ar")}}" class="nav-link-item lang-btn"><span
                                                class="rounded d-block">{{__('landing_page.menu.en_ar')}}</span></a>
                                        @elseif (App::getLocale()=='ar')
                                        <a href="{{route('changeLanguage'," en")}}" class="nav-link-item lang-btn"><span
                                                class="rounded d-block">{{__('landing_page.menu.en_ar')}}</span></a>
                                        @endif

                                    </li>
                                    <li class="d-flex align-items-center">
                                        <div class="user-dropdown-wrapper">
                                            <div class="user-dropdown-trigger">
                                                <i class="fas fa-user-circle"></i>
                                                <span class="welcome-text">Welcome Mohammed</span>
                                                <i class="fas fa-chevron-down"></i>
                                            </div>
                                            <div class="user-dropdown-menu">
                                                <a href="#" class="dropdown-item">
                                                    <i class="fas fa-user-cog"></i>
                                                    Profile Management
                                                </a>
                                                <a href="#" class="dropdown-item">
                                                    <i class="fas fa-store"></i>
                                                    Register as Vendor
                                                </a>
                                            </div>
                                        </div>
                                    </li>
                                </ul>
                            </nav>
                        </div>

                        <!-- mobile menu trigger -->
                        <div class="mobile-menu-trigger">
                            <span></span>
                        </div>
                        <!--/.Mobile Menu Hamburger Ends-->
                    </nav>
                </div>
            </header>

            @section('content')
            @show
            @auth()
            @if(in_array(Route::current()->getName(), [ 'password.reset', 'update_reset_password', 'reset_password']))
            @elseif(Request()->route()->getPrefix() == '/maintenance')
            @else
            @include('layouts.partials._footer')
            @endif
            @endauth

            @if (Request()->route()->getPrefix() == '/maintenance')
            @include('layouts.partials._footer_maintanance')
            <div id="overlayer">
                <span class="loader-overlay">
                    <span class="atbd-spin-dots spin-lg">
                        <span class="spin-dot badge-dot dot-primary"></span>
                        <span class="spin-dot badge-dot dot-primary"></span>
                        <span class="spin-dot badge-dot dot-primary"></span>
                        <span class="spin-dot badge-dot dot-primary"></span>
                    </span>
                </span>
            </div>
            @endif
            @if(Request()->route()->getPrefix() != '/maintenance')
        </main>
        @endif
        @auth()
        <div id="overlayer">
            <span class="loader-overlay">
                <span class="atbd-spin-dots spin-lg">
                    <span class="spin-dot badge-dot dot-primary"></span>
                    <span class="spin-dot badge-dot dot-primary"></span>
                    <span class="spin-dot badge-dot dot-primary"></span>
                    <span class="spin-dot badge-dot dot-primary"></span>
                </span>
            </span>
        </div>
        @endauth
        <div class="overlay-dark-sidebar"></div>
        <div class="customizer-overlay"></div>
        {{-- Inject:js, Global Theme JS Bundle (used by all pages) --}}
        @yield('mapScript')
        @yield('modal')
        @include('layouts.partials._scripts')
        @yield('scripts')
        {{-- Endinject --}}
        <script type="text/javascript" src="{{asset('js/admin/supports/create.js')}}"></script>
        <script>
            window.onload = function () {
                $('.cc-select2').select2();
                $(".calendar").datepicker({
                    dateFormat: 'yy-mm-dd',
                });
            }

            setTimeout(function () {
                $('#Message').fadeOut('fast');

            }, 3000);

            $(document).ready(function () {
                $("#search").keypress(function (e) {
                    if (e.keyCode === 13) {
                        e.preventDefault();
                    }
                });
            });

            $('.akaunting-debug-exit').on('click', function () {
                $('.akaunting-debug-bar').fadeOut();
            });
            // $('img').on("error", function() {

            // $(this).attr('src', 'https://image.shutterstock.com/image-vector/vector-illustration-business-concept-error-600w-1912316272.jpg'+ `?v=${new Date().getTime()}`);
            // });

            // document.addEventListener("DOMContentLoaded", function(event) {
            //     document.querySelectorAll('img').forEach(function(img){

            //         fetch(img.src, { method: 'HEAD' })
            //         .then(res => {
            //             if (res.ok) {
            //             } else {

            //                 $(img).attr('src', 'https://image.shutterstock.com/image-vector/vector-illustration-business-concept-error-600w-1912316272.jpg' + `?v=${new Date().getTime()}`) ;
            //                 console.log(img.src)

            //             }
            //         }).catch(err => console.log('Error:', err));
            //     })
            // });
        </script>
        <style>
            .phpdebugbar {
                display: none;
            }
        </style>
    </body>

</html>

