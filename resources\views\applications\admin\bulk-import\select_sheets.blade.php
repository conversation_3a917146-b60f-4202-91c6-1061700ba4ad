@extends('layouts.app')
@section('styles')
    <link rel = "stylesheet" href = "{{ asset('new_theme/css/import.css') }}">
@endsection
@section('content')
    <div class = "contents">
        <div class = "container-fluid">
            <div class = "social-dash-wrap">
                <div class = "row">
                    <div class = "col-lg-12">
                        <div class = "breadcrumb-main">
                            <h4 class = "text-capitalize breadcrumb-title">
                                <a href = "{{ route('bulk-import.openUploadFile') }}" class = "text-dark">
                                    <i class = "las la-arrow-left"></i>
                                </a>
                                @lang('import.import_project_assets')
                            </h4>
                            <div class = "breadcrumb-action justify-content-center flex-wrap"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class = "container-fluid">
            <div class = "checkout wizard1 wizard7 global-shadow px-sm-50 px-20 py-sm-50 py-30 mb-30 bg-white radius-xl w-100">
                <div class = "row justify-content-center">
                    <div class = "col-xl-8">
                        <div class = "checkout-progress-indicator content-center col-md-10">
                            <div class = "checkout-progress">
                                <div class = "step current" id = "1">
                                    <span>1</span>
                                    <span>@lang('import.upload')</span>
                                </div>
                                 <div class = "current">
                                    <img src = "{{ asset('img/svg/checkout.svg') }}" class = "svg">
                                </div>
                                <div class = "step" id = "2">
                                    <span>2</span>
                                    <span>@lang('import.map')</span>
                                </div>
                                <div class = "current">
                                    <img src = "{{ asset('img/svg/checkout.svg') }}" class = "svg">
                                </div>
                                <div class = "step" id = "3">
                                    <span>3</span>
                                    <span>@lang('import.validate')</span>
                                </div>
                                <div class = "current">
                                    <img src = "{{ asset('img/svg/checkout.svg') }}" class = "svg">
                                </div>
                                <div class = "step" id = "4">
                                    <span>4</span>
                                    <span>@lang('import.done')</span>
                                </div>
                            </div>
                        </div>
                        <div class = "row justify-content-center">
                            <div class = "col-xl-10 col-lg-8 col-sm-10">
                                <div class = "card checkout-shipping-form pt-2 pb-30 border-0">
                                    @if(session()->has('error'))
                                        <div class = "alert alert-danger alert-dismissible fade show py-3" role = "alert">
                                            <ul>
                                                <li>
                                                    <p>{{ session()->get('error') }}</p>
                                                </li>
                                            </ul>
                                            <button type = "button" class = "close" data-dismiss = "alert" aria-label = "Close" style = "margin-top: -10px">
                                                <span aria-hidden = "true" style = "font-size: 28px">&times;</span>
                                            </button>
                                        </div>
                                    @endif
                                    @php session()->forget('error'); @endphp
                                    <form id = "select-sheets-form" name = "select-sheets-form" method = "post" action = "{{ route('bulk-import.updateSelectSheetBulkImport', ['token' => $token]) }}">
                                        @csrf
                                        <div class = "row">
                                            <div class = "col-6">
                                                <div class = "form-group">
                                                    <label for = "name1">
                                                        @lang("data_properties.property_forms.label.project") 
                                                        <span class = "required">*</span>
                                                    </label>
                                                    <input type = "text" class = "form-control" name = "projectName" id = "projectName" value = "{{ $projectName }}" disabled>
                                                </div>
                                            </div>
                                            <div class = "col-6">
                                                <div class = "form-group">
                                                    <label for = "name1">
                                                        @lang("import.connected_user") 
                                                        <span class = "required">*</span>
                                                    </label>
                                                    <input type = "text" class = "form-control" name = "user" id = "user" value = "{{ $username }}" disabled>
                                                </div>
                                            </div>
                                            <div class = "col-6">
                                                <div class = "form-group">
                                                    <label for = "name1">
                                                        @lang("import.filename_title") 
                                                        <span class = "required">*</span>
                                                    </label>
                                                    <input type = "text" class = "form-control" name = "filename" id = "filename" value = "{{ isset($bulkImportDetails) ? $bulkImportDetails->file_name : '-' }}" disabled>
                                                </div>
                                            </div>
                                            <div class = "col-6">
                                                <div class = "form-group">
                                                    <label for = "name1">
                                                        @lang("import.filesize_title") 
                                                        <span class = "required">*</span>
                                                    </label>
                                                    <input type = "text" class = "form-control" name = "filesize" id = "filesize" value = "{{ isset($bulkImportDetails) ? $bulkImportDetails->file_size : '-' }}" disabled>
                                                </div>
                                            </div>
                                        </div>
                                        <div class = "alert alert-warning alert-dismissible fade show mt-30" role = "alert">
                                            <ul>
                                                <li>
                                                    <p>@lang('import.double_check')</p>
                                                </li>
                                                <li>
                                                    <p>@lang('import.notification_company_id')</p>
                                                </li>
                                            </ul>
                                            <button type = "button" class = "close" data-dismiss = "alert" aria-label = "Close">
                                                <span aria-hidden = "true" style = "font-size: 28px">&times;</span>
                                            </button>
                                        </div>
                                        <div class = "table-responsive mt-4">
                                            <table class = "table table-bordered">
                                                <thead class = "userDatatable-header">
                                                    <tr class = "thead-default">
                                                        <th class = "text-dark text-capitalize">@lang('import.sheet')</th>
                                                        <th class = "text-dark text-capitalize">@lang('import.count')</th>
                                                        <th class = "text-dark text-capitalize">@lang('import.action')</th>
                                                        <th class = "text-dark text-capitalize w-50">@lang('import.description')</th>
                                                    </tr>
                                                </thead>
                                                <tbody> 
                                                    <tr class = "{{ $countUsers == 0 ? 'table-light' : '' }}">
                                                        <td>
                                                            <div class = "form-inline">
                                                                <input type = "checkbox" value = "1" name = "checked_users" id = "checked_users" {{ $countUsers == 0 ? 'disabled' : '' }}>
                                                                <label class = "mx-2 text-capitalize fs-12">@lang('import.users_label')</label>
                                                            </div>
                                                        </td>
                                                        <td class = "text-center fs-12">{{ $countUsers ?? 0 }}</td>
                                                        <td>
                                                            <button type = "button" class = "btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2 btn-sm mx-auto" id = "users_affectation_btn" {{ $countUsers == 0 ? 'disabled' : '' }} data-toggle = "modal" data-target = "#affect_users_modal">
                                                                @lang('import.affectation')
                                                            </button>
                                                        </td>
                                                        <td>
                                                            <p class = "fs-12">@lang('import.select_users')</p>
                                                        </td>
                                                    </tr>
                                                    <tr class = "{{ $countPriorities == 0 ? 'table-light' : '' }}">
                                                        <td>
                                                            <div class = "form-inline">
                                                                <input type = "checkbox" value = "1" name = "checked_priorities" id = "checked_priorities" {{ $countPriorities == 0 ? 'disabled' : '' }}>
                                                                <label class = "mx-2 text-capitalize fs-12">@lang('import.priorities_label')</label>
                                                            </div>
                                                        </td>
                                                        <td class = "text-center fs-12">{{ $countPriorities ?? 0 }}</td>
                                                        <td class = "text-center fs-12">-</td>
                                                        <td>
                                                            <p class = "fs-12">@lang('import.select_priorities_level')</p>
                                                        </td>
                                                    </tr>
                                                    <tr class = "{{ $countServices == 0 ? 'table-light' : '' }}">
                                                        <td>
                                                            <div class = "form-inline">
                                                                <input type = "checkbox" value = "1" name = "checked_services" id = "checked_services" {{ $countServices == 0 ? 'disabled' : '' }}>
                                                                <label class = "mx-2 text-capitalize fs-12">@lang('import.services_label')</label>
                                                            </div>
                                                        </td>
                                                        <td class = "text-center fs-12">{{ $countServices ?? 0 }}</td>
                                                        <td class = "text-center fs-12">-</td>
                                                        <td>
                                                            <p class = "fs-12">@lang('import.select_services')</p>
                                                        </td>
                                                    </tr>
                                                    <tr class = "{{ $countProperties == 0 ? 'table-light' : '' }}">
                                                        <td>
                                                            <div class = "form-inline">
                                                                <input type = "checkbox" value = "1" name = "checked_properties" id = "checked_properties" {{ $countProperties == 0 ? 'disabled' : '' }}>
                                                                <label class = "mx-2 text-capitalize fs-12">@lang('import.properties_label')</label>
                                                            </div>
                                                        </td>
                                                        <td class = "text-center fs-12">{{ $countProperties ?? 0 }}</td>
                                                        <td class = "text-center fs-12">-</td>
                                                        <td>
                                                            <p class = "fs-12">@lang('import.select_properties')</p>
                                                        </td>
                                                    </tr>
                                                    <tr class = "{{ $countPropertyBuildings == 0 ? 'table-light' : '' }}">
                                                        <td>
                                                            <div class = "form-inline">
                                                                <input type = "checkbox" value = "1" name = "checked_buildings" id = "checked_buildings" {{ $countPropertyBuildings == 0 ? 'disabled' : '' }}>
                                                                <label class = "mx-2 text-capitalize fs-12">@lang('import.property_buildings_label')</label>
                                                            </div>
                                                        </td>
                                                        <td class = "text-center fs-12">{{ $countPropertyBuildings ?? 0 }}</td>
                                                        <td class = "text-center fs-12">-</td>
                                                        <td>
                                                            <p class = "fs-12">@lang('import.select_properties_building')</p>
                                                        </td>
                                                    </tr>
                                                    <tr class = "{{ $countAssets == 0 ? 'table-light' : '' }}">
                                                        <td>
                                                            <div class = "form-inline">
                                                                <input type = "checkbox" value = "1" name = "checked_assets" id = "checked_assets" {{ $countAssets == 0 ? 'disabled' : '' }}>
                                                                <label class = "mx-2 text-capitalize fs-12">@lang('import.assets_label')</label>
                                                            </div>
                                                        </td>
                                                        <td class = "text-center fs-12">{{ $countAssets ?? 0 }}</td>
                                                        <td class = "text-center fs-12">-</td>
                                                        <td>
                                                            <p class = "fs-12">@lang('import.select_assets')</p>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                        <div class = "button-group d-flex pt-10 justify-content-end mb-40">
                                            <a href = "{{ route('bulk-import.openUploadFile') }}" class = "btn btn-light btn-default btn-squared text-capitalize radius-md shadow2 btn-sm">
                                                @lang('import.previous')
                                            </a>
                                            <button type = "submit" class = "btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2 btn-sm" id = "btn-next">
                                                @lang('import.next')
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
       <div>
           @livewire('bulk-import.new.affect-users', ['usersList' => $usersList, 'projectId' => $projectId])
        </div>
    </div>
@endsection
@section('scripts')
    <script src = "{{ asset('new_theme/js/functions.js') }}"> </script>
    <script>
        $("#select-sheets-form").on("submit", function(event) {
           return showSelectSheetsQuestion(event);
        });

        $("#checked_users").on("change", function() {
            manageAffectationButtonState();
        });

        document.addEventListener('closeAffectModal', () => {
            $('#affect_users_modal').modal('hide');
            resetSelect2();
        });

        document.addEventListener('spa_list', event => {
            updadeSPASelect2(JSON.parse(event.detail));
        });

        document.addEventListener('supervisor_list', event => {
            updadeSPSSelect2(JSON.parse(event.detail));
        });

        $(document).ready(function() {
            manageAffectationButtonState();
            $('.select2-select').select2(); 
        });
    </script>
@endsection