<div>
    @if ($totalPages)
        <div
            class="d-flex justify-content-md-between flex-column flex-md-row justify-content-center align-items-center mt-4 gap-10">
            <div>
                <ul class="atbd-pagination d-none justify-content-between">
                {{-- <ul class="atbd-pagination d-flex justify-content-between"> --}}
                    <li>
                        <div class="paging-option">
                            <div class="dataTables_length d-flex">
                                <label class="d-flex align-items-center mb-0">
                                    @if (@$showPerPage)
                                        <select aria-controls="workorder_table" wire:model.live.debounce.250ms="perPage"
                                            class="custom-select custom-select-sm form-control form-control-sm mx-2"
                                            style="min-height: 35px;">
                                            @foreach ($perPageValues as $item)
                                                <option value="{{ $item }}"
                                                    @if ($perPage == $item) selected @endif>
                                                    {{ $item }}
                                                </option>
                                            @endforeach
                                        </select>
                                        <span class="no-wrap"> @lang('pagination.entries_per_page') </span>
                                    @endif
                                </label>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="">
                <div class="user-pagination">
                    <div class="user-pagination new-pagination">
                        @php
                            $start = max(1, $currentPage - 2);
                            $end = min($totalPages, $currentPage + 2);
                        @endphp

                        <div class="d-flex justify-content-sm-end justify-content-end">
                            <nav>
                                @if ($totalPages > 1)
                                    <span
                                        class="relative z-0 inline-flex rounded-md d-flex new-pagination-section flex-wrap">

                                        {{-- Previous Button --}}
                                        <button class="border-0 {{ $currentPage == 1 ? 'disabled' : '' }}"
                                            wire:click="goToPage({{ $currentPage - 1 }})"
                                            {{ $currentPage == 1 ? 'disabled' : '' }}>
                                            <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block"
                                                icon-name="chevron-left"></i>
                                        </button>

                                        {{-- First Page --}}
                                        @if ($start > 1)
                                            <button class="border-0" wire:click="goToPage(1)">1</button>
                                            @if ($start > 2)
                                                <span class="px-2">...</span>
                                            @endif
                                        @endif

                                        {{-- Page Range --}}
                                        @for ($page = $start; $page <= $end; $page++)
                                            <button class="border-0 {{ $page == $currentPage ? 'current-page' : '' }}"
                                                wire:click="goToPage({{ $page }})"
                                                {{ $page == $currentPage ? 'disabled' : '' }}>
                                                {{ $page }}
                                            </button>
                                        @endfor

                                        {{-- Last Page --}}
                                        @if ($end < $totalPages)
                                            @if ($end < $totalPages - 1)
                                                <span class="px-2">...</span>
                                            @endif
                                            <button class="border-0"
                                                wire:click="goToPage({{ $totalPages }})">{{ $totalPages }}</button>
                                        @endif

                                        {{-- Next Button --}}
                                        <button class="border-0 {{ $currentPage == $totalPages ? 'disabled' : '' }}"
                                            wire:click="goToPage({{ $currentPage + 1 }})"
                                            {{ $currentPage == $totalPages ? 'disabled' : '' }}>
                                            <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block"
                                                icon-name="chevron-right"></i>
                                        </button>
                                    </span>
                                @endif
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
            <div class="pr-20">
                @if (@$totalRecords)
                    <p class="text-sm text-gray-700 leading-5 mb-0">
                        <span>@lang('pagination.showing')</span>
                        <span class="font-medium">{{ $startIndex }}</span>
                        <span>@lang('pagination.to')</span>
                        <span class="font-medium">{{ $endIndex }}</span>
                        <span>@lang('pagination.of')</span>
                        <span class="font-medium">{{ $totalRecords }}</span>
                        <span>@lang('pagination.results')</span>
                    </p>
                @endif
            </div>
        </div>
    @endif
</div>