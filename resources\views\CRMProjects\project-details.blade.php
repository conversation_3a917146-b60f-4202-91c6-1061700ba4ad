@extends('layouts.app')
@section('styles')
@endsection
@section('content')

<livewire:c-r-m-projects.project-details :projectID="$projectID" />


@endsection


@section('scripts')
{{-- <script src="{{ asset('vendor_assets/js/Chart.min.js') }}"></script>
<script src="{{ asset('js/charts_dashboard.js') }}"></script> --}}
<script type="text/javascript">
function assignAvatarColors() {
    let colors = [
        "#FF5733", "#3498DB", "#2ECC71", "#F39C12", "#9B59B6", "#1ABC9C",
        "#E74C3C", "#8E44AD", "#34495E", "#2C3E50", "#D35400", "#27AE60"
    ];

    let spans = document.querySelectorAll(".FLName_avatar");

    spans.forEach(span => {
        let letter = span.innerText.trim().charAt(0);
        let charCode = letter.charCodeAt(0);
        let colorIndex = charCode % colors.length;
        span.style.backgroundColor = colors[colorIndex];
    });
}

function assignAvatarColors() {
    let colors = [
        "#FF5733", "#3498DB", "#2ECC71", "#F39C12", "#9B59B6", "#1ABC9C",
        "#E74C3C", "#8E44AD", "#34495E", "#2C3E50", "#D35400", "#27AE60"
    ];

    // Select all spans with class 'FLName_avatar'
    let spans = document.querySelectorAll(".FLName_avatar");

    spans.forEach(span => {
        let letter = span.innerText.trim().charAt(0);
        let charCode = letter.charCodeAt(0);
        let colorIndex = charCode % colors.length;
        span.style.backgroundColor = colors[colorIndex];
    });
}


document.addEventListener("DOMContentLoaded", assignAvatarColors);
    $(".datepicker").datepicker();
    $("#usersList, #clientsList, #mile-status").select2();
    $(".select2-select").select2();


   /*  var ctx = document.getElementById("tasksOverviewChart").getContext("2d");
    var myChart = new Chart(ctx, {
      type: 'line',
      data: {
          labels: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat"],
          datasets: [
              {
                  label: "Dataset 1",
                  borderColor: "blue",
                  backgroundColor: "transparent",
                  data: [2, 5, 3, 7, 4, 8],
                  lineTension: 0.3
              },
              {
                  label: "Dataset 2",
                  borderColor: "green",
                  backgroundColor: "transparent",
                  data: [1, 4, 6, 5, 3, 4],
                  lineTension: 0.3
              },
              {
                  label: "Dataset 3",
                  borderColor: "yellow",
                  backgroundColor: "transparent",
                  data: [3, 6, 8, 4, 2, 5],
                  lineTension: 0.3
              },
              {
                  label: "Dataset 4",
                  borderColor: "pink",
                  backgroundColor: "transparent",
                  data: [1, 2, 2.5, 3, 3.5, 4],
                  lineTension: 0.3
              }
          ]
      },
      options: {
          responsive: true,
          maintainAspectRatio: false,
          legend: { display: false },
          scales: {
              xAxes: [{ display: true }],
              yAxes: [{ display: true }]
          }
      }
  }); */
</script>

@endsection