<style>

.select2-search__field{
  display: none;
}
</style>
<?php $maintenencePortalOptionalFields = $data['maintenencePortalOptionalFields'];?>
@if(App::getLocale() =='en')
<style>
.form-element .custom-file-label::after {
  height: 48px;
  padding: 0 23px;
  line-height: 1.5;
  color: #9299B8;
  content: "Upload Photo*" !important;   
  background-color: transparent;
  display: flex;
  align-items: center;
}
.form-element .custom-file-label.changed-optional::after {
  height: 48px;
  padding: 0 23px;f
  line-height: 1.5;
  color: #9299B8;
  content: "Upload Photo (Optional)" !important;   
  background-color: transparent;
  display: flex;
  align-items: center;
}
</style>

@else
<style>
.form-element .custom-file-label::after {
  height: 48px;
  padding: 0 23px;
  line-height: 1.5;
  color: #9299B8;
  content: "*أرفق صورة";
  background-color: transparent;
  display: flex;
  align-items: center;
  left: auto;
  right: 0;
  border-left: inherit;
  border-right: none;
}
.form-element .custom-file-label.changed-optional::after {
  height: 48px;
  padding: 0 23px;
  line-height: 1.5;
  color: #9299B8;
  content: "(إختياري) أرفق صورة";
  background-color: transparent;
  display: flex;
  align-items: center;
  left: auto;
  right: 0;
  border-left: inherit;
  border-right: none;
}
</style>


@endif


</style>

@if(isset( $data['floor_details'] ))

  @php

    $roomarr=array_unique($data['floor_details']['rooms']);

  @endphp

@endif

@extends('layouts.app')
@section('styles')

@endsection
@section('content')
<!-- Main Content Starts Here -->
<main class="main-content">
<div class="breadcrumb-main text-center">

<div class="d-sm-flex flex-row-reverse justify-content-between flex-fill px-4 align-items-center border-bottom pb-3">
    <div class="logo-div text-xs-center text-center">
                        @if($data['projectImage'])                        
                        <a class="" href="{{  url('') }}"><img class="svg dark" src="{{ImagesUploadHelper::displayImage($data['projectImage'], 'uploads/project_images')}}" alt="svg" width="150"></a>
                        @else
                        <a class="" href="{{  url('') }}"><img class="svg dark" src="{{ asset('img/OSOOL_logo_svg.svg') }}" alt="svg" width="150"></a>
                        @endif
                        {{--<a class="" href="{{  url('') }}"><img class="svg dark" src="{{ asset('img/OSOOL_logo.png') }}" alt="svg" width="150"></a>--}}
                        </div>
    <div>
      @if(App::getLocale()=='en')
      <h4 class="text-capitalize breadcrumb-title text-left mwp-title fs-25 w-100 text-sm-left text-center mt-sm-0 mt-4">Need Help?</h4>
      @else (App::getLocale()=='ar')
      <h4 class="text-capitalize breadcrumb-title text-left mwp-title fs-25 w-100 text-sm-left text-center mt-sm-0 mt-4"> تحتاج مساعدة؟ </h4>
      @endif
        <div class="clearfix w-100"></div>
        <p class="w-100  m-auto text-left grey-7 d-inline d-rtl">
        @if(App::getLocale()=='en')
          @if(isset($data['unit_qr_settings']->message_en))
            {{$data['unit_qr_settings']->message_en}}
          @else
            {{ __('data_maintanance_request.common.we_are_here_for_you_facing_issue_in_this_unit_space') }}.
          @endif
        @else (App::getLocale()=='ar')
          @if(isset($data['unit_qr_settings']->message_ar))
            {{$data['unit_qr_settings']->message_ar}}
          @else
            {{ __('data_maintanance_request.common.we_are_here_for_you_facing_issue_in_this_unit_space') }}.
          @endif
        @endif
        </p>
    </div>
</div>
<!--
        <h4 class="text-capitalize breadcrumb-title text-center mwp-title fs-25 w-100 mb-10">{{ trans('data_maintanance_request.common.need_help') }}

        </h4> -->

</div>
        <div class="form-element">
            <div class="row">
                <div class="col-lg-12">
                    <div class="">
                        <div class="card-header no-bg border-none">
                            <p class="fs-18 text-dark d-flex align-items-center mb-0 icon-heading"><span class="icon mr-2 border rounded"><span data-feather="map-pin"></span></span> {{ __('data_maintanance_request.common.you_are_at') }}</p>
                        </div>
                        <div class="card-body pb-md-20 pt-0 border-bottom">
                            <form>
                                <div class="form-row mx-n15">
                                    <div class="col-md-4 col-12 mb-10 px-15">
                                        <div class="bg-grey px-3 py-4 radius-md">
                                        <label for="validationDefault01" class="il-gray fs-15 align-center">{{$trans_the_building}}</label>
                                        <span class="text-dark1 align-center">{{ $data['building_tag'] }}</span>
                                    </div>
                                    </div>
                                    <div class="col-md-4 col-12 mb-10  px-15">
                                        <div class="bg-grey px-3 py-4 radius-md">
                                        <label for="validationDefault02" class="il-gray fs-15 align-center">{{ __('data_maintanance_request.common.floor') }}</label>
                                        <span class="text-dark1 align-center floor_id">{{ $data['details']->floor }}</span>
                                    </div>
                                    </div>
                                    <div class="col-md-4 col-12 mb-10  px-15">
                                        <div class="bg-grey px-3 py-4 radius-md">
                                        <label for="validationDefault012" class="il-gray fs-15 align-center">{{ __('data_maintanance_request.common.space_unit') }}</label>
                                        <span class="text-dark1 align-center room_id">{{ $data['details']->room }}</span>
                                    </div>
                                </div>
                                </div>

                            </form>
                            {{ __('data_maintanance_request.common.are you at different place?') }} <a href="javascript:void(0);" data-toggle="modal" data-target="#exampleModal">{{ __('data_maintanance_request.common.click here') }}</a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-12">
                    <div class="">
                        <div class="card-header border-none">
                            <p class="fs-18 text-dark d-flex align-items-center mb-0 icon-heading"><span class="icon mr-2 border rounded"><span data-feather="info"></span></span>{{ __('data_maintanance_request.common.Issue Details') }}</p>
                        </div>
                            <form id="maintanance-create-form" action="{{ Route('maintenance.create.task') }}" method="post" enctype="multipart/form-data">
                        <div class="card-body border-bottom">
                                @csrf()
                                <input type="hidden" name="barcode_id" value="{{ $data['barcode_id'] }}" >
                                <input type="hidden" id="floor_id" name="floor" value="{{ $data['details']->floor }}" >
                                <input type="hidden" id="room_id" name="room" value="{{ $data['details']->room }}" >
                                <input type="hidden" id="files_selected0" name="files_selected0" value="" >
                                <input type="hidden" id="files_selected1" name="files_selected1" value="" >
                                <input type="hidden" id="files_selected2" name="files_selected2" value="" >
                                @if(isset($maintenencePortalOptionalFields) && !in_array('services_assets_enabled', $maintenencePortalOptionalFields))
                               <div class="row">
                                <div class="col-md-6 mb-25">
                                  <label>
                                      {{ __('data_maintanance_request.common.services') }}
                                      @if(isset($maintenencePortalOptionalFields) && in_array('services', $maintenencePortalOptionalFields))
                                          <!-- Optional Text -->
                                          ({{ __('work_order.forms.label.optional') }})
                                      @else
                                          <!-- Red Asterisk for Required -->
                                          <span class="required">*</span>
                                      @endif
                                  </label>
                                  <div>
                                      <!-- Service Dropdown -->
                                      <select name="services" class="form-control ih-medium ip-lightradius-xs b-light" id="inputNameIcon">
                                          <option value="">{{ __('data_maintanance_request.common.select_service') }}</option>
                                          <!-- Example Predefined Services -->
                                          @foreach($data['services'] as $service)
                                   
                                              <option value="{{ $service['id'] }}">{{ $service['asset_category'] }}</option>
                               
                                      @endforeach
                                          <!-- Add more services as needed -->
                                      </select>
                                  </div>
                              </div>
                              <div class="col-md-6 mb-25">
                                <label>
                                    {{ __('data_maintanance_request.common.assets') }}
                                    @if(isset($maintenencePortalOptionalFields) && in_array('assets', $maintenencePortalOptionalFields))
                                        <!-- Optional Text -->
                                        ({{ __('work_order.forms.label.optional') }})
                                    @else
                                        <!-- Red Asterisk for Required -->
                                        <span class="required">*</span>
                                    @endif
                                </label>
                                <div>
                                    <!-- Asset Dropdown -->
                                    <select name="assets" class="form-control ih-medium ip-lightradius-xs b-light" id="inputAssetIcon">
                                        <option value="">{{ __('data_maintanance_request.common.select_asset') }}</option>
                                        <!-- Example Predefined Assets for the selected Unit and Zone -->
                                        @foreach($data['assets']  as $asset)
                                        
                                        @php
                                        $asset_name = \App\Models\AssetName::where('id', $asset->asset_name_id)->value('asset_name');
                                    @endphp
                                    <option value="{{ $asset->id }}">{{ $asset_name }}</option>
                                    
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                          </div>
                            @endif
                                <div class="form-row mx-n15">
                                    <div class="col-md-12 px-15">
                                        <div class="form-group form-element-textarea label-on">
                                            <label for="exampleFormControlTextarea1" class="il-gray fs-14 align-center">{{ __('data_maintanance_request.common.What do you need help with') }}</label>
                                            <textarea name="description" class="form-control" id="exampleFormControlTextarea1" rows="3"></textarea>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                <div class="col-md-12">
                                <p>{{__('work_order.forms.label.max3Files')}}</p>
                                    <div class="custom-file fileinput-button">
                                        <input type="file" class="custom-file-input" accept="image/png, image/jpeg,image/jpg" name="files[]" id="files" multiple >
                                        <label class="custom-file-label pl-15" for="customFile"></label>
                                    </div>
                                    <output id="Filelist" class="image-list mt-10"></output>
                                </div>
                                <div class="max-file-choseen-error col-lg-12 px-15" style="display:none;">
                                    <label for="validationDefault02" class="il-gray fs-14 fw-500 align-center">{{ __('data_maintanance_request.common.You can only attach 3 files') }}  <i class="fas fa-exclamation mr-1"></i>  </label>
                                </div>
                                <span class="file-error"></span>
                            </div>
                        </div>
                            <div class="card-body">
                                <div class="form-row mx-n15 mb-20">
                                    <div class="col-md-12 px-15">
                                        <p class="fs-18 text-dark d-flex align-items-center icon-heading"><span class="icon mr-2 border rounded"><span data-feather="phone"></span></span> {{ __('data_maintanance_request.common.contact_details') }}</p>
                                    </div>
                                    <div class="col-md-12 px-15 mt-3">
                                        <div class="form-group row mb-n25 label-on">
                                            <div class="col-md-4 mb-25">
                                                <label>{{ __('data_maintanance_request.common.name') }}@if(isset($maintenencePortalOptionalFields) && in_array('name',$maintenencePortalOptionalFields)) ({{ __('work_order.forms.label.optional') }}) @else <span class="required">*</span> @endif </label>
                                                <div class="">
                                                    <input type="text" class="form-control  ih-medium ip-lightradius-xs b-light" id="inputNameIcon" name="name" placeholder="{{ __('data_maintanance_request.common.ph_name') }}">
                                                </div>
                                            </div>

                                            <div class="col-md-4 mb-25">
                                                <label>{{ __('data_maintanance_request.common.phone') }}@if(isset($maintenencePortalOptionalFields) && in_array('phone',$maintenencePortalOptionalFields)) ({{ __('work_order.forms.label.optional') }}) @else <span class="required">*</span> @endif</label>
                                                <div class="">
                                                    <input type="tel" class="form-control  ih-medium ip-lightradius-xs b-light" id="phone" name="phone" placeholder="{{ __('data_maintanance_request.common.ph_phone') }}">
                                                </div>
                                            </div>

                                            <div class="col-md-4 mb-25">
                                                <label>{{ __('data_maintanance_request.common.email') }}@if(isset($maintenencePortalOptionalFields) && in_array('email',$maintenencePortalOptionalFields)) ({{ __('work_order.forms.label.optional') }}) @else <span class="required">*</span> @endif</label>
                                                <div class="">
                                                    <input type="text" class="form-control  ih-medium ip-lightradius-xs b-light" id="email" name="email" placeholder="{{ __('data_maintanance_request.common.ph_email') }}">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4 mt-20">
                                        <button class="btn btn-primary px-30 w-100" type="submit"><b>{{ __('data_maintanance_request.buttons.send') }}</b> </button>
                                    </div>
                                </div>
                            </div>

                            </form>
                        </div>
                    </div>
                    <!-- ends: .card -->
                </div>
            </div>
        </div>
    </div>

</div>
</div>
<!-- Modal -->
<div class="modal fade no-select2-search" id="exampleModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
<form id="another_place" method="post">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">{{ __('data_maintanance_request.common.Select a Different Place') }}</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">

           <div class="form-group">
            <label>{{ __('data_maintanance_request.common.Choose Floor') }}</label>
            <select name="floor" id="floor" class="form-control select2" data-pro="{{$data['details']->building_id}}" data-url="{{route('maintenance.ajax-get-room-data')}}">
                <option value="" selected disabled>{{__('work_order.forms.place_holder.Choose_Floor')}}</option>
                @if(isset($roomarr))
                    @foreach($roomarr as $fl)
                    <option value="{{ $fl}}">{{ $fl }}
                    </option>
                    @endforeach
                @endif
            </select>
           </div>
           <div class="form-group">
            <label>{{ __('data_maintanance_request.common.Choose Space Unit') }}</label>
            <select name="room" id="room" class="form-control select2">

                <option value="" selected disabled>{{__('work_order.forms.place_holder.Choose_Room')}}</option>

            </select>
           </div>

      </div>
      <div class="modal-footer">

        <button type="submit" class="btn btn-primary w-100">{{ __('data_maintanance_request.common.Confirm') }}</button>
      </div>
    </div>
  </div>
</form>
</div>

@endsection
@section('scripts')

<script>
document.nameRequired = true;
document.emailRequired = true;
document.imagesRequired = true;
document.phoneRequired = true;
document.servicesRequired = true;
document.assetsRequired = true;
</script>
@if(isset($maintenencePortalOptionalFields) && !empty($maintenencePortalOptionalFields))
 @if(in_array('name',$maintenencePortalOptionalFields)) <script>  document.nameRequired = false; </script> @endif
 @if(in_array('email',$maintenencePortalOptionalFields)) <script>  document.emailRequired = false; </script> @endif
 @if(in_array('phone',$maintenencePortalOptionalFields)) <script>  document.phoneRequired = false; </script> @endif
 @if(in_array('services',$maintenencePortalOptionalFields)) <script>  document.servicesRequired = false; </script> @endif
 @if(in_array('assets',$maintenencePortalOptionalFields)) <script>  document.assetsRequired = false; </script> @endif
  @if(in_array('images',$maintenencePortalOptionalFields))
  <script>
     document.imagesRequired = false;
  $('.form-element .custom-file-label').toggleClass('changed-optional'); 
  </script>
  @endif
@endif
<script>

 $('#files').click(function() {
  console.log('remove-old files')
   //$(".thumb-Images .close").trigger("click");
 })

document.addEventListener("DOMContentLoaded", init, false);
var AttachmentArray = [];
var arrCounter = 0;
var filesCounterAlertStatus = false;

//un ordered list to keep attachments thumbnails
var ul = document.createElement("ul");
ul.className = "thumb-Images";
ul.id = "imgList";



function init() {

  //add javascript handlers for the file upload event
  document
    .querySelector("#files")
    .addEventListener("change", handleFileSelect, false);
}

//the handler for file upload event
function handleFileSelect(e) {
  UploadHandeler()
  //to remove li tag

  //to make sure the user select file/files
  if (!e.target.files) return;

  //To obtaine a File reference
  var files = e.target.files;

  // Loop through the FileList and then to render image files as thumbnails.
  for (var i = 0, f; (f = files[i]); i++) {
    //instantiate a FileReader object to read its contents into memory
    var fileReader = new FileReader();

    // Closure to capture the file information and apply validation.
    fileReader.onload = (function(readerEvt) {
      return function(e) {
        //console.log(readerEvt.type);

        //Apply the validation rules for attachments upload
        ApplyFileValidationRules(readerEvt);

        CheckFileType(readerEvt.type);

        //Render attachments thumbnails.
        RenderThumbnail(e, readerEvt);

        //Check the file count
        if($('#imgList li').length >= 3) {
          $('.custom-file').hide();

          $('.max-file-choseen-error').show();
        } else {
          $('.max-file-choseen-error').hide();
          $('.custom-file').show();

        }

        //Fill the array of attachment
        FillAttachmentArray(e, readerEvt);
      };
    })(f);
    fileReader.readAsDataURL(f);
  }
  document
    .getElementById("files")
    .addEventListener("change", handleFileSelect, false);
}

//To remove attachment once user click on x button
jQuery(function($) {
  $("div").on("click", ".img-wrap .close", function() {
    $('.custom-file').show();
    var id = $(this)
      .closest(".img-wrap")
      .find("img")
      .data("id");

    //to remove the deleted item from array
    var elementPos = AttachmentArray.map(function(x) {
      return x.FileName;
    }).indexOf(id);
    if (elementPos !== -1) {
      AttachmentArray.splice(elementPos, 1);
    }

    //to remove image tag
    $(this)
      .parent()
      .find("img")
      .not()
      .remove();

    //to remove div tag that contain the image
    $(this)
      .parent()
      .find("div")
      .not()
      .remove();

    //to remove div tag that contain caption name
    $(this)
      .parent()
      .parent()
      .find("div")
      .not()
      .remove();

    //to remove li tag
    var lis = document.querySelectorAll("#imgList li");

    for (var i = 0; (li = lis[i]); i++) {
      if (li.innerHTML == "") {
        li.parentNode.removeChild(li);
      }
    }
  });
});

//Apply the validation rules for attachments upload
function ApplyFileValidationRules(readerEvt) {

  //To check files count according to upload conditions
  if (CheckFilesCount(AttachmentArray) == false) {
    if (!filesCounterAlertStatus) {
      filesCounterAlertStatus = true;
      //swal(translations.general_sentence.validation.max_3_pictures);
      toastr.error(
        translations.general_sentence.validation.max_3_pictures,
          { timeOut: 5000 }
        );
    }
    e.preventDefault();
    return;
  }
}

//To check file type according to upload conditions
function CheckFileType(fileType) {
  if (fileType == "image/jpeg") {
    return true;
  } else if (fileType == "image/png") {
    return true;
  } else if (fileType == "image/gif") {
    return true;
  } else {
    toastr.error(
      translations.general_sentence.validation.Please_upload_only_jpg_jpeg_png_image,
      { timeOut: 5000 }
    );
    e.preventDefault();
    return;
  }
  return true;
}

//To check file Size according to upload conditions
function CheckFileSize(fileSize) {
  if (fileSize < 300000) {
    return true;
  } else {
    return false;
  }
  return true;
}

//To check files count according to upload conditions
function CheckFilesCount(AttachmentArray) {
  //Since AttachmentArray.length return the next available index in the array,
  //I have used the loop to get the real length
  var len = 0;
  for (var i = 0; i < AttachmentArray.length; i++) {
    if (AttachmentArray[i] !== undefined) {
      len++;
    }
  }

  //To check the length does not exceed 10 files maximum

  if (len > 2) {
    $('.custom-file').hide();
    return false;
  } else {
    return true;
  }
}

//Render attachments thumbnails.
function RenderThumbnail(e, readerEvt) {
  var li = document.createElement("li");

  ul.appendChild(li);
  li.innerHTML = [
    '<div class="img-wrap"> <span class="close">&times;</span>' +
      '<img class="thumb" src="',
    e.target.result,
    '" title="',
    escape(readerEvt.name),
    '" data-id="',
    readerEvt.name,
    '"/>' + "</div>"
  ].join("");

  var div = document.createElement("div");
  div.className = "FileNameCaptionStyle";
  li.appendChild(div);
  div.innerHTML = [readerEvt.name].join("");
  document.getElementById("Filelist").insertBefore(ul, null);
}

//Fill the array of attachment
function FillAttachmentArray(e, readerEvt) {
  AttachmentArray[arrCounter] = {
    AttachmentType: 1,
    ObjectType: 1,
    FileName: readerEvt.name,
    FileDescription: "Attachment",
    NoteText: "",
    MimeType: readerEvt.type,
    Content: e.target.result.split("base64,")[1],
    FileSizeInBytes: readerEvt.size
  };
  arrCounter = arrCounter + 1;
}


</script>


<script>

$(document).ready(function() {
    //$("#overlayer").css("display", "block");
    //$(".loader-overlay").css("display", "block");
    //$('.previewImg').hide();

});

$.validator.addMethod('max_files', function (value, element, arg) {
        if(element.files[0].length<=arg){
            return true;
        }else{
            return false;
        }
});

$("#maintanance-create-form_").submit(function(e){
        $(".thumb").each(function() {
          imgsrc = this.src;
          console.log(imgsrc);
        });
        e.preventDefault();

});


jQuery.validator.addMethod(
  "regex",
   function(value, element, regexp) {
       if (regexp.constructor != RegExp)
          regexp = new RegExp(regexp);
       else if (regexp.global)
          regexp.lastIndex = 0;
          return this.optional(element) || regexp.test(value);
   }
);
$("#maintanance-create-form").validate({
    rules: {
        description: {
            required: true,
            maxlength: 150,
        },

        
        "files[]": {
            required: document.imagesRequired,
            //extension: "jpg|jpeg|png",
            //filesize: 20971520,
            //totalFiles : 3,
        },
        

        name: {
            required: document.nameRequired,
            maxlength: 50,
        },
        phone: {
            required: document.phoneRequired,
            number: true,
            minlength: 10,
            maxlength: 10,
            regex:/^\d{10}$/
        },
        email: {
            required: document.emailRequired,
            email: true,
        },
        assets: {
          required: document.assetsRequired,
        },
        services: {
          required: document.servicesRequired,
        },

    },
    messages: {


        description: {
            required: translations.general_sentence.validation.This_field_is_required,
            maxlength: translations.general_sentence.validation.Please_enter_no_more_than_150_number,
        },
        
        "files[]": {
                required: translations.general_sentence.validation.This_field_is_required,
        },
        
        name: {
            required: translations.general_sentence.validation.This_field_is_required,
            maxlength: translations.general_sentence.validation.Please_enter_no_more_than_50_characters,
        },
        phone: {
            required: translations.general_sentence.validation.This_field_is_required,
            number:translations.general_sentence.validation.Please_enter_a_valid_number,
            regex:translations.general_sentence.validation.Please_enter_a_valid_number,
            minlength: translations.data_maintanance_request.validation.Please_enter_no_more_than_10_characters,
            maxlength: translations.data_maintanance_request.validation.Please_enter_no_more_than_10_characters,
        },
        email: {
            required: translations.general_sentence.validation.This_field_is_required,
            email: translations.general_sentence.validation.Email_format_not_Valid,
        },
        assets: {
            required: translations.general_sentence.validation.This_field_is_required,
        },
        services: {
            required: translations.general_sentence.validation.This_field_is_required,
        },


    },
    errorPlacement: function (error, element) {
        error.addClass("invalid-feedback");
        error.insertAfter(element);
    },
    highlight: function (element, errorClass, validClass) {
        $(element).addClass("is-invalid");
    },
    unhighlight: function (element, errorClass, validClass) {
        $(element).removeClass("is-invalid");
    },
    submitHandler: function (form) {
        
        $("#overlayer").css("display", "block");
        $(".loader-overlay").css("display", "block");
        const selected_files = [];
        $(".img-wrap").each(function(index, obj) {
          var imag_name = $(this).find(".FileNameCaptionStyle").text();
          //console.log(imag_name);
          var base64img = $(this).find(".thumb").attr("src");
          $('#files_selected'+index).val(base64img)
          //selected_files[index]['base64image'].push(base64img);
          ///selected_files[index]['name'].push(imag_name);
          //selected_files.push(base64img);
        });
        
        //console.log(selected_files)
        //separator = ',';
        //filesArray = selected_files.join(separator);
        //console.log(filesArray)
        //$('#files_selected').val(filesArray)
        form.submit();
    },
});


$("#another_place").validate({
    rules: {
        floor: {
            required: true,
        },
        room: {
            required: true,
        },


    },
    messages: {


        floor: {
            required: translations.general_sentence.validation.This_field_is_required,

        },
        room: {
            required: translations.general_sentence.validation.This_field_is_required,

        },


    },
    errorPlacement: function (error, element) {
        error.addClass("invalid-feedback");
        error.insertAfter(element);
    },
    highlight: function (element, errorClass, validClass) {
        $(element).addClass("is-invalid");
    },
    unhighlight: function (element, errorClass, validClass) {
        $(element).removeClass("is-invalid");
    },
    submitHandler: function (form) {
        //$("#overlayer").css("display", "block");
        //$(".loader-overlay").css("display", "block");
        //form.submit();
        console.log($('#floor').val());
        console.log($('#room').val());
        $('#floor_id').val($('#floor').val());
        $('#room_id').val($('#room').val());

        $('.floor_id').text($('#floor').val());
        $('.room_id').text($('#room').val());

        toastr.success(
                        translations.general_sentence.tostr_lang
                            .success,
                        translations.general_sentence.tostr_lang.success,
                        {
                            timeOut: 1000,
                        }
                    );

        $('#exampleModal').modal('toggle');


    },
});



$("#floor").on("change", function () {
    let value_cnt = $(this).val();
    let pro_id=$(this).data('pro');

    $.ajax({
        url: $(this).data("url"),
        method: "GET",
        data: {
            _token: $('meta[name="csrf-token"]').attr("content"),
            floor: value_cnt,
            pro_id: pro_id,
        },
        dataType: "json",
        beforeSend: function () {},
        success: function (data) {
            console.log(data);
            $("#room").empty();
            $("#room").append(
                $("<option></option>")
                    .attr("value", "")
                    .text(translations.work_order.forms.place_holder.Choose_Room)
            );
            $.each(data, function (key, value) {
                $("#room").append(
                    $("<option></option>")
                        .attr("value", value)
                        .text(value)
                );
            });
        },
        error: function (data) {
            var errors = $.parseJSON(data.responseText);
            toastr.error(data, translations.general_sentence.validation.Error, {
                timeOut: 1500,
                positionClass: "toast-top-center",
                progressBar: true,
                preventDuplicates: true,
                preventOpenDuplicates: true,
            });
        },
    });
});
$(".select2").select2({
   containerCssClass: "error"
});

</script>

@endsection
