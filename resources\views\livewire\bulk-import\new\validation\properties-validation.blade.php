<div>
    <div class = "userDatatable projectDatatable project-table w-100">
        <div class = "d-flex gap-10 pb-3 mb-3 justify-content-between">
            <div class = "d-flex gap-5 align-items-center">
                <div class = "d-flex gap-5 align-items-center">
                    <i class = "iconsax text-danger fs-16" icon-name = "info-circle"></i> 
                    <span class = "text-danger">{{ $errorsList->total() }} @lang('import.system_errors')</span>
                </div>
                <div class = "d-flex gap-5 align-items-center mx-2">
                    <i class = "iconsax text-success fs-16" icon-name = "clipboard-tick"></i> 
                    <span class = "text-success">{{ $propertiesList->total() }} @lang('import.inserted_rows')</span>
                </div>
            </div>
            <div>
                <button type = "button" class = "btn btn-danger mx-2" data-toggle = "modal" data-target = "#confirm-delete-properties">
                    @lang('import.delete_inserted_properties')
                </button>
            </div>
        </div>
    </div>
    <div class = "row">
        <div class = "col-sm-6 mb-3 mb-sm-0">
            <div class = "card">
                <div class = "card-body">
                    <h5 class = "card-title">@lang('import.inserted_rows')</h5>
                    <p class = "card-text">@lang('import.properties_inserted_text')</p>
                    <div class = "table-responsive" id = "properties-table">
                        <table class = "table mb-0">
                            <thead>
                                <tr>
                                    <th scope = "col">
                                        <span style = "font-size:11px">@lang("import.region")</span>
                                    </th>
                                    <th scope = "col">
                                        <span style = "font-size:11px">@lang("import.city")</span>
                                    </th>
                                    <th scope = "col">
                                        <span style = "font-size:11px">@lang("import.type2")</span>
                                    </th>
                                    <th scope = "col" class = "extra-col d-none">
                                        <span style = "font-size:11px">@lang("import.name2")</span>
                                    </th>
                                    <th scope = "col" class = "extra-col d-none">
                                        <span style = "font-size:11px">@lang("import.count")</span>
                                    </th>
                                    <th scope = "col" class = "extra-col d-none">
                                        <span style = "font-size:11px">@lang("import.gps_latitude")</span>
                                    </th>
                                    <th scope = "col" class = "extra-col d-none">
                                        <span style = "font-size:11px">@lang("import.gps_longtitude")</span>
                                    </th>
                                     <th>
                                        <span id = "show-action-btn4" class = "mx-1" style = "text-decoration: underline; cursor: pointer;">
                                            @lang('import.more')
                                        </span>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                @if(isset($propertiesList) && $propertiesList->count())
                                    @foreach($propertiesList as $key => $data)
                                        <tr wire:key = "properties-{{ $key }}">
                                            <td>
                                                <p style = "font-size:11px">
                                                    @if($selectedLanguage == 'en')
                                                        {{ $data->region->name ?? '-' }}
                                                    @else
                                                        {{ $data->region->name_ar ?? '-' }}
                                                    @endif
                                                </p>
                                            </td>
                                            <td>
                                                <p style = "font-size:11px">
                                                    @if($selectedLanguage == 'en')
                                                        {{ $data->city->name_en ?? '-' }}
                                                    @else
                                                        {{ $data->city->name_ar ?? '-' }}
                                                    @endif
                                                </p>
                                            </td>
                                            <td>
                                                <p style = "font-size:11px" class = "text-capitalize">
                                                    @switch(strtolower($data->property_type))
                                                        @case('complex')
                                                            @lang('import.complex')
                                                        @break

                                                        @case('building')
                                                            @lang('import.building')
                                                        @break

                                                        @default
                                                            -
                                                        @break
                                                    @endswitch
                                                </p>
                                            </td>
                                            <td class = "extra-row d-none">
                                                <p style = "font-size:11px">{{ $data->property_tag ?? '-'  }}</p>
                                            </td>
                                            <td class = "extra-row d-none">
                                                <p style = "font-size:11px">{{ $data->buildings_count ?? '-'  }}</p>
                                            </td>
                                            <td class = "extra-row d-none">
                                                <p style = "font-size:11px">{{ $data->latitude ?? '-'  }}</p>
                                            </td>
                                            <td class = "extra-row d-none">
                                                <p style = "font-size:11px">{{ $data->longitude ?? '-'  }}</p>
                                            </td>
                                        </tr>
                                    @endforeach

                                    @if ($propertiesList->hasMorePages())
                                        <tr>
                                            <td colspan = "8">
                                                <div class = "d-flex justify-content-center gap-2">
                                                    <div class = "p-2">
                                                        <div wire:loading wire:target = "managePerPageList">
                                                            <button type = "button" class = "btn btn-primary" wire:loading.attr = "disabled">
                                                                <span class = "spinner-border spinner-border-sm" role = "status" aria-hidden = "true"></span>
                                                                @lang('import.loading3')
                                                            </button>
                                                        </div>
                                                        <button type = "button" class = "btn btn-primary text-center" wire:target = "managePerPageList" wire:click = "managePerPageList" wire:loading.class = "hide">
                                                            @lang('import.load_more')
                                                        </button>
                                                    </div>
                                                    <div class = "p-2">
                                                        <div wire:loading wire:target = "manageLoadAllList">
                                                            <button type = "button" class = "btn btn-info" wire:loading.attr = "disabled">
                                                                <span class = "spinner-border spinner-border-sm" role = "status" aria-hidden = "true"></span>
                                                                @lang('import.loading3')
                                                            </button>
                                                        </div>
                                                        <button type = "button" class = "btn btn-info" wire:target = "manageLoadAllList" wire:click = "manageLoadAllList({{ $propertiesList->total() }})" wire:loading.class = "hide">
                                                            @lang('import.load_all')
                                                        </button>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    @endif
                                @else
                                    <tr class = "text-center">
                                        <td colspan = "8">@lang("import.empty_properties")</td>
                                    </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class = "col-sm-6 mb-3 mb-sm-0">
            <div class = "card">
                <div class = "card-body">
                    <h5 class = "card-title">@lang('import.errors_list')</h5>
                    <p class = "card-text">@lang('import.users_errors_text')</p>
                    <div class = "table-responsive">
                        <table class = "table mb-0">
                            <thead>
                                <tr>
                                    <th scope = "col">
                                        <span style = "font-size:11px">@lang("import.identifier")</span>
                                    </th>
                                    <th scope = "col">
                                        <span style = "font-size:11px">@lang("import.value")</span>
                                    </th>
                                    <th scope = "col">
                                        <span style = "font-size:11px">@lang("import.error")</span>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                @if(isset($errorsList) && $errorsList->count())
                                    @foreach($errorsList as $key => $data)
                                        <tr wire:key = "errors-properties-{{ $key }}"> 
                                            <td>
                                                <p style = "font-size:11px">{{ $data->identifier ?? '-' }}</p>
                                            </td>
                                            <td>
                                                <p style = "font-size:11px">{{ $data->value ?? '-' }}</p>
                                            </td>
                                            <td>
                                                <p style = "font-size:11px">
                                                    @if(isset($data->errors))
                                                        @switch($data->errors->value)
                                                            @case(\App\Enums\ValidationBukImport::BuildingCountIssue->value)
                                                                @lang('validation_bulk_import_step.building_count_error')
                                                            @break

                                                            @case(\App\Enums\ValidationBukImport::PropertyNotSaved->value)
                                                                @lang('validation_bulk_import_step.property_not_saved')
                                                            @break

                                                            @case(\App\Enums\ValidationBukImport::PropertyNotUpdated->value)
                                                                @lang('validation_bulk_import_step.property_not_updated')
                                                            @break

                                                            @default
                                                                -
                                                            @break
                                                        @endswitch
                                                    @else
                                                        -
                                                    @endif
                                                </p>
                                            </td>
                                        </tr>
                                    @endforeach

                                    @if ($errorsList->hasMorePages())
                                        <tr>
                                            <td colspan = "3">
                                                <div class = "d-flex justify-content-center gap-2">
                                                    <div class = "p-2">
                                                        <div wire:loading wire:target = "managePerPage">
                                                            <button type = "button" class = "btn btn-primary" wire:loading.attr = "disabled">
                                                                <span class = "spinner-border spinner-border-sm" role = "status" aria-hidden = "true"></span>
                                                                @lang('import.loading3')
                                                            </button>
                                                        </div>
                                                        <button type = "button" class = "btn btn-primary text-center" wire:target = "managePerPage" wire:click = "managePerPage" wire:loading.class = "hide">
                                                            @lang('import.load_more')
                                                        </button>
                                                    </div>
                                                    <div class = "p-2">
                                                        <div wire:loading wire:target = "manageLoadAll">
                                                            <button type = "button" class = "btn btn-info" wire:loading.attr = "disabled">
                                                                <span class = "spinner-border spinner-border-sm" role = "status" aria-hidden = "true"></span>
                                                                @lang('import.loading3')
                                                            </button>
                                                        </div>
                                                        <button type = "button" class = "btn btn-info" wire:target = "manageLoadAll" wire:click = "manageLoadAll({{ $errorsList->total() }})" wire:loading.class = "hide">
                                                            @lang('import.load_all')
                                                        </button>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    @endif
                                @else
                                    <tr class = "text-center">
                                        <td colspan = "3">@lang("import.empty_errors")</td>
                                    </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class = "modal fade delete" id = "confirm-delete-properties" tabindex = "-1" role = "dialog" aria-labelledby = "deleteModalLabel" aria-hidden = "true" wire:ignore.self>
        <div class = "modal-dialog modal-sm modal-dialog-centered" role = "document">
            <div class = "modal-content radius-xl">
                <div class = "modal-body">
                    <div class = "text-center">
                        <h1 class = "text-loss mb-4">
                            <i class = "las la-exclamation-circle fs-60"></i>
                        </h1>
                        <h5 class = "mb-3">@lang('CRMProjects.common.are_you_sure')</h5>
                        <p>
                            @lang('import.question_delete_sheet')
                            @lang('CRMProjects.common.this_action_cannot_be_undone') 
                        </p>
                    </div>
                </div>
                <div class = "modal-footer justify-content-between border-0 gap-10">
                    <button type = "button" class = "btn bg-hold-light text-white flex-fill radius-xl" data-dismiss = "modal">@lang('import.cancel')</button>
                    <div wire:loading class = "text-center" wire:target = "destroyUProperitesList">
                        <button type = "button" class = "btn bg-loss flex-fill radius-xl text-white" wire:loading.attr = "disabled">
                            <span class = "spinner-border spinner-border-sm" role = "status" aria-hidden = "true"></span> 
                            @lang('work_order.common.loading')
                        </button>
                    </div>
                    <button class = "btn bg-loss flex-fill radius-xl text-white" wire:loading.class = "hide" wire:target = "destroyUProperitesList" wire:click = "destroyUProperitesList()">@lang('import.delete')</button>
                </div>
            </div>
        </div>
    </div>
</div>
