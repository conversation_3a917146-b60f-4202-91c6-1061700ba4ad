<?php
// phpinfo();
?>
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}"
    dir="{{ (app()->getLocale()=='ar' ? 'rtl' : 'ltr') }}">

<head>
    <meta charset="UTF-8">
    @if(App::getLocale()=='en')
    <title lang="en">Osool </title>
    @else (App::getLocale()=='ar')
    <title lang="ar">أصول </title>
    @endif
    <meta name="description" lang="en" content="Osool is your technical partner in following up on maintenance contracts and managing the operational system. Osool enables facilities and properties management teams to manage maintenance and property operations, link with operational service providers, follow up on maintenance contract work in one platform, and serve the final beneficiary of the property.">
    <meta name="description" lang="ar" content="أصول هو شريكك التقني في متابعة عقود الصيانة وإدارة المنظومة التشغيلية. يمكن أصول فرق إدارة المرافق والأملاك من إدارة عمليات الصيانة والعقار والربط مع مقدمي الخدمات التشغيلية ومتابعة أعمال عقود الصيانة في منصة واحدة وخدمة المستفيد النهائي من العقار.">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta property="og:image" content="{{ asset('img/img-share.jpg') }}">
    <meta property="og:image:secure_url" content="{{ asset('img/img-share.jpg') }}">
    <meta property="og:image:type" content="image/jpg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <link rel="shortcut icon" href="{{ asset('home/image/favicon/favicon-32x32.png') }}" type="image">
    <!-- Bootstrap, fonts & icons  -->
    <link rel="stylesheet" href="{{ asset('new_theme/css/new_font.css')}}">


    @if(App::getLocale()=='en')
    <link rel="stylesheet" href="{{ asset('home/css/bootstrap.css') }}">
    @else (App::getLocale()=='ar')
    <link rel="stylesheet" href="{{ asset('home/css/bootstrap-rtl.css') }}">
    @endif
    @include('layouts.partials._styles')
    @if(App::getLocale()=='en')
    <link rel="stylesheet" href="{{ asset('home/css/main.css') }}">
    @else (App::getLocale()=='ar')
    <link rel="stylesheet" href="{{ asset('home/css/main-rtl.css') }}">
    @endif
    <link rel="stylesheet" href="{{ asset('home/fonts/icon-font/css/style.css') }}">
    <link rel="stylesheet" href="{{ asset('home/fonts/typography-font/typo.css') }}">
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.4/css/all.css" integrity="sha384-DyZ88mC6Up2uqS4h/KRgHuoeGwBcD4Ng9SiP4dIRy0EXTlnuz47vAwmeGwVChigm" crossorigin="anonymous">
    <link href="https://fonts.googleapis.com/css2?family=Karla:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Gothic+A1:wght@400;500;700;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Work+Sans:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Rubik:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700;800;900&display=swap" rel="stylesheet">
    <!-- Plugin'stylesheets  -->
    <link rel="stylesheet" href="{{ asset('home/plugins/aos/aos.min.css') }}">
    <link rel="stylesheet" href="{{ asset('home/plugins/fancybox/jquery.fancybox.min.css') }}">
    <link rel="stylesheet" href="{{ asset('home/plugins/nice-select/nice-select.min.css') }}">
    <link rel="stylesheet" href="{{ asset('home/plugins/slick/slick.min.css') }}">
    <!-- Vendor stylesheets  -->
    <link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">

    <link rel="stylesheet" href="{{ asset('css/scss/new-style.css') }}">
    <style type="text/css">
        .page-wrapper {
            padding-top: 150px;
        }
        @media only screen and (max-width:576px{
            .page-wrapper {
                padding-top: 100px;
            }
        }

        .account-profile .pro_img_wrapper {
            position: relative;
            width: 60px;
            height: 60px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .service-item select,
        .service-item input {
            flex: 1;
        }

        .file-item {
            display: flex;
            align-items: center;
            padding: 15px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background-color: white;
            width: 250px;
        }

        .form-check-input:checked {
            background-color: #6366f1;
            border-color: #6366f1;
        }

        .form-check-input:checked {
            background-color: #6366f1;
            border-color: #6366f1;
        }

        .form-check-input {
            width: 1.5em;
            height: 1.5em;
            margin-top: 0.25em;
        }

        .form-check-label {
            padding-left: 0.5em;
        }
    </style>
</head>
@php
$lang_path=resource_path('lang/'.App::getLocale());
$translations=collect(File::allFiles($lang_path))->flatMap(function ($file)use($lang_path) {
return [
($translation = $file->getBasename('.php')) => trans($translation),
];
})->toJson();
@endphp
<script type="text/javascript">
    window.baseUrl = "{{URL::to('/')}}";
    window.current_locale = "{{App::getLocale()}}";
    window.translations = {
        !!$translations!!
    };
    //console.log(window.current_locale) ;
</script>

<body data-theme-mode-panel-active data-theme="light" class="ltr">
    <header class="site-header site-header--menu-right landing-1-menu site-header--absolute site-header--sticky bg-white shadow-sm">
        <div class="container-fluid">
            <nav class="navbar site-navbar">
                <!-- Brand Logo-->
                <div class="brand-logo">
                    <a href="{{ url('/') }}">
                        <!-- light version logo (logo must be black)-->
                        <img src="{{ asset('home/image/home/<USER>') }}" alt="" class="light-version-logo">
                        <!-- Dark version logo (logo must be White)-->
                        <img src="{{ asset('home/image/home/<USER>') }}" alt="" class="dark-version-logo">
                    </a>
                </div>
                <div class="menu-block-wrapper">
                    <div class="menu-overlay"></div>
                    <nav class="menu-block" id="append-menu-header">
                        <div class="mobile-menu-head">
                            <img src="{{ asset('home/image/home/<USER>') }}" alt="" class="">
                            <div class="go-back">
                                <i class="fa fa-angle-left"></i>
                            </div>
                            <div class="current-menu-title"></div>
                            <div class="mobile-menu-close">&times;</div>
                        </div>
                        <ul class="site-menu-main">
                            <li class="nav-item">
                                <a href="#menu1" class="nav-link-item"> {{__('landing_page.menu.wahts_osool')}}</a>
                            </li>
                            <li class="nav-item">
                                <a href="#menu2" class="nav-link-item">{{__('landing_page.menu.osool_advantage')}} </a>
                            </li>
                            <li class="nav-item">
                                <a href="#menu3" class="nav-link-item"> {{__('landing_page.menu.beneficiaries_of_osool')}}</a>
                            </li>
                            <li class="nav-item">
                                <a href="#menu4" class="nav-link-item"> {{__('landing_page.menu.contact_us')}}</a>
                            </li>
                            <li class="nav-item mt-sm-0 mt-3 mt-sm-0">
                                <span class="nav-link-item no-hover pr-0">
                                    <a href="{{ route('psp-registration.index') }}" class="btn btn-border-lb btn-hover-slide position-relative overflow-hidden"><span class="rounded d-block position-relative"> Sign Up</span></a>
                                </span>
                            </li>
                            <li class="nav-item mt-sm-0 mt-3 mt-sm-0">
                                <span class="nav-link-item no-hover pr-0">
                                    <a href="javascript:void(0);" class="btn btn-border-lb btn-hover-slide position-relative overflow-hidden" data-bs-toggle="modal" data-bs-target="#osool-popup"><span class="rounded d-block position-relative"> {{__('landing_page.menu.get_started')}}</span></a>
                                </span>
                            </li>
                            <li class="d-flex align-items-center">
                                <span class="nav-link-item no-hover">
                                    <a class="btn bg-db text-white focus-reset lan-btn" href="{{ url('login') }}">
                                        {{__('landing_page.menu.login')}}
                                    </a>
                                </span>
                            </li>
                            <li class="nav-item">
                                @if (App::getLocale()=='en')
                                <a href="{{route('changeLanguage',"ar")}}" class="nav-link-item lang-btn"><span class="rounded d-block">{{__('landing_page.menu.en_ar')}}</span></a>
                                @elseif (App::getLocale()=='ar')
                                <a href="{{route('changeLanguage',"en")}}" class="nav-link-item lang-btn"><span class="rounded d-block">{{__('landing_page.menu.en_ar')}}</span></a>
                                @endif

                            </li>
                            <li class="d-flex align-items-center">
                                <div class="user-dropdown-wrapper">
                                    <div class="user-dropdown-trigger">
                                        <i class="fas fa-user-circle"></i>
                                        <span class="welcome-text">Welcome Mohammed</span>
                                        <i class="fas fa-chevron-down"></i>
                                    </div>
                                    <div class="user-dropdown-menu">
                                        <a href="#" class="dropdown-item">
                                            <i class="fas fa-user-cog"></i>
                                            Profile Management
                                        </a>
                                        <a href="#" class="dropdown-item">
                                            <i class="fas fa-store"></i>
                                            Register as Vendor
                                        </a>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </nav>
                </div>

                <!-- mobile menu trigger -->
                <div class="mobile-menu-trigger">
                    <span></span>
                </div>
                <!--/.Mobile Menu Hamburger Ends-->
            </nav>
        </div>
    </header>
    <div class="page-wrapper container">
        <div class="page-title-wrap">
            <div class="page-title d-flex justify-content-between w-100">

                <div class="d-flex align-items-center px-3 justify-content-between w-100 ">
                    <div class="d-flex align-items-center gap-10">
                        <h4 class="text-capitalize fw-500 breadcrumb-title border-end pr-3">
                            Mohammed Alotaibi
                        </h4>
                        <p class="m-0 fw-600">AP00001</p>
                    </div>
                    <div class="d-flex align-items-center gap-10">
                        <p class="m-0 border-0 fw-600 fs-6 text-black">Status:</p>
                        <p class="m-0">
                        <p class="status-badge m-0 info-badge">info</p>
                        <p class="status-badge m-0 warning-badge ">warning</p>
                        <p class="status-badge m-0 error-badge ">error</p>
                        <p class="status-badge m-0 success-badge ">success</p>
                        <p class="status-badge m-0 update-badge ">update</p>
                        </p>
                    </div>

                </div>
            </div>
        </div>
        <div class="mb-30 mt-15">
            <div>
                <div class="edit-profile">
                    <div class="">
                        <div class="row">
                            <div class="col-lg-9 col-sm-10">
                                <div class="card">
                                    <div class="card-body">
                                    <form method="post" id="user_profile_form2" action="/update-account" enctype="multipart/form-data">
                                        <div class="account-profile border-bottom mb-25 py-25 pb-0">
                                            <h6> Company Information</h6>
                                        </div>
                                        <div class="account-profile border-bottom mb-25 py-25 pb-0 flex-column d-flex align-items-center">
                                            <div class="d-flex gap-10 justify-content-between align-items-center align-self-start">
                                                <div class="ap-img pro_img_wrapper">
                                                    <img class="ap-img__main rounded-circle wh-60" src="/images/feature-card-image7.png" alt="profile" />

                                                </div>
                                                <div>
                                                    <h6>Mohammed ziyad</h6>
                                                    <p class="m-0"><EMAIL></p>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-grid">
                                            <div class="form-group">
                                                <label>Business Name</label>
                                                <input type="text" class="form-control" value="OSOOL">
                                            </div>
                                            <div class="form-group">
                                                <label>Business Address</label>
                                                <input type="text" class="form-control" value="Riyadh, Saudi Arabia">
                                            </div>
                                            <div class="form-group mb-20 z-20 position-relative">
                                                <label for="phone">Phone Number</label>
                                                <div class="mb-3">
                                                    <input type="tel" id="phone" disabled class="form-control placeholder-gray phone-input" placeholder="54xxxxxxx">
                                                    <small class="text-danger d-none" id="error-msg">Invalid phone number</small>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label>Number of Employees</label>
                                                <input type="number" class="form-control" value="183">
                                            </div>
                                            <div class="form-group">
                                                <label>Business Type</label>
                                                <input type="text" class="form-control" value="business type">
                                            </div>
                                        </div>

                                        <div class="mb-40">
                                            <div class="border-bottom pb-15 mb-20">
                                                <h5 class="mt-4">Services</h5>
                                            </div>

                                            <div class="services-container" id="services-container">
                                                <div class="service-item row w-100">
                                                    <div class="form-group col-12 col-md-6 mb-20">
                                                        <label for="service_1">Service Category</label>
                                                        <input type="text" class="form-control" value="service category">
                                                    </div>
                                                    <div class="form-group col-12 col-md-6">
                                                        <label>Service Description</label>
                                                        <input type="text" class="form-control" value="service Description">
                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                        <div class="mb-40">
                                            <div class="border-bottom pb-15 mb-20">
                                                <h5 class="mt-4">Address</h5>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <label>Region</label>
                                                    <select class="form-control">
                                                        <option>Region</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-6">
                                                    <label>City</label>
                                                    <select class="form-control">
                                                        <option>City</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mb-40">
                                            <div class="border-bottom pb-15 mb-20">
                                                <h5 class="mt-4">Document Upload</h5>
                                            </div>
                                            <div class="form-group">
                                                <label>Certifications and Qualifications</label>
                                                <div id="certifications-container">
                                                    <div class="upload-container d-flex align-items-center gap-10 mt-10">
                                                        <div class="d-flex gap-10 ">
                                                            <!-- PDF File -->
                                                            <div class="file-item">
                                                                <div class="mr-3">
                                                    <img src="{{asset('img/svg/pdf-icon.svg')}}">
                                                                </div>
                                                                <div class="file-details">
                                                                    <div class="file-name">Certifications.pdf</div>
                                                                    <div class="file-size">28.23 kb</div>
                                                                    <a href="#" class="file-action mt-3">Download</a>
                                                                </div>
                                                            </div>

                                                            <!-- Image File -->
                                                            <div class="file-item">
                                                                <div class="">
                                                                    <img src="{{asset('img/svg/certificate-icon.svg')}}">

                                                                </div>
                                                                <div class="file-details">
                                                                    <div class="file-name">Qualifications.jpg</div>
                                                                    <div class="file-size">56.33 kb</div>
                                                                    <a href="#" class="file-action mt-3">View</a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="mb-40">
                                                <div class="border-bottom pb-15 mb-20">
                                                    <h5 class="mt-4">Client References</h5>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <label>Case Studies</label>
                                                        <div id="case-studies-container">
                                                            <div class="d-flex flex-column gap-10 ">
                                                                <!-- PDF File -->
                                                                <div class="file-item">
                                                                    <div class="mr-3">
                                                                        <img src="{{asset('img/svg/pdf-icon.svg')}}">

                                                                    </div>
                                                                    <div class="file-details">
                                                                        <div class="file-name">Certifications.pdf</div>
                                                                        <div class="file-size">28.23 kb</div>
                                                                        <a href="#" class="file-action mt-3">Download</a>
                                                                    </div>
                                                                </div>

                                                                <!-- Image File -->
                                                                <div class="file-item">
                                                                    <div class="">
                                                                       <img src="{{asset('img/svg/certificate-icon.svg')}}">
                                                                    </div>
                                                                    <div class="file-details">
                                                                        <div class="file-name">Qualifications.jpg</div>
                                                                        <div class="file-size">56.33 kb</div>
                                                                        <a href="#" class="file-action mt-3">View</a>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                    </div>
                                                    <div class="col-md-6">
                                                        <label>Success Stories</label>
                                                        <div id="success-stories-container">
                                                            <div class="upload-container d-flex align-items-center gap-10 mt-10">
                                                                <div class="d-flex flex-column gap-10 ">
                                                                    <!-- PDF File -->
                                                                    <div class="file-item">
                                                                        <div class="mr-3">
                                                                            <img src="{{asset('img/svg/certificate-icon.svg')}}">

                                                                        </div>
                                                                        <div class="file-details">
                                                                            <div class="file-name">Certifications.pdf</div>
                                                                            <div class="file-size">28.23 kb</div>
                                                                            <a href="#" class="file-action mt-3">Download</a>
                                                                        </div>
                                                                    </div>

                                                                    <!-- Image File -->
                                                                    <div class="file-item">
                                                                        <div class="">
                                                                           <img src="{{asset('img/svg/certificate-icon.svg')}}">

                                                                        </div>
                                                                        <div class="file-details">
                                                                            <div class="file-name">Qualifications.jpg</div>
                                                                            <div class="file-size">56.33 kb</div>
                                                                            <a href="#" class="file-action mt-3">View</a>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                    </div>
                                                </div>
                                            </div>
                                            <div class="mt-4 d-flex align-items-center justify-content-end gap-10">
                                                <button type="button" class="btn btn-primary">Back</button>
                                                <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#confirmActionModal">
                                                    Accept modal
                                                </button>

                                                <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#confirmationModal">
                                                    Reject modal
                                                </button>

                                            </div>
                                        </div>
                                    </form>
                                </div>

                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h6 class="fw-500 fs-13">Timeline</h6>
    </div>
    <div class="card-body overflow-x-auto pl-0 pr-3">
        <div class="timeline-box--3 timeline-vertical left-middle basic-timeline scroll-timeline">
            <ul class="timeline">
                <li class="timeline-inverted">
                    <span class="timeline-single__buble bg-success"></span>
                    <div class="timeline-single">
                        <div class="timeline-single__days">
                            <span>30 Oct 2024 - 15:59</span>
                        </div>
                        <div class="timeline-single__content">
                            <p>
                                Worker <strong>Worker 03</strong> has been <strong><i>automatically</i></strong> assigned to the work order by <strong>SPA-1</strong>
                            </p>
                        </div>
                    </div>
                    <!-- ends: .timelline-single -->
                </li>
                <li class="timeline-inverted">
                    <span class="timeline-single__buble bg-success"></span>
                    <div class="timeline-single">
                        <div class="timeline-single__days">
                            <span>30 Oct 2024 - 15:59</span>
                        </div>
                        <div class="timeline-single__content">
                            <p>
                                Worker <strong>Worker 02</strong> has been <strong><i>automatically</i></strong> assigned to the work order by <strong>SPA-1</strong>
                            </p>
                        </div>
                    </div>
                    <!-- ends: .timelline-single -->
                </li>
                <li class="timeline-inverted">
                    <span class="timeline-single__buble bg-success"></span>
                    <div class="timeline-single">
                        <div class="timeline-single__days">
                            <span>28 Oct 2024 - 16:04</span>
                        </div>
                        <div class="timeline-single__content">
                            <p>
                                Worker <strong>Worker 1</strong> has been <strong><i>automatically</i></strong> assigned to the work order by <strong>SPA-1</strong>
                            </p>
                        </div>
                    </div>
                    <!-- ends: .timelline-single -->
                </li>
                <li class="timeline-inverted">
                    <span class="timeline-single__buble bg-success"></span>
                    <div class="timeline-single">
                        <div class="timeline-single__days">
                            <span>28 Oct 2024 - 14:01</span>
                        </div>
                        <div class="timeline-single__content">
                            <p>
                                Worker <strong>Worker 1</strong> has been <strong><i>automatically</i></strong> assigned to the work order by <strong>SPA-1</strong>
                            </p>
                        </div>
                    </div>
                    <!-- ends: .timelline-single -->
                </li>
                <li class="timeline-inverted">
                    <span class="timeline-single__buble bg-success"></span>
                    <div class="timeline-single">
                        <div class="timeline-single__days">
                            <span>28 Oct 2024 - 14:01</span>
                        </div>
                        <div class="timeline-single__content">
                            <p>
                                Worker <strong>Worker 1</strong> has been <strong><i>automatically</i></strong> assigned to the work order by <strong>SPA-1</strong>
                            </p>
                        </div>
                    </div>
                    <!-- ends: .timelline-single -->
                </li>
                <li class="timeline-inverted">
                    <span class="timeline-single__buble bg-success"></span>
                    <div class="timeline-single">
                        <div class="timeline-single__days">
                            <span>28 Oct 2024 - 13:59</span>
                        </div>
                        <div class="timeline-single__content">
                            <p>
                                Worker <strong>Worker 1</strong> has been <strong><i>automatically</i></strong> assigned to the work order by <strong>SPA-1</strong>
                            </p>
                        </div>
                    </div>
                    <!-- ends: .timelline-single -->
                </li>
                <li class="timeline-inverted">
                    <span class="timeline-single__buble bg-primary"></span>
                    <div class="timeline-single">
                        <div class="timeline-single__days">
                            <span>31 Dec 2024 - 08:00</span>
                        </div>
                        <div class="timeline-single__content">
                            <p>Work Order Submitted by <strong>BMA-1</strong></p>
                        </div>
                    </div>
                    <!-- ends: .timelline-single -->
                </li>
            </ul>
        </div>
    </div>
</div>

<div class="card mt-3">
    <div class="card-body p-2">
    <div class="job-category-li rounded">
        <h6 data-toggle="tooltip" data-html="true" title="" data-placement="top" data-original-title="<div class='job-cat-tool'>HVAC</div>">HVAC</h6>
        <div class="progress-wrap my-1">
            <div class="progress" style="width: 100%;">
                <div class="progress-bar bg-primary" role="progressbar" style="width: 95%;" aria-valuenow="1" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
        </div>
        <div class="d-flex justify-content-between"><span class="percent">95%</span><span>1100 Work Order</span></div>
    </div>
    </div>
</div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- accept Modal -->
    <div class="modal fade" id="confirmActionModal" tabindex="-1" aria-labelledby="confirmActionModal" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content rounded-3">
                <div class="modal-header border-bottom pb-4">
                    <h5 class="modal-title d-flex align-items-center gap-2">
                        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M32.0008 16C31.9961 20.242 30.3088 24.3089 27.3093 27.3084C24.3097 30.308 20.2428 31.9952 16.0008 32C-5.17516 31.248 -5.16916 0.748 16.0008 0C20.2428 0.00476422 24.3097 1.692 27.3093 4.69155C30.3088 7.6911 31.9961 11.758 32.0008 16ZM18.0008 24C18.0008 23.4696 17.7901 22.9609 17.4151 22.5858C17.04 22.2107 16.5313 22 16.0008 22C15.4704 22 14.9617 22.2107 14.5866 22.5858C14.2116 22.9609 14.0008 23.4696 14.0008 24C14.0008 24.5304 14.2116 25.0391 14.5866 25.4142C14.9617 25.7893 15.4704 26 16.0008 26C16.5313 26 17.04 25.7893 17.4151 25.4142C17.7901 25.0391 18.0008 24.5304 18.0008 24ZM18.0008 8C18.0008 7.46957 17.7901 6.96086 17.4151 6.58579C17.04 6.21071 16.5313 6 16.0008 6C15.4704 6 14.9617 6.21071 14.5866 6.58579C14.2116 6.96086 14.0008 7.46957 14.0008 8V16C14.0008 16.5304 14.2116 17.0391 14.5866 17.4142C14.9617 17.7893 15.4704 18 16.0008 18C16.5313 18 17.04 17.7893 17.4151 17.4142C17.7901 17.0391 18.0008 16.5304 18.0008 16V8Z" fill="#FF2828" />
                        </svg>

                        Are you sure?
                    </h5>
                </div>
                <div class="modal-body">
                    <div class="mb-4 pb-4">
                        <p class="fw-medium mb-2">Note:</p>
                        <p class="text-muted mb-0">
                            Lorem ipsum dolor sit amet consectetur. Sem sit erat euismod in. Sit lectus nibh quam diam felis risus.
                        </p>
                    </div>

                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary">Submit</button>
                </div>
            </div>
        </div>
    </div>

    <!-- reject Modal -->

    <div class="modal fade" id="confirmationModal" tabindex="-1" aria-labelledby="confirmationModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content rounded-3">
                <div class="modal-header border-bottom pb-4">
                    <h5 class="modal-title d-flex align-items-center gap-2">
                        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M32.0008 16C31.9961 20.242 30.3088 24.3089 27.3093 27.3084C24.3097 30.308 20.2428 31.9952 16.0008 32C-5.17516 31.248 -5.16916 0.748 16.0008 0C20.2428 0.00476422 24.3097 1.692 27.3093 4.69155C30.3088 7.6911 31.9961 11.758 32.0008 16ZM18.0008 24C18.0008 23.4696 17.7901 22.9609 17.4151 22.5858C17.04 22.2107 16.5313 22 16.0008 22C15.4704 22 14.9617 22.2107 14.5866 22.5858C14.2116 22.9609 14.0008 23.4696 14.0008 24C14.0008 24.5304 14.2116 25.0391 14.5866 25.4142C14.9617 25.7893 15.4704 26 16.0008 26C16.5313 26 17.04 25.7893 17.4151 25.4142C17.7901 25.0391 18.0008 24.5304 18.0008 24ZM18.0008 8C18.0008 7.46957 17.7901 6.96086 17.4151 6.58579C17.04 6.21071 16.5313 6 16.0008 6C15.4704 6 14.9617 6.21071 14.5866 6.58579C14.2116 6.96086 14.0008 7.46957 14.0008 8V16C14.0008 16.5304 14.2116 17.0391 14.5866 17.4142C14.9617 17.7893 15.4704 18 16.0008 18C16.5313 18 17.04 17.7893 17.4151 17.4142C17.7901 17.0391 18.0008 16.5304 18.0008 16V8Z" fill="#FF2828" />
                        </svg>

                        Are you sure?
                    </h5>
                </div>
                <div class="modal-body">
                    <div class="mb-4 pb-4 border-bottom">
                        <p class="fw-medium mb-2">Note:</p>
                        <p class="text-muted mb-0">
                            Lorem ipsum dolor sit amet consectetur. Sem sit erat euismod in. Sit lectus nibh quam diam felis risus.
                        </p>
                    </div>

                    <div class="mb-4 pb-4 border-bottom">
                        <p class="fw-medium mb-3">Select Section:</p>
                        <div class="row g-3">
                            <div class="col-sm-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="companyInfo">
                                    <label class="form-check-label" for="companyInfo">
                                        Company Information
                                    </label>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="services" checked>
                                    <label class="form-check-label" for="services">
                                        Services
                                    </label>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="address">
                                    <label class="form-check-label" for="address">
                                        Address
                                    </label>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="document" checked>
                                    <label class="form-check-label" for="document">
                                        Document
                                    </label>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="clientRef">
                                    <label class="form-check-label" for="clientRef">
                                        Client References
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div>
                        <p class="fw-medium mb-2">Rejected Reason:</p>
                        <textarea class="form-control" rows="4" placeholder="Write a reason"></textarea>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary">Submit</button>
                </div>
            </div>
        </div>
    </div>


    <div class="modal fade" id="osool-popup" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-md">
            <div class="modal-content radius-20">
                <div class="success-div w-100 h-100 p-5 h-100 w-100 position-absolute bg-white radius-20 text-center">
                    <div class="d-flex h-100 align-items-center">
                        <div class="w-100">
                            <div class="col-md-6 m-auto">
                                <img src="{{ asset('home/image/home/<USER>') }}" class="m-auto mb-5">
                            </div>
                            <h2 class="mb-3">{{__('landing_page.popup.data_sent_success')}}</h2>
                            <p class="">{{__('landing_page.popup.we_wil_reach_soon')}}</p>
                            <div class="m-auto mt-5 mb-4">
                                <a class="w-100 btn bg-lb radius-10 py-4 text-white close_final_modal" data-bs-dismiss="modal" aria-label="Close">{{__('landing_page.popup.okay')}}</a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-header px-4">
                    <div>
                        <h5 class="modal-title c-l-b" id="exampleModalLabel">{{__('landing_page.menu.request_free_demo')}}</h5>
                        <p class="mb-0">{{__('landing_page.popup.to_get_a_free_demo')}}</p>
                    </div>
                    <button type="button" class="btn-close radius-10" data-bs-dismiss="modal" aria-label="Close"><i class="fas fa-times"></i></button>
                </div>
                <div class="modal-body site-form px-4">
                    <form id="contact_form" action="{{ route('contact.save') }}" method="post">
                        @csrf
                        <input type="hidden" id="form_name" value="top_form" />
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="name" class="mb-1">
                                        {{__('landing_page.menu.name')}}<small class="required">*</small>
                                    </label>
                                    <input type="text" name="name" placeholder="{{__('landing_page.menu.enter_your_name')}}" id="full_name" class="form-control radius-10">
                                </div>
                                <div class="form-group mb-3">
                                    <label for="email" class="mb-1">
                                        {{__('landing_page.popup.company_entity')}}
                                    </label>
                                    <input type="text" name="dept" placeholder="{{__('landing_page.popup.enter_name_of_company')}}" id="dept" class="form-control  radius-10">
                                </div>
                                <div class="form-group mb-3">
                                    <label for="email" class="mb-1">
                                        {{__('landing_page.popup.filed_of_company')}}
                                    </label>
                                    <input type="text" name="entry_area" placeholder="{{__('landing_page.popup.enter_company_field_of_work')}}" id="entry_area" class="form-control  radius-10">
                                </div>
                                <div class="form-group mb-3">
                                    <label for="email" class="mb-1">
                                        {{__('landing_page.menu.phone_number')}}<small class="required">*</small>
                                    </label>
                                    <input type="text" name="phone" placeholder="05xxxxxxxx" id="phone" class="form-control  radius-10">
                                </div>
                                <div class="form-group">
                                    <label for="name" class="mb-1">
                                        {{__('landing_page.menu.email')}}<small class="required">*</small>
                                    </label>
                                    <input type="text" name="email" placeholder="<EMAIL>" id="email" class="form-control  radius-10">
                                </div>
                            </div>

                            <div class="col-md-12 mt-3">
                                <div class="d-flex">
                                    <div class="form-group mt-2 me-2 radius-15">
                                        <label for="captcha" class="captcha2 d-flex align-items-center form-control radius-10">
                                            <button type="button" class="px-2 py-2 rounded-circle reload" id="reload2">
                                                &#x21bb;
                                            </button> &nbsp;&nbsp;&nbsp;


                                        </label>
                                    </div>

                                    <div class="form-group mt-2 flex-fill">
                                        <input id="captcha2" type="text" class="form-control radius-10" placeholder="{{__('landing_page.menu.enter_sum_of_numbers')}}" name="captcha">
                                        @error('captcha')
                                        <div class="alert alert-danger mt-1 mb-1">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="w-100">
                            <div class="m-auto mt-5 mb-4">
                                <!-- data-bs-toggle="modal" data-bs-target="#osool-popup" -->
                                <button type="submit" class="w-100 btn bg-lb radius-10 py-4 text-white btn-pop"> {{__('landing_page.menu.submit')}}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

<!--Empty list Design-->
<div class="container d-none">
    <div class="">
        <div class="row">
            <div class="col-lg-12">
                <div class="breadcrumb-main user-member justify-content-sm-between ">
                    <div class=" d-flex flex-wrap justify-content-center breadcrumb-main__wrapper">
                        <div
                            class="d-flex align-items-center user-member__title justify-content-center mr-sm-25 page-title__left">
                            <h4 class="text-capitalize fw-500 breadcrumb-title"><a href="{{ url()->previous() }}"><i
                                        class="las la-arrow-left"></i></a>
                                {{__('user_management_module.common.users_list')}}</h4>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="PropertyListEmpty">
                        <img src="{{asset('empty-icon/Tiredness_amico.svg')}}" class="fourth_img" alt="">
                        <h4 class="first_title">{{__('general_sentence.empty_ui.No_users_yet')}}</h4>
                        <h6 class="second_title">{{__('general_sentence.empty_ui.The_users_list_will_appear_here')}}
                        </h6>
                        <!--@if(Auth::user()->user_type=='osool_admin')
                                <div class="action-btn">
                                    <a href="{{ route('users.user_type_filter') }}" class="btn px-15 btn-primary third_button">
                                        <i
                                            class="las la-plus fs-16"></i>{{__('user_management_module.user_button.filter_user_type')}}</a>
                                </div>
                                @endif -->
                        <div class="action-btn mt-5">
                            <a href="{{ route('users.create.info') }}" class="btn bg-lb radius-10 py-4 text-white btn-pop">
                                <i
                                    class="las la-plus fs-16"></i>{{__('user_management_module.user_button.add_new_user')}}</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!--End Empty list Design-->




    <!-- CONFIRM DELETE Photo MODAL ENDS -->
    @include('layouts.partials._scripts')

    <script src="{{ asset('home/plugins/menu/menu.js') }}"></script>
    <script src="{{ asset('home/js/vendor.min.js') }}"></script>

    <!-- toggle header dropdown , general layout  -->
    <script>
        $(document).ready(function() {
            // Toggle dropdown menu
            $('.user-dropdown-trigger').on('click', function(e) {
                e.stopPropagation();
                $('.user-dropdown-menu').toggleClass('active');
            });

            // Close dropdown when clicking outside
            $(document).on('click', function(e) {
                if (!$(e.target).closest('.user-dropdown-wrapper').length) {
                    $('.user-dropdown-menu').removeClass('active');
                }
            });

            // Handle mobile menu integration
            $('.mobile-menu-trigger').on('click', function() {
                if (window.innerWidth <= 768) {
                    $('.user-dropdown-menu').removeClass('active');
                }
            });
        });
    </script>






</body>


</html>
