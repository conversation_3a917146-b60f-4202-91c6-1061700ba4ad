<?php

    /*
    |--------------------------------------------------------------------------
    | Data  module
    |--------------------------------------------------------------------------
    |
    | language translate for create, edit , list and delte
    | english verion file
    | update@sobhon
    |
     */

return [

    'common'=> [
        'no_building_wrong_qrcode' => 'Sorry, The Scanned QR Code Is Not Working',
        'contact_building_manager' => 'please contact your building manager',
        'to_review_your_request_details' => 'to review your request details',
        'click here' => 'click here',
        'are you at different place?' => 'are you at different place?',
        'You can only attach 3 files' => 'You can only attach 3 files',
        'Confirm' => 'Confirm',
        'Choose Space Unit' => 'Choose Space Unit',
        'group' => 'Group',
        'select_all' => 'Select All',
        'maintenance_req' => 'Maintenance Request',
        'Choose Floor' => 'Choose Zone',
        'Select a Different Place' => 'Select a Different Place',
        'we_are_here_for_you_facing_issue_in_this_unit_space' => 'We are here to help if you are facing any issue in this space unit',
        'need_help' => 'Need Help?',
        'you_are_at' => 'You are at',
        'feebback_thank_you' => 'Thanks for your feedback',
        'back_details' => 'Go to request page',
        'services' => 'Services',
        'assets' => 'Assets',
        'enable_service_asset' => 'Enable Services & Assets',
        'select_service' => 'Select a Service',
        'services_assets' => 'Services & Assets',
        'select_asset' => 'Select an Asset',
        'maintanance_request_submitted' => 'Your request #:maintanance_id has been sent successfully to the property manager. you will receive an email and SMS in few minutes',
        'sent_successfully' => 'Sent successfully!',
        'no_feedback' => '(The requestor hasn’t submitted a feedback yet)',

        'worst' => 'Bad',

        'not_good_at_all' => 'Poor',
        'its_ok' => 'Fair',
        'very_good' => 'Good',
        'excellent' => 'Excellent!!!',
        'how_satisfy_are_you'=>'How satisfied are you with your Maintenance exprience?',
        'maintanance_request_completed' => 'Request #:maintanance_id Is Completed',
        'Sorry, Your Maintenance Request was Rejected' => 'Sorry, Your Maintenance Request was Rejected',
        'Your request is rejected' => 'Your request is rejected',
        'maintanance_request_list'=>'Maintenance Requests List',
        'maintanance_request'=>'Requests',
        'search_by_property_name'=>'Search by Property Name',
        'confirm'=>'Confirmation',
        'maintanance_request_id'=>'Maintenance Request ID',
        'group_id'=>'Group ID',
        'name'=>'Name',
        'ph_name'=>'Full name',
        'building'=>'Building',
        'floor'=>'Zone',
        'space_unit'=>'Space Unit',
        'place'=>'Place',
        'phone'=>'Phone',
        'ph_phone'=>'05xxxxxxxx',
        'email'=>'Email',
        'ph_email'=>'<EMAIL>',
        'uploaded_pictures'=> 'Uploaded Pictures',
        'description'=>'Description',
        'feedback'=>'Feedback',
        'reason_of_rejection'=>'Reason of rejection',
        'status'=>'Status',
        'submited_date'=>'Submitted Date',
        'submited_from'=>'Submitted From',
        'maintenance_portal'=>'Maintenance Portal',
        'tenant_app'=>'Tenant App',
        'worker_app'=>'Worker App',
        'no_pictures_uploaded'=>'No pictures uploaded',
        'apartment_no'=>'Apartment No.',
        'actions'=>'Action',
        'No'=>'No',
        'No_results_found'=>'No results found',
        'Geocoder_failed_due_to'=>'Geocoder failed due to',
        'Dear' => 'Dear User',
        'mail_message' => 'Your request :maintanance_id has been sent successfully',
        'request_details' => ' Click here to view request details',
        'Request' => 'Request',
        'You can track your maintenance request details here' => 'You can track your maintenance request details here',
        'Workorder Status' => 'Workorder Status',
        'pending_description' => 'Waiting for the building manager to review your issue',
        'inprogress_description' => 'Your request is accepted and it\'s in progress',
        'completed_description' => 'Your request has been completed, Thank you',
        'rejected_description' => 'Building manager reject your request',
        'Workorder Location' => 'Workorder Location',
        'Issue Details' => 'Issue Details',
        'Issue Description' => 'Issue Description',
        'What do you need help with' => 'What do you need help with?',
        'contact_details' => 'Contact Details',
        'reject_description' => 'Why would you reject this maintenance request? Please provide a valid reason to inform the beneficiary.',
        'view_work_order'=>'View Work Order',
        'note'=>'Note',
        'select_requests_to_generate_wo'=>'Select requests to generate a work order or reject',
        'group_maintenance_requests'=>'Group Maintenance Requests',
        'unit_receival_requests'=>'Unit Receival Requests',
        'is_reschedule' =>'Is Reschedules?',
        'yes' =>'Yes',
        'no'=>'No',
        'maintenance_reschedules_request_details'=>'Maintenance Reschedules Request Details',
        'approve'=>'Approve',
        'reject'=>'Reject',
        'reschedule_reason'=>'Reschedule Reason',
        'enter_reason'=>'Enter Reason',
        'request_count'=>'Notes raised',
        'pending' => 'Pending',
        'approved' => 'Approved',
        'rejected' => 'Rejected',
        'date_res'=>'Scheduling date',
        'order_num'=>'order number',
        'reason'=>'reason',
        'date_creation'=>'Date of creation',
        'done'=>'Successfully completed'
    ],

    'buttons'=>[
        'submit'=>'Generate WorkOrder',
        'submit_rejection' => 'Submit Rejection',
        'cancel'=>'Cancel',
        'reject'=>'Reject',
        'send' => 'Send',
        'skip' => 'Skip',
        'add_feedback' => 'Add feedback',
    ],

    'property_button'=>[
        'add_new_property'=>'Add New Property',
        'save_and_next'=>'Save & Next',
        'back'=>'back',
        'add_new_room'=>'Add New Room',
        'save'=>'Save',
        'submit'=>'Submit',
        'cancel'=>'Cancel',
        'Add_new_asset'=>'Add New Asset',
        'add_another_property'=>'Add Another Property',
        'download_all_barcode'=>'Download All Barcodes',
        'submit_all_buildings'=>'Submit All Buildings',
        'Reset'=>'Reset'
    ],

    'bread_crumbs'=>[
        'maintanance_request'=>'Maintenance Request',
        'add_property'=>'Add Property',
        'edit_property'=>'Edit Property',
        'view_property'=>'View Property',
        'property_management'=>'Property Management',
        'create_new_property'=>'Create New Property',
        'create_property'=>'Create Property',
        'property_structer'=>'Property Structure',
        'property_location'=>'Property Location',
        'confirmation'=>'Confirmation',
        'building'=>'Building',
        'complex'=>'Complex',
        'property'=>'Property',
        'data'=>'Data',
        'pro_on'=>'Pro One',
        'information'=>'Information',
        'title'=>'Title',
        'details'=>'Details',
        'location'=>'Location',
        'assets'=>'Asset',
        'no_assets_created'=>'No assets created',
        'contracts'=>'Contracts',
        'no_contracts_created'=>'No contracts created',
        'has_been_created'=>'has been created',
        'Category'=>'Category',
        'Name'=>'Name',
        'Service_Provider'=>'Service Provider',
        'pending' => 'Pending',
        'rejected' => 'Rejected',
        'in_progress' => 'In progress',
        'completed' => 'Completed',
        'finished' => 'Finished',
    ],



    /**-------------------------forms fields--------------- */

    'property_forms' => [

        'label' => [
            'please_add_property_information'=>'Please Add Property Information',
            'project'=>'Project',
            'region'=>'Region',
            'description'=>'Description',
            'city'=>'City',
            'property_type' =>'Property Type',
            'please_add_property_structure' => 'Please Add Property Structure Details',
            'building_name'=>'Building Name',
            'beneficiary'=>'Beneficiary',
            'property_barcode'=>'Property barcode',
            'floor'=>'Zone',
            'room'=>'Room',
            'U'=>'Room Type',
            'upload_logo'=>'Upload Logo',
            'property_barcode_choose_one'=>'Property barcode (Choose one)',
            'would_you_like_to_upload_your_own_barcode_or_generate_a_new_one_from_osool ?'=>'Would you like to upload your own barcode or generate a new one from Osool ?',
            'upload_my_own_barcode'=>'Upload my own Barcode',
            'generate_using_osool'=>'Generate using Osool',
            'please_add_complex_details'=>'Please add complex details',
            'Asset_barcode_choose_one'=>'Asset barcode (Choose one)',
            'Asset_barcode'=>'Asset barcode',
            'Please_add_property_location'=>'Please Add Property Location',
            'property_information'=>'Property Information',
            'building_tag'=>'Building Tag',
            'number_of_rooms'=>'Number of Rooms',
            'no_of_building'=>'Number Of Building',
            'choose_a_building'=>'Choose a building',
            'Asset_Category'=>'Service Type',
            'Asset_Name'=>'Asset Name',
            'Asset_Number'=>'Asset Number'
        ],

        'place_holder' => [
            'project'=>"Project",
            'region'=>'Choose Region',
            'city'=>'Choose City',
            'building_name_fixed'=>'Building Name (B-)',
            'building_name'=>'Building Name',
            'beneficiary'=>'Tags Mode',
            'property_type'=>'Select Property Type',
            'floor'=>'Zone',
            'room_no'=>'Room No',
            'room'=>'Choose Room',
            'room_type'=>'Room Type',
            'image_drag_and_drop_an_image'=>'Drag and drop an image',
            'image_or'=>'or',
            'image_browse'=>'Browse',
            'image_to_change_a_file'=>'to choose a file',
            'Choose_Floor'=>'Choose Zone',
            'Choose_Category'=>'Choose Category',
            'Choose_name'=>'Choose Asset Name',
            'Name'=>'Name',
            'Symbol'=>'Symbol',
            'Choose_name'=>'Choose name'
        ],
    ],

    /**-----------------validation------------------------------- */

    'validation' => [
        'Please_enter_no_more_than_10_characters' => 'Please enter 10 numbers'
     ],


    ];
