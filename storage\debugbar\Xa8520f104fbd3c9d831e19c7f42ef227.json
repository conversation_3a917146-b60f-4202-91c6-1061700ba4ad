{"__meta": {"id": "Xa8520f104fbd3c9d831e19c7f42ef227", "datetime": "2025-07-31 12:10:41", "utime": 1753953041.846953, "method": "POST", "uri": "/livewire/message/project.task-board.list-view", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 1, "messages": [{"message": "[12:10:41] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1753953041.765416, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753953041.415013, "end": 1753953041.846968, "duration": 0.43195486068725586, "duration_str": "432ms", "measures": [{"label": "Booting", "start": 1753953041.415013, "relative_start": 0, "end": 1753953041.746396, "relative_end": 1753953041.746396, "duration": 0.33138298988342285, "duration_str": "331ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753953041.746408, "relative_start": 0.33139491081237793, "end": 1753953041.84697, "relative_end": 2.1457672119140625e-06, "duration": 0.10056209564208984, "duration_str": "101ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 37855472, "peak_usage_str": "36MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 7, "templates": [{"name": "livewire.project.task-board.list-view (\\resources\\views\\livewire\\project\\task-board\\list-view.blade.php)", "param_count": 46, "params": ["errors", "_instance", "records", "itemId", "priorities", "usersForAssign", "userAssigned", "milestonesList", "selectedmilestone", "prioritiesList", "selected<PERSON><PERSON><PERSON><PERSON>", "work_order_type", "viewType", "Bulk_assign_to", "selected_WorkOrder", "work_orders_list", "selected_work_order_data", "sortField", "sortByPriority", "sortDirection", "start_date", "assign_to", "end_date", "change_status", "users", "selectedTasks", "taskStages", "milestones", "id_task", "activeTab", "taskDetails", "taskEdit", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.project.bug-report.modals.Task-Initialise-WorkOrder (\\resources\\views\\livewire\\project\\bug-report\\modals\\Task-Initialise-WorkOrder.blade.php)", "param_count": 59, "params": ["__env", "app", "errors", "_instance", "records", "itemId", "priorities", "usersForAssign", "userAssigned", "milestonesList", "selectedmilestone", "prioritiesList", "selected<PERSON><PERSON><PERSON><PERSON>", "work_order_type", "viewType", "Bulk_assign_to", "selected_WorkOrder", "work_orders_list", "selected_work_order_data", "sortField", "sortByPriority", "sortDirection", "start_date", "assign_to", "end_date", "change_status", "users", "selectedTasks", "taskStages", "milestones", "id_task", "activeTab", "taskDetails", "taskEdit", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "__currentLoopData", "user", "loop", "__empty_1", "task", "color", "index", "taskData", "html", "componentId", "componentTag"], "type": "blade"}, {"name": "livewire.project.task-board.modals.show-untangible-task (\\resources\\views\\livewire\\project\\task-board\\modals\\show-untangible-task.blade.php)", "param_count": 59, "params": ["__env", "app", "errors", "_instance", "records", "itemId", "priorities", "usersForAssign", "userAssigned", "milestonesList", "selectedmilestone", "prioritiesList", "selected<PERSON><PERSON><PERSON><PERSON>", "work_order_type", "viewType", "Bulk_assign_to", "selected_WorkOrder", "work_orders_list", "selected_work_order_data", "sortField", "sortByPriority", "sortDirection", "start_date", "assign_to", "end_date", "change_status", "users", "selectedTasks", "taskStages", "milestones", "id_task", "activeTab", "taskDetails", "taskEdit", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "__currentLoopData", "user", "loop", "__empty_1", "task", "color", "index", "taskData", "html", "componentId", "componentTag"], "type": "blade"}, {"name": "livewire.project.task-board.modals.edit-untangible-task (\\resources\\views\\livewire\\project\\task-board\\modals\\edit-untangible-task.blade.php)", "param_count": 59, "params": ["__env", "app", "errors", "_instance", "records", "itemId", "priorities", "usersForAssign", "userAssigned", "milestonesList", "selectedmilestone", "prioritiesList", "selected<PERSON><PERSON><PERSON><PERSON>", "work_order_type", "viewType", "Bulk_assign_to", "selected_WorkOrder", "work_orders_list", "selected_work_order_data", "sortField", "sortByPriority", "sortDirection", "start_date", "assign_to", "end_date", "change_status", "users", "selectedTasks", "taskStages", "milestones", "id_task", "activeTab", "taskDetails", "taskEdit", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "__currentLoopData", "user", "loop", "__empty_1", "task", "color", "index", "taskData", "html", "componentId", "componentTag"], "type": "blade"}, {"name": "livewire.project.task-board.modals.edit-tangible-task (\\resources\\views\\livewire\\project\\task-board\\modals\\edit-tangible-task.blade.php)", "param_count": 59, "params": ["__env", "app", "errors", "_instance", "records", "itemId", "priorities", "usersForAssign", "userAssigned", "milestonesList", "selectedmilestone", "prioritiesList", "selected<PERSON><PERSON><PERSON><PERSON>", "work_order_type", "viewType", "Bulk_assign_to", "selected_WorkOrder", "work_orders_list", "selected_work_order_data", "sortField", "sortByPriority", "sortDirection", "start_date", "assign_to", "end_date", "change_status", "users", "selectedTasks", "taskStages", "milestones", "id_task", "activeTab", "taskDetails", "taskEdit", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "__currentLoopData", "user", "loop", "__empty_1", "task", "color", "index", "taskData", "html", "componentId", "componentTag"], "type": "blade"}, {"name": "livewire.project.task-board.modals.Bulk-Actions.assignUserToTaskBulk (\\resources\\views\\livewire\\project\\task-board\\modals\\Bulk-Actions\\assignUserToTaskBulk.blade.php)", "param_count": 59, "params": ["__env", "app", "errors", "_instance", "records", "itemId", "priorities", "usersForAssign", "userAssigned", "milestonesList", "selectedmilestone", "prioritiesList", "selected<PERSON><PERSON><PERSON><PERSON>", "work_order_type", "viewType", "Bulk_assign_to", "selected_WorkOrder", "work_orders_list", "selected_work_order_data", "sortField", "sortByPriority", "sortDirection", "start_date", "assign_to", "end_date", "change_status", "users", "selectedTasks", "taskStages", "milestones", "id_task", "activeTab", "taskDetails", "taskEdit", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "__currentLoopData", "user", "loop", "__empty_1", "task", "color", "index", "taskData", "html", "componentId", "componentTag"], "type": "blade"}, {"name": "livewire.project.task-board.modals.Bulk-Actions.updateTaskBulk (\\resources\\views\\livewire\\project\\task-board\\modals\\Bulk-Actions\\updateTaskBulk.blade.php)", "param_count": 59, "params": ["__env", "app", "errors", "_instance", "records", "itemId", "priorities", "usersForAssign", "userAssigned", "milestonesList", "selectedmilestone", "prioritiesList", "selected<PERSON><PERSON><PERSON><PERSON>", "work_order_type", "viewType", "Bulk_assign_to", "selected_WorkOrder", "work_orders_list", "selected_work_order_data", "sortField", "sortByPriority", "sortDirection", "start_date", "assign_to", "end_date", "change_status", "users", "selectedTasks", "taskStages", "milestones", "id_task", "activeTab", "taskDetails", "taskEdit", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "__currentLoopData", "user", "loop", "__empty_1", "task", "color", "index", "taskData", "html", "componentId", "componentTag"], "type": "blade"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00443, "accumulated_duration_str": "4.43ms", "statements": [{"sql": "select * from `users` where `id` = 7368 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Middleware\\CheckSuperLogin.php", "line": 23}], "duration": 0.00326, "duration_str": "3.26ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_dev_db2", "start_percent": 0, "width_percent": 73.589}, {"sql": "select * from `user_company` where `user_company`.`user_id` = 7368 and `user_company`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Services\\AkauntingService.php", "line": 148}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Middleware\\AkauntingCompanyMiddleware.php", "line": 30}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Middleware\\CheckSuperLogin.php", "line": 42}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.0005200000000000001, "duration_str": "520μs", "stmt_id": "\\app\\Services\\AkauntingService.php:148", "connection": "osool_dev_db2", "start_percent": 73.589, "width_percent": 11.738}, {"sql": "select * from `projects_details` where `projects_details`.`id` = 201 and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["201"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Providers\\AsideViewComposerServiceProvider.php", "line": 59}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 120}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 91}], "duration": 0.00065, "duration_str": "650μs", "stmt_id": "\\app\\Providers\\AsideViewComposerServiceProvider.php:59", "connection": "osool_dev_db2", "start_percent": 85.327, "width_percent": 14.673}]}, "models": {"data": {"App\\Models\\ProjectsDetails": 1, "App\\Models\\UserCompany": 1, "App\\Models\\User": 1}, "count": 3}, "livewire": {"data": {"project.task-board.list-view #QK9s8J7F4RfIdeNBsp4H": "array:5 [\n  \"data\" => array:31 [\n    \"records\" => array:9 [\n      \"items\" => array:10 [\n        0 => array:21 [\n          \"id\" => 1179\n          \"title\" => \"TEST TASK\"\n          \"priority\" => \"High\"\n          \"description\" => \"DESC\"\n          \"start_date\" => \"2025-06-29 23:00:00\"\n          \"due_date\" => \"2025-07-28 23:00:00\"\n          \"task_type\" => \"untangible\"\n          \"workorder_id\" => null\n          \"workorder_type\" => null\n          \"property_name\" => null\n          \"order\" => 0\n          \"assign_to\" => array:1 [\n            0 => array:3 [\n              \"id\" => 65\n              \"name\" => \"<PERSON><PERSON><PERSON>\"\n              \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n            ]\n          ]\n          \"project_name\" => \"dup 1\"\n          \"milestone_name\" => \"MILESTONE2\"\n          \"milestone_id\" => 563\n          \"stage_name\" => \"Done\"\n          \"created_at\" => \"2025-07-02T08:44:04.000000Z\"\n          \"updated_at\" => \"2025-07-30T11:57:18.000000Z\"\n          \"files\" => []\n          \"comments\" => []\n          \"subTasks\" => []\n        ]\n        1 => array:21 [\n          \"id\" => 1182\n          \"title\" => \"TEST123\"\n          \"priority\" => \"High\"\n          \"description\" => \"DESCRIPTION HERE\"\n          \"start_date\" => \"2025-07-06 08:37:43\"\n          \"due_date\" => \"2025-07-13 08:37:43\"\n          \"task_type\" => \"untangible\"\n          \"workorder_id\" => null\n          \"workorder_type\" => null\n          \"property_name\" => null\n          \"order\" => 0\n          \"assign_to\" => array:1 [\n            0 => array:3 [\n              \"id\" => 65\n              \"name\" => \"Khansaa Hasan\"\n              \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n            ]\n          ]\n          \"project_name\" => \"dup 1\"\n          \"milestone_name\" => \"TEST\"\n          \"milestone_id\" => 562\n          \"stage_name\" => \"Done\"\n          \"created_at\" => \"2025-07-06T08:37:43.000000Z\"\n          \"updated_at\" => \"2025-07-21T15:30:44.000000Z\"\n          \"files\" => []\n          \"comments\" => []\n          \"subTasks\" => []\n        ]\n        2 => array:21 [\n          \"id\" => 1602\n          \"title\" => \"TEST NOTIFICATION\"\n          \"priority\" => \"High\"\n          \"description\" => \"DESC\"\n          \"start_date\" => \"2025-07-19 23:00:00\"\n          \"due_date\" => \"2025-07-19 23:00:00\"\n          \"task_type\" => \"untangible\"\n          \"workorder_id\" => null\n          \"workorder_type\" => null\n          \"property_name\" => null\n          \"order\" => 0\n          \"assign_to\" => array:1 [\n            0 => array:3 [\n              \"id\" => 65\n              \"name\" => \"Khansaa Hasan\"\n              \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n            ]\n          ]\n          \"project_name\" => \"dup 1\"\n          \"milestone_name\" => \"MILESTONE3\"\n          \"milestone_id\" => 577\n          \"stage_name\" => \"Done\"\n          \"created_at\" => \"2025-07-15T10:07:26.000000Z\"\n          \"updated_at\" => \"2025-07-30T11:57:54.000000Z\"\n          \"files\" => []\n          \"comments\" => []\n          \"subTasks\" => []\n        ]\n        3 => array:21 [\n          \"id\" => 1608\n          \"title\" => \"Sed ullam voluptates\"\n          \"priority\" => \"High\"\n          \"description\" => \"test\"\n          \"start_date\" => \"2025-07-15 11:43:31\"\n          \"due_date\" => \"2025-07-15 11:43:31\"\n          \"task_type\" => \"untangible\"\n          \"workorder_id\" => null\n          \"workorder_type\" => null\n          \"property_name\" => null\n          \"order\" => 0\n          \"assign_to\" => array:1 [\n            0 => array:3 [\n              \"id\" => 65\n              \"name\" => \"Khansaa Hasan\"\n              \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n            ]\n          ]\n          \"project_name\" => \"dup 1\"\n          \"milestone_name\" => \"MS5\"\n          \"milestone_id\" => 579\n          \"stage_name\" => \"Done\"\n          \"created_at\" => \"2025-07-15T11:43:31.000000Z\"\n          \"updated_at\" => \"2025-07-15T11:44:59.000000Z\"\n          \"files\" => []\n          \"comments\" => []\n          \"subTasks\" => []\n        ]\n        4 => array:21 [\n          \"id\" => 1609\n          \"title\" => \"TSK4\"\n          \"priority\" => \"High\"\n          \"description\" => \"b\"\n          \"start_date\" => \"2025-07-15 11:45:58\"\n          \"due_date\" => \"2025-07-15 11:45:58\"\n          \"task_type\" => \"untangible\"\n          \"workorder_id\" => null\n          \"workorder_type\" => null\n          \"property_name\" => null\n          \"order\" => 0\n          \"assign_to\" => array:1 [\n            0 => array:3 [\n              \"id\" => 65\n              \"name\" => \"Khansaa Hasan\"\n              \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n            ]\n          ]\n          \"project_name\" => \"dup 1\"\n          \"milestone_name\" => \"MS5\"\n          \"milestone_id\" => 579\n          \"stage_name\" => \"Done\"\n          \"created_at\" => \"2025-07-15T11:45:58.000000Z\"\n          \"updated_at\" => \"2025-07-15T11:48:42.000000Z\"\n          \"files\" => []\n          \"comments\" => []\n          \"subTasks\" => []\n        ]\n        5 => array:21 [\n          \"id\" => 1611\n          \"title\" => \"TASK5\"\n          \"priority\" => \"High\"\n          \"description\" => \"ASD\"\n          \"start_date\" => \"2025-07-15 11:54:16\"\n          \"due_date\" => \"2025-07-15 11:54:16\"\n          \"task_type\" => \"untangible\"\n          \"workorder_id\" => null\n          \"workorder_type\" => null\n          \"property_name\" => null\n          \"order\" => 0\n          \"assign_to\" => array:1 [\n            0 => array:3 [\n              \"id\" => 65\n              \"name\" => \"Khansaa Hasan\"\n              \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n            ]\n          ]\n          \"project_name\" => \"dup 1\"\n          \"milestone_name\" => \"MS5\"\n          \"milestone_id\" => 579\n          \"stage_name\" => \"Done\"\n          \"created_at\" => \"2025-07-15T11:54:16.000000Z\"\n          \"updated_at\" => \"2025-07-15T11:55:07.000000Z\"\n          \"files\" => []\n          \"comments\" => []\n          \"subTasks\" => []\n        ]\n        6 => array:21 [\n          \"id\" => 1668\n          \"title\" => \"Task call22\"\n          \"priority\" => \"Medium\"\n          \"description\" => \"test 3\"\n          \"start_date\" => \"2025-08-29 21:00:00\"\n          \"due_date\" => \"2025-08-29 21:00:00\"\n          \"task_type\" => \"untangible\"\n          \"workorder_id\" => null\n          \"workorder_type\" => null\n          \"property_name\" => null\n          \"order\" => 0\n          \"assign_to\" => array:1 [\n            0 => array:3 [\n              \"id\" => 65\n              \"name\" => \"Khansaa Hasan\"\n              \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n            ]\n          ]\n          \"project_name\" => \"dup 1\"\n          \"milestone_name\" => \"MILESTONE3\"\n          \"milestone_id\" => 577\n          \"stage_name\" => \"Tangible WO\"\n          \"created_at\" => \"2025-07-23T12:45:03.000000Z\"\n          \"updated_at\" => \"2025-07-23T12:56:25.000000Z\"\n          \"files\" => []\n          \"comments\" => []\n          \"subTasks\" => []\n        ]\n        7 => array:21 [\n          \"id\" => 1671\n          \"title\" => \"drag task33\"\n          \"priority\" => \"Medium\"\n          \"description\" => \"effdd\"\n          \"start_date\" => \"2025-08-26 21:00:00\"\n          \"due_date\" => \"2025-08-26 21:00:00\"\n          \"task_type\" => \"untangible\"\n          \"workorder_id\" => null\n          \"workorder_type\" => null\n          \"property_name\" => null\n          \"order\" => 0\n          \"assign_to\" => array:1 [\n            0 => array:3 [\n              \"id\" => 65\n              \"name\" => \"Khansaa Hasan\"\n              \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n            ]\n          ]\n          \"project_name\" => \"dup 1\"\n          \"milestone_name\" => \"MILESTONE2\"\n          \"milestone_id\" => 563\n          \"stage_name\" => \"Tangible WO\"\n          \"created_at\" => \"2025-07-23T12:51:33.000000Z\"\n          \"updated_at\" => \"2025-07-23T12:56:15.000000Z\"\n          \"files\" => []\n          \"comments\" => []\n          \"subTasks\" => []\n        ]\n        8 => array:21 [\n          \"id\" => 1702\n          \"title\" => \"TESTING NOTIFICATIONS\"\n          \"priority\" => \"High\"\n          \"description\" => \"DESCRIPTION\"\n          \"start_date\" => \"2025-07-29 23:00:00\"\n          \"due_date\" => \"2025-07-29 23:00:00\"\n          \"task_type\" => \"untangible\"\n          \"workorder_id\" => null\n          \"workorder_type\" => null\n          \"property_name\" => null\n          \"order\" => 0\n          \"assign_to\" => array:1 [\n            0 => array:3 [\n              \"id\" => 65\n              \"name\" => \"Khansaa Hasan\"\n              \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n            ]\n          ]\n          \"project_name\" => \"dup 1\"\n          \"milestone_name\" => \"MS5\"\n          \"milestone_id\" => 579\n          \"stage_name\" => \"Done\"\n          \"created_at\" => \"2025-07-27T12:04:42.000000Z\"\n          \"updated_at\" => \"2025-07-30T11:57:35.000000Z\"\n          \"files\" => []\n          \"comments\" => []\n          \"subTasks\" => []\n        ]\n        9 => array:21 [\n          \"id\" => 1715\n          \"title\" => \"TTRREEE\"\n          \"priority\" => \"High\"\n          \"description\" => \"DESC\"\n          \"start_date\" => \"2025-07-31 09:10:02\"\n          \"due_date\" => \"2025-07-31 09:10:02\"\n          \"task_type\" => \"untangible\"\n          \"workorder_id\" => null\n          \"workorder_type\" => null\n          \"property_name\" => null\n          \"order\" => 0\n          \"assign_to\" => array:1 [\n            0 => array:3 [\n              \"id\" => 65\n              \"name\" => \"Khansaa Hasan\"\n              \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n            ]\n          ]\n          \"project_name\" => \"dup 1\"\n          \"milestone_name\" => \"MS5\"\n          \"milestone_id\" => 579\n          \"stage_name\" => \"Tangible WO\"\n          \"created_at\" => \"2025-07-31T09:10:02.000000Z\"\n          \"updated_at\" => \"2025-07-31T09:10:02.000000Z\"\n          \"files\" => []\n          \"comments\" => []\n          \"subTasks\" => []\n        ]\n      ]\n      \"next_page_url\" => null\n      \"prev_page_url\" => null\n      \"per_page\" => 10\n      \"total\" => 10\n      \"allUsers\" => array:1 [\n        0 => array:3 [\n          \"id\" => 65\n          \"name\" => \"Khansaa Hasan\"\n          \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n        ]\n      ]\n      \"allMilestones\" => array:5 [\n        0 => array:2 [\n          \"id\" => 562\n          \"title\" => \"TEST\"\n        ]\n        1 => array:2 [\n          \"id\" => 563\n          \"title\" => \"MILESTONE2\"\n        ]\n        2 => array:2 [\n          \"id\" => 577\n          \"title\" => \"MILESTONE3\"\n        ]\n        3 => array:2 [\n          \"id\" => 578\n          \"title\" => \"MS4\"\n        ]\n        4 => array:2 [\n          \"id\" => 579\n          \"title\" => \"MS5\"\n        ]\n      ]\n      \"priority\" => array:3 [\n        \"Low\" => \"Low\"\n        \"Medium\" => \"Medium\"\n        \"High\" => \"High\"\n      ]\n      \"taskStages\" => array:5 [\n        0 => array:9 [\n          \"id\" => 462\n          \"name\" => \"Tangible WO\"\n          \"color\" => \"#000000\"\n          \"complete\" => 0\n          \"workspace_id\" => 46\n          \"order\" => 0\n          \"created_by\" => 65\n          \"created_at\" => \"2025-05-28T11:56:32.000000Z\"\n          \"updated_at\" => \"2025-05-28T11:56:32.000000Z\"\n        ]\n        1 => array:9 [\n          \"id\" => 101\n          \"name\" => \"Todo\"\n          \"color\" => \"#77b6ea\"\n          \"complete\" => 0\n          \"workspace_id\" => 46\n          \"order\" => 1\n          \"created_by\" => 65\n          \"created_at\" => \"2025-01-22T13:42:17.000000Z\"\n          \"updated_at\" => \"2025-05-28T11:56:32.000000Z\"\n        ]\n        2 => array:9 [\n          \"id\" => 102\n          \"name\" => \"In Progress\"\n          \"color\" => \"#545454\"\n          \"complete\" => 0\n          \"workspace_id\" => 46\n          \"order\" => 2\n          \"created_by\" => 65\n          \"created_at\" => \"2025-01-22T13:42:17.000000Z\"\n          \"updated_at\" => \"2025-05-28T11:56:32.000000Z\"\n        ]\n        3 => array:9 [\n          \"id\" => 103\n          \"name\" => \"Review\"\n          \"color\" => \"#3cb8d9\"\n          \"complete\" => 0\n          \"workspace_id\" => 46\n          \"order\" => 3\n          \"created_by\" => 65\n          \"created_at\" => \"2025-01-22T13:42:17.000000Z\"\n          \"updated_at\" => \"2025-05-28T11:56:32.000000Z\"\n        ]\n        4 => array:9 [\n          \"id\" => 104\n          \"name\" => \"Done\"\n          \"color\" => \"#37b37e\"\n          \"complete\" => 1\n          \"workspace_id\" => 46\n          \"order\" => 4\n          \"created_by\" => 65\n          \"created_at\" => \"2025-01-22T13:42:17.000000Z\"\n          \"updated_at\" => \"2025-07-13T15:33:32.000000Z\"\n        ]\n      ]\n    ]\n    \"itemId\" => 635\n    \"priorities\" => array:3 [\n      \"Low\" => \"Low\"\n      \"Medium\" => \"Medium\"\n      \"High\" => \"High\"\n    ]\n    \"usersForAssign\" => array:1 [\n      0 => array:3 [\n        \"id\" => 65\n        \"name\" => \"Khansaa Hasan\"\n        \"email\" => \"<EMAIL>\"\n      ]\n    ]\n    \"userAssigned\" => \"\"\n    \"milestonesList\" => array:5 [\n      0 => array:2 [\n        \"id\" => 562\n        \"title\" => \"TEST\"\n      ]\n      1 => array:2 [\n        \"id\" => 563\n        \"title\" => \"MILESTONE2\"\n      ]\n      2 => array:2 [\n        \"id\" => 577\n        \"title\" => \"MILESTONE3\"\n      ]\n      3 => array:2 [\n        \"id\" => 578\n        \"title\" => \"MS4\"\n      ]\n      4 => array:2 [\n        \"id\" => 579\n        \"title\" => \"MS5\"\n      ]\n    ]\n    \"selectedmilestone\" => \"\"\n    \"prioritiesList\" => array:3 [\n      \"Low\" => \"Low\"\n      \"Medium\" => \"Medium\"\n      \"High\" => \"High\"\n    ]\n    \"selectedprioritie\" => \"\"\n    \"work_order_type\" => null\n    \"viewType\" => null\n    \"Bulk_assign_to\" => null\n    \"selected_WorkOrder\" => \"\"\n    \"work_orders_list\" => []\n    \"selected_work_order_data\" => null\n    \"sortField\" => \"\"\n    \"sortByPriority\" => \"\"\n    \"sortDirection\" => \"asc\"\n    \"start_date\" => \"\"\n    \"assign_to\" => \"\"\n    \"end_date\" => \"\"\n    \"change_status\" => \"Done\"\n    \"users\" => array:1 [\n      0 => array:3 [\n        \"id\" => 65\n        \"name\" => \"Khansaa Hasan\"\n        \"email\" => \"<EMAIL>\"\n      ]\n    ]\n    \"selectedTasks\" => array:1 [\n      0 => \"1715\"\n    ]\n    \"taskStages\" => array:5 [\n      0 => array:9 [\n        \"id\" => 462\n        \"name\" => \"Tangible WO\"\n        \"color\" => \"#000000\"\n        \"complete\" => 0\n        \"workspace_id\" => 46\n        \"order\" => 0\n        \"created_by\" => 65\n        \"created_at\" => \"2025-05-28T11:56:32.000000Z\"\n        \"updated_at\" => \"2025-05-28T11:56:32.000000Z\"\n      ]\n      1 => array:9 [\n        \"id\" => 101\n        \"name\" => \"Todo\"\n        \"color\" => \"#77b6ea\"\n        \"complete\" => 0\n        \"workspace_id\" => 46\n        \"order\" => 1\n        \"created_by\" => 65\n        \"created_at\" => \"2025-01-22T13:42:17.000000Z\"\n        \"updated_at\" => \"2025-05-28T11:56:32.000000Z\"\n      ]\n      2 => array:9 [\n        \"id\" => 102\n        \"name\" => \"In Progress\"\n        \"color\" => \"#545454\"\n        \"complete\" => 0\n        \"workspace_id\" => 46\n        \"order\" => 2\n        \"created_by\" => 65\n        \"created_at\" => \"2025-01-22T13:42:17.000000Z\"\n        \"updated_at\" => \"2025-05-28T11:56:32.000000Z\"\n      ]\n      3 => array:9 [\n        \"id\" => 103\n        \"name\" => \"Review\"\n        \"color\" => \"#3cb8d9\"\n        \"complete\" => 0\n        \"workspace_id\" => 46\n        \"order\" => 3\n        \"created_by\" => 65\n        \"created_at\" => \"2025-01-22T13:42:17.000000Z\"\n        \"updated_at\" => \"2025-05-28T11:56:32.000000Z\"\n      ]\n      4 => array:9 [\n        \"id\" => 104\n        \"name\" => \"Done\"\n        \"color\" => \"#37b37e\"\n        \"complete\" => 1\n        \"workspace_id\" => 46\n        \"order\" => 4\n        \"created_by\" => 65\n        \"created_at\" => \"2025-01-22T13:42:17.000000Z\"\n        \"updated_at\" => \"2025-07-13T15:33:32.000000Z\"\n      ]\n    ]\n    \"milestones\" => array:5 [\n      0 => array:2 [\n        \"id\" => 562\n        \"title\" => \"TEST\"\n      ]\n      1 => array:2 [\n        \"id\" => 563\n        \"title\" => \"MILESTONE2\"\n      ]\n      2 => array:2 [\n        \"id\" => 577\n        \"title\" => \"MILESTONE3\"\n      ]\n      3 => array:2 [\n        \"id\" => 578\n        \"title\" => \"MS4\"\n      ]\n      4 => array:2 [\n        \"id\" => 579\n        \"title\" => \"MS5\"\n      ]\n    ]\n    \"id_task\" => null\n    \"activeTab\" => \"comments\"\n    \"taskDetails\" => array:10 [\n      \"id_task\" => 0\n      \"title\" => \"\"\n      \"priority\" => \"\"\n      \"assign_to\" => []\n      \"milestone\" => \"\"\n      \"description\" => \"\"\n      \"start_date\" => \"\"\n      \"due_date\" => \"\"\n      \"comments\" => []\n      \"users\" => []\n    ]\n    \"taskEdit\" => array:13 [\n      \"title\" => \"\"\n      \"priority\" => \"\"\n      \"selected_priority\" => \"\"\n      \"assign_to\" => []\n      \"selected_assign_to\" => []\n      \"milestone\" => \"\"\n      \"selected_milestone\" => \"\"\n      \"description\" => \"\"\n      \"start_date\" => \"\"\n      \"due_date\" => \"\"\n      \"workorder_id\" => \"\"\n      \"workorder_type\" => \"\"\n      \"property_name\" => \"\"\n    ]\n    \"currentPage\" => array:1 [\n      \"fetchData\" => 1\n    ]\n  ]\n  \"name\" => \"project.task-board.list-view\"\n  \"view\" => \"livewire.project.task-board.list-view\"\n  \"component\" => \"App\\Http\\Livewire\\Project\\TaskBoard\\ListView\"\n  \"id\" => \"QK9s8J7F4RfIdeNBsp4H\"\n]"}, "count": 1}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "82Y3S2Ii8XAEUfJqJwoJcxv4amqMyZ5Eyd3DAmvL", "captcha_answer": "32", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/_debugbar/open?id=X50e6a35dc4de0d3f080488da3882e7cc&op=get\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7368", "plain_user_password": "123456", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/livewire/message/project.task-board.list-view", "status_code": "<pre class=sf-dump id=sf-dump-2147441581 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2147441581\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-976555431 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-976555431\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1848064524 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">QK9s8J7F4RfIdeNBsp4H</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"28 characters\">project.task-board.list-view</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"233 characters\">CRMProjects/eyJpdiI6IkZKNlpmTFZEVGNFSlRNSTBwREFpOXc9PSIsInZhbHVlIjoiS2QyZGowVWE5bkxhampHUlRocC9tZz09IiwibWFjIjoiZmUxN2UwZjNhZWFmMTNkNTgyNDZlN2Q1Yzc4M2E1ZTNiYjVjOTZjODViYWNjNzJmZDY4ZmY5ZjM5NjU2YjQ3MyIsInRhZyI6IiJ9/task-board-list-view</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>l38340390-0</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">p2yfu39mw4UfYWyyn1sf</span>\"\n        \"<span class=sf-dump-key>tag</span>\" => \"<span class=sf-dump-str title=\"3 characters\">div</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>comments-0</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">YMfzDQnxmQfPMWV4Uccp</span>\"\n        \"<span class=sf-dump-key>tag</span>\" => \"<span class=sf-dump-str title=\"3 characters\">div</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>l38340390-1</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">gKrrm10kTNsceN4Xzoqd</span>\"\n        \"<span class=sf-dump-key>tag</span>\" => \"<span class=sf-dump-str title=\"3 characters\">div</span>\"\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">82176ec5</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:31</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>records</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>items</span>\" => <span class=sf-dump-note>array:10</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:21</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1179</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"9 characters\">TEST TASK</span>\"\n            \"<span class=sf-dump-key>priority</span>\" => \"<span class=sf-dump-str title=\"4 characters\">High</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"4 characters\">DESC</span>\"\n            \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-29 23:00:00</span>\"\n            \"<span class=sf-dump-key>due_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-28 23:00:00</span>\"\n            \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">untangible</span>\"\n            \"<span class=sf-dump-key>workorder_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>workorder_type</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>property_name</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>assign_to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>65</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Khansaa Hasan</span>\"\n                \"<span class=sf-dump-key>avatar</span>\" => \"<span class=sf-dump-str title=\"62 characters\">https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png</span>\"\n              </samp>]\n            </samp>]\n            \"<span class=sf-dump-key>project_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">dup 1</span>\"\n            \"<span class=sf-dump-key>milestone_name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">MILESTONE2</span>\"\n            \"<span class=sf-dump-key>milestone_id</span>\" => <span class=sf-dump-num>563</span>\n            \"<span class=sf-dump-key>stage_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-02T08:44:04.000000Z</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-30T11:57:18.000000Z</span>\"\n            \"<span class=sf-dump-key>files</span>\" => []\n            \"<span class=sf-dump-key>comments</span>\" => []\n            \"<span class=sf-dump-key>subTasks</span>\" => []\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:21</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1182</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">TEST123</span>\"\n            \"<span class=sf-dump-key>priority</span>\" => \"<span class=sf-dump-str title=\"4 characters\">High</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"16 characters\">DESCRIPTION HERE</span>\"\n            \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-06 08:37:43</span>\"\n            \"<span class=sf-dump-key>due_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-13 08:37:43</span>\"\n            \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">untangible</span>\"\n            \"<span class=sf-dump-key>workorder_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>workorder_type</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>property_name</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>assign_to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>65</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Khansaa Hasan</span>\"\n                \"<span class=sf-dump-key>avatar</span>\" => \"<span class=sf-dump-str title=\"62 characters\">https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png</span>\"\n              </samp>]\n            </samp>]\n            \"<span class=sf-dump-key>project_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">dup 1</span>\"\n            \"<span class=sf-dump-key>milestone_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">TEST</span>\"\n            \"<span class=sf-dump-key>milestone_id</span>\" => <span class=sf-dump-num>562</span>\n            \"<span class=sf-dump-key>stage_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-06T08:37:43.000000Z</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-21T15:30:44.000000Z</span>\"\n            \"<span class=sf-dump-key>files</span>\" => []\n            \"<span class=sf-dump-key>comments</span>\" => []\n            \"<span class=sf-dump-key>subTasks</span>\" => []\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:21</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1602</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"17 characters\">TEST NOTIFICATION</span>\"\n            \"<span class=sf-dump-key>priority</span>\" => \"<span class=sf-dump-str title=\"4 characters\">High</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"4 characters\">DESC</span>\"\n            \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-19 23:00:00</span>\"\n            \"<span class=sf-dump-key>due_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-19 23:00:00</span>\"\n            \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">untangible</span>\"\n            \"<span class=sf-dump-key>workorder_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>workorder_type</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>property_name</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>assign_to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>65</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Khansaa Hasan</span>\"\n                \"<span class=sf-dump-key>avatar</span>\" => \"<span class=sf-dump-str title=\"62 characters\">https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png</span>\"\n              </samp>]\n            </samp>]\n            \"<span class=sf-dump-key>project_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">dup 1</span>\"\n            \"<span class=sf-dump-key>milestone_name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">MILESTONE3</span>\"\n            \"<span class=sf-dump-key>milestone_id</span>\" => <span class=sf-dump-num>577</span>\n            \"<span class=sf-dump-key>stage_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-15T10:07:26.000000Z</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-30T11:57:54.000000Z</span>\"\n            \"<span class=sf-dump-key>files</span>\" => []\n            \"<span class=sf-dump-key>comments</span>\" => []\n            \"<span class=sf-dump-key>subTasks</span>\" => []\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:21</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1608</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Sed ullam voluptates</span>\"\n            \"<span class=sf-dump-key>priority</span>\" => \"<span class=sf-dump-str title=\"4 characters\">High</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"4 characters\">test</span>\"\n            \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-15 11:43:31</span>\"\n            \"<span class=sf-dump-key>due_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-15 11:43:31</span>\"\n            \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">untangible</span>\"\n            \"<span class=sf-dump-key>workorder_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>workorder_type</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>property_name</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>assign_to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>65</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Khansaa Hasan</span>\"\n                \"<span class=sf-dump-key>avatar</span>\" => \"<span class=sf-dump-str title=\"62 characters\">https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png</span>\"\n              </samp>]\n            </samp>]\n            \"<span class=sf-dump-key>project_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">dup 1</span>\"\n            \"<span class=sf-dump-key>milestone_name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">MS5</span>\"\n            \"<span class=sf-dump-key>milestone_id</span>\" => <span class=sf-dump-num>579</span>\n            \"<span class=sf-dump-key>stage_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-15T11:43:31.000000Z</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-15T11:44:59.000000Z</span>\"\n            \"<span class=sf-dump-key>files</span>\" => []\n            \"<span class=sf-dump-key>comments</span>\" => []\n            \"<span class=sf-dump-key>subTasks</span>\" => []\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:21</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1609</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"4 characters\">TSK4</span>\"\n            \"<span class=sf-dump-key>priority</span>\" => \"<span class=sf-dump-str title=\"4 characters\">High</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str>b</span>\"\n            \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-15 11:45:58</span>\"\n            \"<span class=sf-dump-key>due_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-15 11:45:58</span>\"\n            \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">untangible</span>\"\n            \"<span class=sf-dump-key>workorder_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>workorder_type</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>property_name</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>assign_to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>65</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Khansaa Hasan</span>\"\n                \"<span class=sf-dump-key>avatar</span>\" => \"<span class=sf-dump-str title=\"62 characters\">https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png</span>\"\n              </samp>]\n            </samp>]\n            \"<span class=sf-dump-key>project_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">dup 1</span>\"\n            \"<span class=sf-dump-key>milestone_name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">MS5</span>\"\n            \"<span class=sf-dump-key>milestone_id</span>\" => <span class=sf-dump-num>579</span>\n            \"<span class=sf-dump-key>stage_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-15T11:45:58.000000Z</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-15T11:48:42.000000Z</span>\"\n            \"<span class=sf-dump-key>files</span>\" => []\n            \"<span class=sf-dump-key>comments</span>\" => []\n            \"<span class=sf-dump-key>subTasks</span>\" => []\n          </samp>]\n          <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:21</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1611</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"5 characters\">TASK5</span>\"\n            \"<span class=sf-dump-key>priority</span>\" => \"<span class=sf-dump-str title=\"4 characters\">High</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ASD</span>\"\n            \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-15 11:54:16</span>\"\n            \"<span class=sf-dump-key>due_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-15 11:54:16</span>\"\n            \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">untangible</span>\"\n            \"<span class=sf-dump-key>workorder_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>workorder_type</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>property_name</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>assign_to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>65</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Khansaa Hasan</span>\"\n                \"<span class=sf-dump-key>avatar</span>\" => \"<span class=sf-dump-str title=\"62 characters\">https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png</span>\"\n              </samp>]\n            </samp>]\n            \"<span class=sf-dump-key>project_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">dup 1</span>\"\n            \"<span class=sf-dump-key>milestone_name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">MS5</span>\"\n            \"<span class=sf-dump-key>milestone_id</span>\" => <span class=sf-dump-num>579</span>\n            \"<span class=sf-dump-key>stage_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-15T11:54:16.000000Z</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-15T11:55:07.000000Z</span>\"\n            \"<span class=sf-dump-key>files</span>\" => []\n            \"<span class=sf-dump-key>comments</span>\" => []\n            \"<span class=sf-dump-key>subTasks</span>\" => []\n          </samp>]\n          <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:21</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1668</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Task call22</span>\"\n            \"<span class=sf-dump-key>priority</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Medium</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"6 characters\">test 3</span>\"\n            \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-29 21:00:00</span>\"\n            \"<span class=sf-dump-key>due_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-29 21:00:00</span>\"\n            \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">untangible</span>\"\n            \"<span class=sf-dump-key>workorder_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>workorder_type</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>property_name</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>assign_to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>65</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Khansaa Hasan</span>\"\n                \"<span class=sf-dump-key>avatar</span>\" => \"<span class=sf-dump-str title=\"62 characters\">https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png</span>\"\n              </samp>]\n            </samp>]\n            \"<span class=sf-dump-key>project_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">dup 1</span>\"\n            \"<span class=sf-dump-key>milestone_name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">MILESTONE3</span>\"\n            \"<span class=sf-dump-key>milestone_id</span>\" => <span class=sf-dump-num>577</span>\n            \"<span class=sf-dump-key>stage_name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Tangible WO</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-23T12:45:03.000000Z</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-23T12:56:25.000000Z</span>\"\n            \"<span class=sf-dump-key>files</span>\" => []\n            \"<span class=sf-dump-key>comments</span>\" => []\n            \"<span class=sf-dump-key>subTasks</span>\" => []\n          </samp>]\n          <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:21</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1671</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"11 characters\">drag task33</span>\"\n            \"<span class=sf-dump-key>priority</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Medium</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"5 characters\">effdd</span>\"\n            \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-26 21:00:00</span>\"\n            \"<span class=sf-dump-key>due_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-26 21:00:00</span>\"\n            \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">untangible</span>\"\n            \"<span class=sf-dump-key>workorder_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>workorder_type</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>property_name</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>assign_to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>65</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Khansaa Hasan</span>\"\n                \"<span class=sf-dump-key>avatar</span>\" => \"<span class=sf-dump-str title=\"62 characters\">https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png</span>\"\n              </samp>]\n            </samp>]\n            \"<span class=sf-dump-key>project_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">dup 1</span>\"\n            \"<span class=sf-dump-key>milestone_name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">MILESTONE2</span>\"\n            \"<span class=sf-dump-key>milestone_id</span>\" => <span class=sf-dump-num>563</span>\n            \"<span class=sf-dump-key>stage_name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Tangible WO</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-23T12:51:33.000000Z</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-23T12:56:15.000000Z</span>\"\n            \"<span class=sf-dump-key>files</span>\" => []\n            \"<span class=sf-dump-key>comments</span>\" => []\n            \"<span class=sf-dump-key>subTasks</span>\" => []\n          </samp>]\n          <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:21</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1702</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"21 characters\">TESTING NOTIFICATIONS</span>\"\n            \"<span class=sf-dump-key>priority</span>\" => \"<span class=sf-dump-str title=\"4 characters\">High</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"11 characters\">DESCRIPTION</span>\"\n            \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-29 23:00:00</span>\"\n            \"<span class=sf-dump-key>due_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-29 23:00:00</span>\"\n            \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">untangible</span>\"\n            \"<span class=sf-dump-key>workorder_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>workorder_type</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>property_name</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>assign_to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>65</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Khansaa Hasan</span>\"\n                \"<span class=sf-dump-key>avatar</span>\" => \"<span class=sf-dump-str title=\"62 characters\">https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png</span>\"\n              </samp>]\n            </samp>]\n            \"<span class=sf-dump-key>project_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">dup 1</span>\"\n            \"<span class=sf-dump-key>milestone_name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">MS5</span>\"\n            \"<span class=sf-dump-key>milestone_id</span>\" => <span class=sf-dump-num>579</span>\n            \"<span class=sf-dump-key>stage_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-27T12:04:42.000000Z</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-30T11:57:35.000000Z</span>\"\n            \"<span class=sf-dump-key>files</span>\" => []\n            \"<span class=sf-dump-key>comments</span>\" => []\n            \"<span class=sf-dump-key>subTasks</span>\" => []\n          </samp>]\n          <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:21</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1715</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">TTRREEE</span>\"\n            \"<span class=sf-dump-key>priority</span>\" => \"<span class=sf-dump-str title=\"4 characters\">High</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"4 characters\">DESC</span>\"\n            \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-31 09:10:02</span>\"\n            \"<span class=sf-dump-key>due_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-31 09:10:02</span>\"\n            \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">untangible</span>\"\n            \"<span class=sf-dump-key>workorder_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>workorder_type</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>property_name</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>assign_to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>65</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Khansaa Hasan</span>\"\n                \"<span class=sf-dump-key>avatar</span>\" => \"<span class=sf-dump-str title=\"62 characters\">https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png</span>\"\n              </samp>]\n            </samp>]\n            \"<span class=sf-dump-key>project_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">dup 1</span>\"\n            \"<span class=sf-dump-key>milestone_name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">MS5</span>\"\n            \"<span class=sf-dump-key>milestone_id</span>\" => <span class=sf-dump-num>579</span>\n            \"<span class=sf-dump-key>stage_name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Tangible WO</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-31T09:10:02.000000Z</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-31T09:10:02.000000Z</span>\"\n            \"<span class=sf-dump-key>files</span>\" => []\n            \"<span class=sf-dump-key>comments</span>\" => []\n            \"<span class=sf-dump-key>subTasks</span>\" => []\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>next_page_url</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>prev_page_url</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>per_page</span>\" => <span class=sf-dump-num>10</span>\n        \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>10</span>\n        \"<span class=sf-dump-key>allUsers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>65</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Khansaa Hasan</span>\"\n            \"<span class=sf-dump-key>avatar</span>\" => \"<span class=sf-dump-str title=\"62 characters\">https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png</span>\"\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>allMilestones</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>562</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"4 characters\">TEST</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>563</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"10 characters\">MILESTONE2</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>577</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"10 characters\">MILESTONE3</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>578</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"3 characters\">MS4</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>579</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"3 characters\">MS5</span>\"\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>Low</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Low</span>\"\n          \"<span class=sf-dump-key>Medium</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Medium</span>\"\n          \"<span class=sf-dump-key>High</span>\" => \"<span class=sf-dump-str title=\"4 characters\">High</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>taskStages</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>462</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Tangible WO</span>\"\n            \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#000000</span>\"\n            \"<span class=sf-dump-key>complete</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>workspace_id</span>\" => <span class=sf-dump-num>46</span>\n            \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>65</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-05-28T11:56:32.000000Z</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-05-28T11:56:32.000000Z</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>101</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Todo</span>\"\n            \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#77b6ea</span>\"\n            \"<span class=sf-dump-key>complete</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>workspace_id</span>\" => <span class=sf-dump-num>46</span>\n            \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>65</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-01-22T13:42:17.000000Z</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-05-28T11:56:32.000000Z</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>102</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">In Progress</span>\"\n            \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#545454</span>\"\n            \"<span class=sf-dump-key>complete</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>workspace_id</span>\" => <span class=sf-dump-num>46</span>\n            \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>2</span>\n            \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>65</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-01-22T13:42:17.000000Z</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-05-28T11:56:32.000000Z</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>103</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Review</span>\"\n            \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#3cb8d9</span>\"\n            \"<span class=sf-dump-key>complete</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>workspace_id</span>\" => <span class=sf-dump-num>46</span>\n            \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>3</span>\n            \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>65</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-01-22T13:42:17.000000Z</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-05-28T11:56:32.000000Z</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>104</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n            \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#37b37e</span>\"\n            \"<span class=sf-dump-key>complete</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>workspace_id</span>\" => <span class=sf-dump-num>46</span>\n            \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>4</span>\n            \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>65</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-01-22T13:42:17.000000Z</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-13T15:33:32.000000Z</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>itemId</span>\" => <span class=sf-dump-num>635</span>\n      \"<span class=sf-dump-key>priorities</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>Low</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Low</span>\"\n        \"<span class=sf-dump-key>Medium</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Medium</span>\"\n        \"<span class=sf-dump-key>High</span>\" => \"<span class=sf-dump-str title=\"4 characters\">High</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>usersForAssign</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>65</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Khansaa Hasan</span>\"\n          \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"24 characters\"><EMAIL></span>\"\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>userAssigned</span>\" => \"\"\n      \"<span class=sf-dump-key>milestonesList</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>562</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"4 characters\">TEST</span>\"\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>563</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"10 characters\">MILESTONE2</span>\"\n        </samp>]\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>577</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"10 characters\">MILESTONE3</span>\"\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>578</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"3 characters\">MS4</span>\"\n        </samp>]\n        <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>579</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"3 characters\">MS5</span>\"\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>selectedmilestone</span>\" => \"\"\n      \"<span class=sf-dump-key>prioritiesList</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>Low</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Low</span>\"\n        \"<span class=sf-dump-key>Medium</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Medium</span>\"\n        \"<span class=sf-dump-key>High</span>\" => \"<span class=sf-dump-str title=\"4 characters\">High</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>selectedprioritie</span>\" => \"\"\n      \"<span class=sf-dump-key>work_order_type</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>viewType</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>Bulk_assign_to</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>selected_WorkOrder</span>\" => \"\"\n      \"<span class=sf-dump-key>work_orders_list</span>\" => []\n      \"<span class=sf-dump-key>selected_work_order_data</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>sortField</span>\" => \"\"\n      \"<span class=sf-dump-key>sortByPriority</span>\" => \"\"\n      \"<span class=sf-dump-key>sortDirection</span>\" => \"<span class=sf-dump-str title=\"3 characters\">asc</span>\"\n      \"<span class=sf-dump-key>start_date</span>\" => \"\"\n      \"<span class=sf-dump-key>assign_to</span>\" => \"\"\n      \"<span class=sf-dump-key>end_date</span>\" => \"\"\n      \"<span class=sf-dump-key>change_status</span>\" => \"\"\n      \"<span class=sf-dump-key>users</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>65</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Khansaa Hasan</span>\"\n          \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"24 characters\"><EMAIL></span>\"\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>selectedTasks</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1715</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>taskStages</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>462</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Tangible WO</span>\"\n          \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#000000</span>\"\n          \"<span class=sf-dump-key>complete</span>\" => <span class=sf-dump-num>0</span>\n          \"<span class=sf-dump-key>workspace_id</span>\" => <span class=sf-dump-num>46</span>\n          \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>\n          \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>65</span>\n          \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-05-28T11:56:32.000000Z</span>\"\n          \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-05-28T11:56:32.000000Z</span>\"\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>101</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Todo</span>\"\n          \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#77b6ea</span>\"\n          \"<span class=sf-dump-key>complete</span>\" => <span class=sf-dump-num>0</span>\n          \"<span class=sf-dump-key>workspace_id</span>\" => <span class=sf-dump-num>46</span>\n          \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>1</span>\n          \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>65</span>\n          \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-01-22T13:42:17.000000Z</span>\"\n          \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-05-28T11:56:32.000000Z</span>\"\n        </samp>]\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>102</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">In Progress</span>\"\n          \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#545454</span>\"\n          \"<span class=sf-dump-key>complete</span>\" => <span class=sf-dump-num>0</span>\n          \"<span class=sf-dump-key>workspace_id</span>\" => <span class=sf-dump-num>46</span>\n          \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>2</span>\n          \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>65</span>\n          \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-01-22T13:42:17.000000Z</span>\"\n          \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-05-28T11:56:32.000000Z</span>\"\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>103</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Review</span>\"\n          \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#3cb8d9</span>\"\n          \"<span class=sf-dump-key>complete</span>\" => <span class=sf-dump-num>0</span>\n          \"<span class=sf-dump-key>workspace_id</span>\" => <span class=sf-dump-num>46</span>\n          \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>3</span>\n          \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>65</span>\n          \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-01-22T13:42:17.000000Z</span>\"\n          \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-05-28T11:56:32.000000Z</span>\"\n        </samp>]\n        <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>104</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n          \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#37b37e</span>\"\n          \"<span class=sf-dump-key>complete</span>\" => <span class=sf-dump-num>1</span>\n          \"<span class=sf-dump-key>workspace_id</span>\" => <span class=sf-dump-num>46</span>\n          \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>4</span>\n          \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>65</span>\n          \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-01-22T13:42:17.000000Z</span>\"\n          \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-13T15:33:32.000000Z</span>\"\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>milestones</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>562</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"4 characters\">TEST</span>\"\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>563</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"10 characters\">MILESTONE2</span>\"\n        </samp>]\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>577</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"10 characters\">MILESTONE3</span>\"\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>578</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"3 characters\">MS4</span>\"\n        </samp>]\n        <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>579</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"3 characters\">MS5</span>\"\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>id_task</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>activeTab</span>\" => \"<span class=sf-dump-str title=\"8 characters\">comments</span>\"\n      \"<span class=sf-dump-key>taskDetails</span>\" => <span class=sf-dump-note>array:10</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id_task</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>title</span>\" => \"\"\n        \"<span class=sf-dump-key>priority</span>\" => \"\"\n        \"<span class=sf-dump-key>assign_to</span>\" => []\n        \"<span class=sf-dump-key>milestone</span>\" => \"\"\n        \"<span class=sf-dump-key>description</span>\" => \"\"\n        \"<span class=sf-dump-key>start_date</span>\" => \"\"\n        \"<span class=sf-dump-key>due_date</span>\" => \"\"\n        \"<span class=sf-dump-key>comments</span>\" => []\n        \"<span class=sf-dump-key>users</span>\" => []\n      </samp>]\n      \"<span class=sf-dump-key>taskEdit</span>\" => <span class=sf-dump-note>array:13</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>title</span>\" => \"\"\n        \"<span class=sf-dump-key>priority</span>\" => \"\"\n        \"<span class=sf-dump-key>selected_priority</span>\" => \"\"\n        \"<span class=sf-dump-key>assign_to</span>\" => []\n        \"<span class=sf-dump-key>selected_assign_to</span>\" => []\n        \"<span class=sf-dump-key>milestone</span>\" => \"\"\n        \"<span class=sf-dump-key>selected_milestone</span>\" => \"\"\n        \"<span class=sf-dump-key>description</span>\" => \"\"\n        \"<span class=sf-dump-key>start_date</span>\" => \"\"\n        \"<span class=sf-dump-key>due_date</span>\" => \"\"\n        \"<span class=sf-dump-key>workorder_id</span>\" => \"\"\n        \"<span class=sf-dump-key>workorder_type</span>\" => \"\"\n        \"<span class=sf-dump-key>property_name</span>\" => \"\"\n      </samp>]\n      \"<span class=sf-dump-key>currentPage</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>fetchData</span>\" => <span class=sf-dump-num>1</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => []\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">dcd49e0668cd519f38be370b75ce2ccb890b4aa0363a8b90153d3de8611798ab</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:18</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">syncInput</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">mmuu</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">change_status</span>\"\n        \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">npbb</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"4 characters\">$set</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">change_status</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">vyb</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"4 characters\">$set</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">change_status</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">gon3</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"4 characters\">$set</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">change_status</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">l0ft</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"4 characters\">$set</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">change_status</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">4g01</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"4 characters\">$set</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">change_status</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">c328</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"4 characters\">$set</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">change_status</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">ystb</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"4 characters\">$set</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">change_status</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">fdam</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"4 characters\">$set</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">change_status</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">jmfp</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"4 characters\">$set</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">change_status</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">hxon</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"4 characters\">$set</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">change_status</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"5 characters\">qwili</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"4 characters\">$set</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">change_status</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">bjtt</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"4 characters\">$set</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">change_status</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">7un2</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"4 characters\">$set</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">change_status</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">z2vs</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"4 characters\">$set</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">change_status</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">balq</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"4 characters\">$set</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">change_status</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">pu9k</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"4 characters\">$set</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">change_status</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">nkvs</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"4 characters\">$set</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">change_status</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1848064524\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-666247542 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">11971</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">82Y3S2Ii8XAEUfJqJwoJcxv4amqMyZ5Eyd3DAmvL</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"262 characters\">http://osool-b2g.test/CRMProjects/eyJpdiI6IkZKNlpmTFZEVGNFSlRNSTBwREFpOXc9PSIsInZhbHVlIjoiS2QyZGowVWE5bkxhampHUlRocC9tZz09IiwibWFjIjoiZmUxN2UwZjNhZWFmMTNkNTgyNDZlN2Q1Yzc4M2E1ZTNiYjVjOTZjODViYWNjNzJmZDY4ZmY5ZjM5NjU2YjQ3MyIsInRhZyI6IiJ9/task-board-list-view?page=1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6IjI0QUcwaG5BVWpNRi8zaXYrRG1LTkE9PSIsInZhbHVlIjoiZFQ5U0VsUHFvUXR1d2NMZDhVMndUZVFFZ2ozNlRIVWdTT2hZNDVqeCt1Qk1ZR1NYczF3bzJXbUZydXNQVmZvRlliTjNCVGJmVGRvcXR6TzRvWmFoQWZqV3E2ektFRGVkS0tSNUhBWUNkaVJIMjdpUm5HdE5wN3IxT1pGSHBnMHYiLCJtYWMiOiI5NzU3NTliMmQzNDAwOTIyODJmMjhmNzY5OTExZDRjMWI5M2VlNTdmNGFiYWRkNWUyYjFmZjhkZTFkNzFhYTliIiwidGFnIjoiIn0%3D; osool_session=21XEPhA9jSBmVwD5WwcRXZIgCl5QKPNGrhpNVY55</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-666247542\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1504128389 data-indent-pad=\"  \"><span class=sf-dump-note>array:43</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"5 characters\">11971</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"262 characters\">http://osool-b2g.test/CRMProjects/eyJpdiI6IkZKNlpmTFZEVGNFSlRNSTBwREFpOXc9PSIsInZhbHVlIjoiS2QyZGowVWE5bkxhampHUlRocC9tZz09IiwibWFjIjoiZmUxN2UwZjNhZWFmMTNkNTgyNDZlN2Q1Yzc4M2E1ZTNiYjVjOTZjODViYWNjNzJmZDY4ZmY5ZjM5NjU2YjQ3MyIsInRhZyI6IiJ9/task-board-list-view?page=1</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6IjI0QUcwaG5BVWpNRi8zaXYrRG1LTkE9PSIsInZhbHVlIjoiZFQ5U0VsUHFvUXR1d2NMZDhVMndUZVFFZ2ozNlRIVWdTT2hZNDVqeCt1Qk1ZR1NYczF3bzJXbUZydXNQVmZvRlliTjNCVGJmVGRvcXR6TzRvWmFoQWZqV3E2ektFRGVkS0tSNUhBWUNkaVJIMjdpUm5HdE5wN3IxT1pGSHBnMHYiLCJtYWMiOiI5NzU3NTliMmQzNDAwOTIyODJmMjhmNzY5OTExZDRjMWI5M2VlNTdmNGFiYWRkNWUyYjFmZjhkZTFkNzFhYTliIiwidGFnIjoiIn0%3D; osool_session=21XEPhA9jSBmVwD5WwcRXZIgCl5QKPNGrhpNVY55</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">52271</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"46 characters\">/livewire/message/project.task-board.list-view</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"46 characters\">/livewire/message/project.task-board.list-view</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753953041.415</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753953041</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1504128389\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-748323680 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">82Y3S2Ii8XAEUfJqJwoJcxv4amqMyZ5Eyd3DAmvL</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-748323680\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-418616263 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 09:10:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImEwQ2xjNExXQVRXMzgvbTNRRmFsRFE9PSIsInZhbHVlIjoiSWlzWXZBNUVZWlA3SGJZR3AzaTNqQklyUXFSSjlVWEFxYk40aHlSMjZZNWZzU0NWZDI0bzlHbkRYYWtxVnVOa2hRVEsxQ3BzMGhDY0Uxa2RibENON1kyUWNEOTd5RENkSFhoMlp0ZGJ3SXJUZXlHUWg2eXRhcEZwSHJMZmVRLzciLCJtYWMiOiJlNTQxM2M4M2MwYTU1NzgyOTUxMDUwYjhhZDk2NGYzMmQ0NWZmNjgxODY4OWJiZTIxNjg0MDExMWJjZjYyZmVmIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 11:10:41 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6Ii95RkNlekplc3VaeUNPTlZlaTh3Rmc9PSIsInZhbHVlIjoiemd2ME5OVlZMb1dwN3VuMURTOExUNXNNZDJMb1ArNzFEWXZDSVdxQ20xOHgyL3JLOEkzdElXaEZoY2k2V2ZwRUQ4RnZKcld2QTlQWGFIaHVxcHlQRGlJMVNENXA5ZndpMXR6cDhiVEVjVlF6aDl0Mms2N3ZmUERiMUg1bTJ5RlQiLCJtYWMiOiI2YWVmNTM5Mzg0ODU3Y2IzMjE2M2JlMjA1ZmY2NTU3MmRjMDQzYmQxMDExNTlmODBiYjFiYjU0ZDJlZjk3ZWY3IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 11:10:41 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImEwQ2xjNExXQVRXMzgvbTNRRmFsRFE9PSIsInZhbHVlIjoiSWlzWXZBNUVZWlA3SGJZR3AzaTNqQklyUXFSSjlVWEFxYk40aHlSMjZZNWZzU0NWZDI0bzlHbkRYYWtxVnVOa2hRVEsxQ3BzMGhDY0Uxa2RibENON1kyUWNEOTd5RENkSFhoMlp0ZGJ3SXJUZXlHUWg2eXRhcEZwSHJMZmVRLzciLCJtYWMiOiJlNTQxM2M4M2MwYTU1NzgyOTUxMDUwYjhhZDk2NGYzMmQ0NWZmNjgxODY4OWJiZTIxNjg0MDExMWJjZjYyZmVmIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 11:10:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6Ii95RkNlekplc3VaeUNPTlZlaTh3Rmc9PSIsInZhbHVlIjoiemd2ME5OVlZMb1dwN3VuMURTOExUNXNNZDJMb1ArNzFEWXZDSVdxQ20xOHgyL3JLOEkzdElXaEZoY2k2V2ZwRUQ4RnZKcld2QTlQWGFIaHVxcHlQRGlJMVNENXA5ZndpMXR6cDhiVEVjVlF6aDl0Mms2N3ZmUERiMUg1bTJ5RlQiLCJtYWMiOiI2YWVmNTM5Mzg0ODU3Y2IzMjE2M2JlMjA1ZmY2NTU3MmRjMDQzYmQxMDExNTlmODBiYjFiYjU0ZDJlZjk3ZWY3IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 11:10:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-418616263\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-349286364 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">82Y3S2Ii8XAEUfJqJwoJcxv4amqMyZ5Eyd3DAmvL</span>\"\n  \"<span class=sf-dump-key>captcha_answer</span>\" => <span class=sf-dump-num>32</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"80 characters\">http://osool-b2g.test/_debugbar/open?id=X50e6a35dc4de0d3f080488da3882e7cc&amp;op=get</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7368</span>\n  \"<span class=sf-dump-key>plain_user_password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123456</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-349286364\", {\"maxDepth\":0})</script>\n"}}