@extends('layouts.app')
@section('styles')
@endsection
@section('content')
<div class="contents">
   <div class="container-fluid">
      <div class="row">
         <div class="col-lg-12">
            <div class="page-title-wrap">
                <div class="page-title d-flex justify-content-between">
                    <div class="page-title__left">
                        <div class="d-flex align-items-center user-member__title justify-content-center mr-sm-25">
                            <h4 class="text-capitalize fw-500 breadcrumb-title">
                        <a href="Javascript:history.back()"><i class="las la-arrow-left"></i></a> {{__('user_management_module.user_button.add_new_user')}}</h4>
                         </div>
                    </div>
                </div>
                <!-- {{ Breadcrumbs::render('add-user') }} -->
            </div>
         </div>
      </div>
   </div>
   <div class="container-fluid">
      <div class=" checkout wizard1 wizard7 global-shadow px-sm-50 px-20 py-sm-50 py-30 mb-30 bg-white radius-xl w-100">
         <div class="row justify-content-center">
            <div class="col-xl-8">
               <div class="checkout-progress-indicator content-center col-md-10">
                  <div class="checkout-progress">
                     <div class="step current" id="1">
                        <span>1</span>
                        <span>{{__('user_management_module.common.user_info')}}</span>
                     </div>
                     <div class="current"><img src="{{ asset('img/svg/checkout.svg') }}" alt="img" class="svg"></div>
                     <div class="step" id="2">
                        <span>2</span>
                        <span>{{__('user_management_module.common.user_role')}}</span>
                     </div>
                     <div class="current"><img src="{{ asset('img/svg/checkout.svg') }}" alt="img" class="svg"></div>

                     <div class="step" id="3">
                        <span>3</span>
                        <span>{{__('user_management_module.common.user_previleges')}}</span>
                     </div>

                     <div class="current"><img src="{{ asset('img/svg/checkout.svg') }}" alt="img" class="svg"></div>
                     <div class="step" id="4">
                        <span>4</span>
                        <span>{{__('user_management_module.common.password')}}</span>
                     </div>
                     <div class="current"><img src="{{ asset('img/svg/checkout.svg') }}" alt="img" class="svg"></div>
                     <div class="step" id="5">
                        <span>5</span>
                        <span>{{__('user_management_module.common.confirm')}}</span>
                     </div>
                  </div>
               </div>
               <!-- checkout -->
               <div class="row justify-content-center">
                  <div class="col-xl-7 col-lg-8 col-sm-10">
                     <div class="card checkout-shipping-form px-30 pt-2 pb-30 border-0">
                        <div class="card-header border-bottom-0 align-content-start pb-sm-0 pb-1 px-0">
                           <h4 class="fw-400">{{__('user_management_module.common.user_info')}}</h4>
                        </div>
                        <div class="card-body px-0 pb-0">
                             <div class="edit-profile__body">
                                <form method="post" id="user_create_supervisor_form" action="{{ route('users.create.role')}}" enctype="multipart/form-data">
                                @csrf
                                 <input type="hidden" id="user_type" name="user_type" value="sp_worker">
                                <div class="account-profile d-flex align-items-center mb-4 ">
                                    <div class="pro_img_wrapper">
                                       <input id="file_upload" type="file" name="profile_img" class="d-none" accept="image/*">
                                       <!-- Profile picture image-->
                                       <label for="file_upload">
                                       <img class="ap-img__main rounded-circle wh-120 bg-lighter d-flex" src="/img/upload.png" alt="profile" id="output_pic">
                                       <span class="cross" id="remove_pro_pic" >
                                       <span data-feather="camera" ></span>
                                       </span>
                                       </label>
                                       <span class="remove-img text-white btn-danger rounded-circle hide" data-toggle="modal" data-target="#confirmDeletePhoto">
                                         <span data-feather="x"></span>
                                       </span>
                                    </div>
                                    <div class="account-profile__title">
                                       <h6 class="fs-15 ml-20 fw-500 text-capitalize">{{__('user_management_module.user_forms.label.photo')}}</h6>
                                    </div>
                                 </div>
                                   <div class="form-group mb-20">
                                      <label for="worker_name">{{__('user_management_module.user_forms.label.worker_name')}} <small class="required">*</small></label>
                                      <input type="text" class="form-control" name="worker_name" id="worker_name" placeholder="Abdullah Omar">
                                   </div>
                                   
                                   <div class="form-group mb-20">
                                   <label for="worker_id">{{__('user_management_module.user_forms.label.worker_id')}} <i class="fas fa-question-circle" onfocus="theFocus(this);"  data-toggle="tooltip" data-placement="top" title="{{__('user_management_module.common.worker_id_description')}}"></i><small class="required">*</small></label>
                                      <input type="text" maxlength="10" class="form-control" name="worker_id" id="worker_id" placeholder="4466">
                                   </div>
                                 
                                   <div id="msg_error_workID"></div>

                                    <div class="form- group mb-20 user_info nationality_select">
                                       <div class="">
                                          <label for="nationality_id">
                                          {{__('user_management_module.user_forms.label.nationality')}} <small class="required">*</small>
                                          </label>
                                          <select class="form-control" id="nationality_id" name="nationality_id">
                                             <option value="" selected disabled>{{__('user_management_module.user_forms.label.choose_a_nationality')}}</option>

                                             @foreach($data['nationalities'] as $nationality)
                                                @if(App::getLocale()=='en')
                                                <option value="{{$nationality->id}}">{{$nationality->name_en}}</option>
                                                @else
                                                <option value="{{$nationality->id}}">{{$nationality->name_ar}}</option>
                                                @endif
                                             @endforeach

                                          </select>
                                       </div>
                                       <div id="nationality-id-error"></div>
                                    </div>

                                    <div class="form- group mb-20 user_info favorite_language_select">
                                       <div class="">
                                          <label for="favorite_language">
                                          {{__('user_management_module.user_forms.label.favorite_language')}} <small class="required">*</small>
                                          </label>
                                          <select class="form-control" id="favorite_language" name="favorite_language">
                                             <option value="" selected disabled>{{__('user_management_module.user_forms.label.choose_a_favorite_language')}}</option>
                                             <option value="en">{{__('user_management_module.user_forms.label.english')}}</option>
                                             <option value="ar">{{__('user_management_module.user_forms.label.arabic')}}</option>
                                             <option value="ur">{{__('user_management_module.user_forms.label.urdu')}}</option>

                                          </select>
                                       </div>
                                       <div id="favorite-language-error"></div>
                                    </div>

                                 <div class="form- group mb-20 user_info profession_select">
                                    <div class="">
                                       <label for="profession_id">
                                       {{__('user_management_module.user_forms.label.select_profession_heading')}} <small class="required">*</small>
                                       </label>
                                       <select class="form-control" id="profession_id" name="profession_id">
                                          <option value="" selected disabled>{{__('user_management_module.user_forms.label.choose_a_profession')}}</option>

                                          @foreach($data['workerProfessions'] as $profession)
                                             @if(App::getLocale()=='en')
                                             <option value="{{$profession->id}}">{{$profession->profession_en}}</option>
                                             @else
                                             <option value="{{$profession->id}}">{{$profession->profession_ar}}</option>
                                             @endif
                                          @endforeach

                                       </select>
                                    </div>
                                    <div id="profession-id-error"></div>
                                 </div>

                                   <div class="form-group mb-20 profession">
                                      <label for="worker_profession">{{__('user_management_module.user_forms.label.profession')}} <small class="required">*</small></label>
                                      <input type="text" class="form-control" name="profession" id="profession" placeholder="Electrician">
                                   </div>

                                   <div id="msg_error_profession"></div>

                                   <div class="form-group mb-20">
                                      <label for="worker_phone_number">{{__('user_management_module.user_forms.label.emp_phone')}}
                                       {{-- <small class="required">*</small> --}}
                                    </label>
                                      <div class="input-group mb-3 phone-ltr">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text" id="basic-addon1">+966</span>
                                        </div>
                                        <input type="tel" class="form-control" id="phone" name="phone" placeholder="576428964">
                                        </div>

                                   </div>
   
                                   <input type="hidden" id="ajax_check_userphone_unique" value="{{route('users.ajax_check_unique_usernumber')}}">
                                   <input type="hidden" id="ajax_check_employee_id_unique" value="{{route('users.ajax_check_unique_emp_id')}}">

                                   <div class="button-group d-flex pt-25 justify-content-end">
                                     <a href="{{ route('users.list') }}" class="btn btn-light btn-default btn-sm btn-squared fw-400 text-capitalize radius-md">{{__('user_management_module.user_button.cancel')}}</a>
                                      <button type="submit" class="btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2">{{__('user_management_module.user_button.save_next')}}
                                      </button>
                                   </div>
                                </form>
                             </div>
                        </div>
                     </div>
                     <!-- ends: card -->
                  </div>
                  <!-- ends: col -->
               </div>
            </div>
            <!-- ends: col -->
         </div>
      </div>
      <!-- End: .global-shadow-->
   </div>
</div>
<!-- CONFIRM DELETE Photo MODAL START -->

<div class="modal new-member  bouncein-new" id="confirmDeletePhoto" role="dialog" tabindex="-1"
        aria-labelledby="staticBackdropLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-md">
            <div class="modal-content  radius-xl  bouncein-new">

                <div class="modal-body">
                    <div class="create-asset-modal">
                        <h2 class="mb-3 fs-20"><i class="fa fa-exclamation-circle mr-1 text-warning"
                                aria-hidden="true"></i>
                            {{ __('data_properties.property_forms.label.sure_remove_photo') }} </h2>
                    </div>

                        <div class="button-group d-flex justify-content-end pt-25">
                            <div class="button-group d-flex justify-content-end pt-25">
                                <button type="button" class="btn btn-light   btn-squared text-capitalize"
                                    data-dismiss="modal" aria-label="Close">
                                    {{ __('data_properties.property_button.cancel') }}

                                </button>
                                <button type="button" class="btn btn-danger btn-default btn-squared text-capitalize confirm_remove_photo" data-dismiss="modal" aria-label="Close">
                                    {{ __('data_properties.property_button.remove') }}
                                </button>

                            </div>

                        </div>
                </div>
            </div>
        </div>
    </div>
</div>

    <!-- CONFIRM DELETE Photo MODAL ENDS -->
@endsection

@section('scripts')
<script type="text/javascript" src="{{asset('js/admin/users/create-supervisor.js')}}"></script>
<script type="text/javascript">
  
    sessionStorage.setItem('end_creation', 'false');
   $("#profession_id").select2({
     placeholder:translations.user_management_module.user_forms.label.choose_a_profession,
     dropdownCssClass: "tag",
     language: { noResults: () => translations.general_sentence.validation.No_results_found,}
   });
   $("#nationality_id").select2({
     placeholder:translations.user_management_module.user_forms.label.choose_a_nationality,
     dropdownCssClass: "tag",
     language: { noResults: () => translations.general_sentence.validation.No_results_found,}
   });
   $("#favorite_language").select2({
     placeholder:translations.user_management_module.user_forms.label.choose_a_favorite_language,
     dropdownCssClass: "tag",
     language: { noResults: () => translations.general_sentence.validation.No_results_found,}
   });
   $('#worker_name').keyup(function() {
      sessionStorage.setItem('createuser_name', JSON.stringify($(this).val()));
   });

   $('#worker_id').keyup(function() {
      sessionStorage.setItem('createuser_emp_id', JSON.stringify($(this).val()));
   });

   $('#profession').keyup(function() {
      sessionStorage.setItem('createuser_emp_dept', JSON.stringify($(this).val()));
   });

   $('#phone').keyup(function() {
      sessionStorage.setItem('createuser_phone', JSON.stringify($(this).val()));
   });


         var storedusernameValues = sessionStorage.getItem('createuser_name');
         if(storedusernameValues)
         {
            $("#worker_name").val(JSON.parse(storedusernameValues));
         }
         var storeduserempidValues = sessionStorage.getItem('createuser_emp_id');
         if(storeduserempidValues)
         {
            $("#worker_id").val(JSON.parse(storeduserempidValues));
         }
         var storeduserempdeptValues = sessionStorage.getItem('createuser_emp_dept');
         if(storeduserempdeptValues)
         {
            $("#profession").val(JSON.parse(storeduserempdeptValues));
         }
         var storeduserphoneValues = sessionStorage.getItem('createuser_phone');
         if(storeduserphoneValues)
         {
            $("#phone").val(JSON.parse(storeduserphoneValues));
         }


   $(document).ready(function() {
      $('#profession').attr('maxlength', 20); // Set the new max-length attribute
      //$('.profession_select').hide();
      $('.profession').hide();
      $('#profession').prop('required', false); // Make input not required
      
      $('#profession_id').on('change', function() {         
         var selectedOption = $(this).val();

         if (selectedOption === '10') {
               $('.profession').show();
               $('#profession').prop('required', true); // Make input required
               $('#profession').attr('maxlength', 15); // Set the new max-length attribute
         } else {
               $('.profession').hide();
               $('#profession').prop('required', false); // Make input not required
               $('#profession').attr('maxlength', 20); // Set the new max-length attribute
         }
      });
   });
</script>
@endsection
