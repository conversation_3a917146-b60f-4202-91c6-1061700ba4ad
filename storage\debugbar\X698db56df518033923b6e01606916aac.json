{"__meta": {"id": "X698db56df518033923b6e01606916aac", "datetime": "2025-07-31 12:02:22", "utime": 1753952542.86852, "method": "GET", "uri": "/dashboards/admin", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 10, "messages": [{"message": "[12:02:20] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1753952540.853889, "xdebug_link": null, "collector": "log"}, {"message": "[12:02:20] LOG.warning: Optional parameter $privilegeName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 644", "message_html": null, "is_string": false, "label": "warning", "time": 1753952540.88605, "xdebug_link": null, "collector": "log"}, {"message": "[12:02:20] LOG.warning: Optional parameter $privilegeSectionName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 644", "message_html": null, "is_string": false, "label": "warning", "time": 1753952540.886099, "xdebug_link": null, "collector": "log"}, {"message": "[12:02:20] LOG.warning: Optional parameter $shouldBeTrue declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 644", "message_html": null, "is_string": false, "label": "warning", "time": 1753952540.886138, "xdebug_link": null, "collector": "log"}, {"message": "[12:02:22] LOG.warning: Creation of dynamic property Illuminate\\Http\\Client\\Response::$cookies is deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php on line 810", "message_html": null, "is_string": false, "label": "warning", "time": 1753952542.799226, "xdebug_link": null, "collector": "log"}, {"message": "[12:02:22] LOG.warning: Creation of dynamic property Illuminate\\Http\\Client\\Response::$transferStats is deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php on line 812", "message_html": null, "is_string": false, "label": "warning", "time": 1753952542.799278, "xdebug_link": null, "collector": "log"}, {"message": "[12:02:22] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2416", "message_html": null, "is_string": false, "label": "warning", "time": 1753952542.809879, "xdebug_link": null, "collector": "log"}, {"message": "[12:02:22] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2476", "message_html": null, "is_string": false, "label": "warning", "time": 1753952542.809974, "xdebug_link": null, "collector": "log"}, {"message": "[12:02:22] LOG.warning: Optional parameter $search declared before required parameter $asset_id is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 3617", "message_html": null, "is_string": false, "label": "warning", "time": 1753952542.810526, "xdebug_link": null, "collector": "log"}, {"message": "[12:02:22] LOG.info: Dashboard User Type Checked ====>admin", "message_html": null, "is_string": false, "label": "info", "time": 1753952542.812592, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753952540.430391, "end": 1753952542.868551, "duration": 2.438159942626953, "duration_str": "2.44s", "measures": [{"label": "Booting", "start": 1753952540.430391, "relative_start": 0, "end": 1753952540.834121, "relative_end": 1753952540.834121, "duration": 0.4037299156188965, "duration_str": "404ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753952540.834135, "relative_start": 0.4037439823150635, "end": 1753952542.868553, "relative_end": 1.9073486328125e-06, "duration": 2.0344178676605225, "duration_str": "2.03s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 39570784, "peak_usage_str": "38MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET dashboards/admin", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\Admin\\DashboardControllerNew@index", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "", "where": [], "as": "admin.dashboard", "file": "<a href=\"phpstorm://open?file=C:\\laragon\\www\\Osool-B2G\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php&line=62\">\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:62-99</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.043559999999999995, "accumulated_duration_str": "43.56ms", "statements": [{"sql": "select * from `users` where `id` = 7368 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.004900000000000001, "duration_str": "4.9ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_dev_db2", "start_percent": 0, "width_percent": 11.249}, {"sql": "select * from `user_company` where `user_company`.`user_id` = 7368 and `user_company`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Services\\AkauntingService.php", "line": 148}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Middleware\\AkauntingCompanyMiddleware.php", "line": 30}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Middleware\\CheckSuperLogin.php", "line": 42}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00692, "duration_str": "6.92ms", "stmt_id": "\\app\\Services\\AkauntingService.php:148", "connection": "osool_dev_db2", "start_percent": 11.249, "width_percent": 15.886}, {"sql": "select * from `projects_details` where `projects_details`.`id` = 201 and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["201"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 1412}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 113}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 85}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.0020099999999999996, "duration_str": "2.01ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:1412", "connection": "osool_dev_db2", "start_percent": 27.135, "width_percent": 4.614}, {"sql": "update `users` set `allow_akaunting` = 1, `users`.`modified_at` = '2025-07-31 12:02:22' where `project_user_id` = 7368 and `user_type` not in ('osool_admin', 'super_admin') and `allow_akaunting` = 0 and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "2025-07-31 12:02:22", "7368", "osool_admin", "super_admin", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 1417}, {"index": 12, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 113}, {"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 85}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.01809, "duration_str": "18.09ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:1417", "connection": "osool_dev_db2", "start_percent": 31.749, "width_percent": 41.529}, {"sql": "select * from `users` where `users`.`id` = 7368 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 1421}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 113}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 85}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00087, "duration_str": "870μs", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:1421", "connection": "osool_dev_db2", "start_percent": 73.278, "width_percent": 1.997}, {"sql": "select exists(select * from `service_providers` where `id` = '1' and `global_sp` = 1 and `service_providers`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["1", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 1423}, {"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 113}, {"index": 12, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 85}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00799, "duration_str": "7.99ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:1423", "connection": "osool_dev_db2", "start_percent": 75.275, "width_percent": 18.343}, {"sql": "select count(*) as aggregate from `work_orders` where `project_user_id` = 7368", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 117}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 116}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php", "line": 85}], "duration": 0.00278, "duration_str": "2.78ms", "stmt_id": "\\app\\Http\\Controllers\\Admin\\DashboardControllerNew.php:117", "connection": "osool_dev_db2", "start_percent": 93.618, "width_percent": 6.382}]}, "models": {"data": {"App\\Models\\ProjectsDetails": 1, "App\\Models\\UserCompany": 1, "App\\Models\\User": 2}, "count": 4}, "livewire": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "82Y3S2Ii8XAEUfJqJwoJcxv4amqMyZ5Eyd3DAmvL", "captcha_answer": "32", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/dashboards/admin\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "array:1 [\n  \"X5d18ff307c2a78ec424cecfd65203439\" => null\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7368", "plain_user_password": "123456"}, "request": {"path_info": "/dashboards/admin", "status_code": "<pre class=sf-dump id=sf-dump-1297506610 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1297506610\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-22237562 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-22237562\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1446651915 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1446651915\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1076961470 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://osool-b2g.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6ImZGRm96c0l4Uzc1SjhldUFWT1ROeEE9PSIsInZhbHVlIjoiRzg4WSt4L2J6TXg3NGs4TlRNZGJQd29WanRVZ25JNTdvVndCSmttRURRckdWOGhxRTM1Q1cxTUZRZUhnWVd4dkdrSi93cXluMFZ4TnI5K1RQa2FVYUtCaW9IYlBSNUUyMndoQzdKNnVlRlBGTFY3cC9zNXFuQ2NMRjE5dG1IV0UiLCJtYWMiOiJiZDA2ZjdkMzdjMzdjNzJhMGIxNzk5YjY5MmNiNjJiMGM1ODQwNmQwNjhjM2RlMGUxYTMyYzE0OTQxMWQwZGRlIiwidGFnIjoiIn0%3D; osool_session=RGp6h3KKkkW2brhaAoBmgOqENB0Z1w31yed8G6GS</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1076961470\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1937263805 data-indent-pad=\"  \"><span class=sf-dump-note>array:40</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://osool-b2g.test/login</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6ImZGRm96c0l4Uzc1SjhldUFWT1ROeEE9PSIsInZhbHVlIjoiRzg4WSt4L2J6TXg3NGs4TlRNZGJQd29WanRVZ25JNTdvVndCSmttRURRckdWOGhxRTM1Q1cxTUZRZUhnWVd4dkdrSi93cXluMFZ4TnI5K1RQa2FVYUtCaW9IYlBSNUUyMndoQzdKNnVlRlBGTFY3cC9zNXFuQ2NMRjE5dG1IV0UiLCJtYWMiOiJiZDA2ZjdkMzdjMzdjNzJhMGIxNzk5YjY5MmNiNjJiMGM1ODQwNmQwNjhjM2RlMGUxYTMyYzE0OTQxMWQwZGRlIiwidGFnIjoiIn0%3D; osool_session=RGp6h3KKkkW2brhaAoBmgOqENB0Z1w31yed8G6GS</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">51631</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"17 characters\">/dashboards/admin</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"17 characters\">/dashboards/admin</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753952540.4304</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753952540</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1937263805\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1337413941 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">82Y3S2Ii8XAEUfJqJwoJcxv4amqMyZ5Eyd3DAmvL</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1337413941\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-34505117 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 09:02:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">http://osool-b2g.test/dashboards/blank-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ik9DQk9nTTNYVGJZbUhZbXVsVitPL1E9PSIsInZhbHVlIjoiL1NtcHVRY214KzNCNUN4MDJ4cDlreXI2QjVqUkxZOW1XR25yN1NLT29ubmZCUTBhSjk2WTlpMGlSZXVFTnJZVDJ6VlhiTVlsck0reGplUEkvTjZ5QzlEVUExSllXTTAvcnkyS0xLdVdGRUpQbHdrb1R3WjdPMHd4TW94U1Z6Q28iLCJtYWMiOiI0NDAzM2RkOTNmZTljNDRlNDk4MjQwMjliMGNiMGRkZjk3NzBmOGM1NWZiY2Y4OTU5Zjc1NzlkMzQyNThlZmU4IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 11:02:22 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6IlZYSGlzc2pIcDROcUlQQzVvS3I3OHc9PSIsInZhbHVlIjoiS1U0VHZLMUtKRThvMFZTblBFcUFCSzJod21mS3Fvd3VIMHpnSWpTOVUzNEFDeHRiR0VzbUsrYUlLSnV4SEdDazR4K29xTk5lbFgrdlMyY0g0TU1mbmRsK3I0S1NyUWxrNGl3R3VpZ2xhZ2FWRzJYNzQyY0VIN29rSTFrbmhJR04iLCJtYWMiOiI0MmI3OWE4YWE2YWZmMGRlODE2NDEyMzVhMjU4Mzg0MjEyNDU1MjAwMTViNGRlNTI5N2Q3NGMxNWU4NDBjMjZlIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 11:02:22 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ik9DQk9nTTNYVGJZbUhZbXVsVitPL1E9PSIsInZhbHVlIjoiL1NtcHVRY214KzNCNUN4MDJ4cDlreXI2QjVqUkxZOW1XR25yN1NLT29ubmZCUTBhSjk2WTlpMGlSZXVFTnJZVDJ6VlhiTVlsck0reGplUEkvTjZ5QzlEVUExSllXTTAvcnkyS0xLdVdGRUpQbHdrb1R3WjdPMHd4TW94U1Z6Q28iLCJtYWMiOiI0NDAzM2RkOTNmZTljNDRlNDk4MjQwMjliMGNiMGRkZjk3NzBmOGM1NWZiY2Y4OTU5Zjc1NzlkMzQyNThlZmU4IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 11:02:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6IlZYSGlzc2pIcDROcUlQQzVvS3I3OHc9PSIsInZhbHVlIjoiS1U0VHZLMUtKRThvMFZTblBFcUFCSzJod21mS3Fvd3VIMHpnSWpTOVUzNEFDeHRiR0VzbUsrYUlLSnV4SEdDazR4K29xTk5lbFgrdlMyY0g0TU1mbmRsK3I0S1NyUWxrNGl3R3VpZ2xhZ2FWRzJYNzQyY0VIN29rSTFrbmhJR04iLCJtYWMiOiI0MmI3OWE4YWE2YWZmMGRlODE2NDEyMzVhMjU4Mzg0MjEyNDU1MjAwMTViNGRlNTI5N2Q3NGMxNWU4NDBjMjZlIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 11:02:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-34505117\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1433212594 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">82Y3S2Ii8XAEUfJqJwoJcxv4amqMyZ5Eyd3DAmvL</span>\"\n  \"<span class=sf-dump-key>captcha_answer</span>\" => <span class=sf-dump-num>32</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://osool-b2g.test/dashboards/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>X5d18ff307c2a78ec424cecfd65203439</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7368</span>\n  \"<span class=sf-dump-key>plain_user_password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123456</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1433212594\", {\"maxDepth\":0})</script>\n"}}